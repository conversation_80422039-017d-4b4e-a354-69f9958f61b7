"use strict";(self.wpRiseJsonp=self.wpRiseJsonp||[]).push([["node_modules_pnpm_react_19_1_0_node_modules_react_index_js"],{30115:(T,r)=>{/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var a=Symbol.for("react.transitional.element"),N=Symbol.for("react.portal"),M=Symbol.for("react.fragment"),Y=Symbol.for("react.strict_mode"),k=Symbol.for("react.profiler"),I=Symbol.for("react.consumer"),L=Symbol.for("react.context"),U=Symbol.for("react.forward_ref"),b=Symbol.for("react.suspense"),D=Symbol.for("react.memo"),C=Symbol.for("react.lazy"),w=Symbol.iterator;function q(t){return t===null||typeof t!="object"?null:(t=w&&t[w]||t["@@iterator"],typeof t=="function"?t:null)}var A={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},S=Object.assign,h={};function l(t,e,n){this.props=t,this.context=e,this.refs=h,this.updater=n||A}l.prototype.isReactComponent={},l.prototype.setState=function(t,e){if(typeof t!="object"&&typeof t!="function"&&t!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,t,e,"setState")},l.prototype.forceUpdate=function(t){this.updater.enqueueForceUpdate(this,t,"forceUpdate")};function g(){}g.prototype=l.prototype;function y(t,e,n){this.props=t,this.context=e,this.refs=h,this.updater=n||A}var v=y.prototype=new g;v.constructor=y,S(v,l.prototype),v.isPureReactComponent=!0;var O=Array.isArray,i={H:null,A:null,T:null,S:null,V:null},j=Object.prototype.hasOwnProperty;function R(t,e,n,u,s,f){return n=f.ref,{$$typeof:a,type:t,key:e,ref:n!==void 0?n:null,props:f}}function z(t,e){return R(t.type,e,void 0,void 0,void 0,t.props)}function d(t){return typeof t=="object"&&t!==null&&t.$$typeof===a}function G(t){var e={"=":"=0",":":"=2"};return"$"+t.replace(/[=:]/g,function(n){return e[n]})}var H=/\/+/g;function m(t,e){return typeof t=="object"&&t!==null&&t.key!=null?G(""+t.key):e.toString(36)}function P(){}function K(t){switch(t.status){case"fulfilled":return t.value;case"rejected":throw t.reason;default:switch(typeof t.status=="string"?t.then(P,P):(t.status="pending",t.then(function(e){t.status==="pending"&&(t.status="fulfilled",t.value=e)},function(e){t.status==="pending"&&(t.status="rejected",t.reason=e)})),t.status){case"fulfilled":return t.value;case"rejected":throw t.reason}}throw t}function p(t,e,n,u,s){var f=typeof t;(f==="undefined"||f==="boolean")&&(t=null);var o=!1;if(t===null)o=!0;else switch(f){case"bigint":case"string":case"number":o=!0;break;case"object":switch(t.$$typeof){case a:case N:o=!0;break;case C:return o=t._init,p(o(t._payload),e,n,u,s)}}if(o)return s=s(t),o=u===""?"."+m(t,0):u,O(s)?(n="",o!=null&&(n=o.replace(H,"$&/")+"/"),p(s,e,n,"",function(W){return W})):s!=null&&(d(s)&&(s=z(s,n+(s.key==null||t&&t.key===s.key?"":(""+s.key).replace(H,"$&/")+"/")+o)),e.push(s)),1;o=0;var _=u===""?".":u+":";if(O(t))for(var c=0;c<t.length;c++)u=t[c],f=_+m(u,c),o+=p(u,e,n,f,s);else if(c=q(t),typeof c=="function")for(t=c.call(t),c=0;!(u=t.next()).done;)u=u.value,f=_+m(u,c++),o+=p(u,e,n,f,s);else if(f==="object"){if(typeof t.then=="function")return p(K(t),e,n,u,s);throw e=String(t),Error("Objects are not valid as a React child (found: "+(e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e)+"). If you meant to render a collection of children, use an array instead.")}return o}function E(t,e,n){if(t==null)return t;var u=[],s=0;return p(t,u,"","",function(f){return e.call(n,f,s++)}),u}function B(t){if(t._status===-1){var e=t._result;e=e(),e.then(function(n){(t._status===0||t._status===-1)&&(t._status=1,t._result=n)},function(n){(t._status===0||t._status===-1)&&(t._status=2,t._result=n)}),t._status===-1&&(t._status=0,t._result=e)}if(t._status===1)return t._result.default;throw t._result}var $=typeof reportError=="function"?reportError:function(t){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var e=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof t=="object"&&t!==null&&typeof t.message=="string"?String(t.message):String(t),error:t});if(!window.dispatchEvent(e))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",t);return}console.error(t)};function J(){}r.Children={map:E,forEach:function(t,e,n){E(t,function(){e.apply(this,arguments)},n)},count:function(t){var e=0;return E(t,function(){e++}),e},toArray:function(t){return E(t,function(e){return e})||[]},only:function(t){if(!d(t))throw Error("React.Children.only expected to receive a single React element child.");return t}},r.Component=l,r.Fragment=M,r.Profiler=k,r.PureComponent=y,r.StrictMode=Y,r.Suspense=b,r.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=i,r.__COMPILER_RUNTIME={__proto__:null,c:function(t){return i.H.useMemoCache(t)}},r.cache=function(t){return function(){return t.apply(null,arguments)}},r.cloneElement=function(t,e,n){if(t==null)throw Error("The argument must be a React element, but you passed "+t+".");var u=S({},t.props),s=t.key,f=void 0;if(e!=null)for(o in e.ref!==void 0&&(f=void 0),e.key!==void 0&&(s=""+e.key),e)!j.call(e,o)||o==="key"||o==="__self"||o==="__source"||o==="ref"&&e.ref===void 0||(u[o]=e[o]);var o=arguments.length-2;if(o===1)u.children=n;else if(1<o){for(var _=Array(o),c=0;c<o;c++)_[c]=arguments[c+2];u.children=_}return R(t.type,s,void 0,void 0,f,u)},r.createContext=function(t){return t={$$typeof:L,_currentValue:t,_currentValue2:t,_threadCount:0,Provider:null,Consumer:null},t.Provider=t,t.Consumer={$$typeof:I,_context:t},t},r.createElement=function(t,e,n){var u,s={},f=null;if(e!=null)for(u in e.key!==void 0&&(f=""+e.key),e)j.call(e,u)&&u!=="key"&&u!=="__self"&&u!=="__source"&&(s[u]=e[u]);var o=arguments.length-2;if(o===1)s.children=n;else if(1<o){for(var _=Array(o),c=0;c<o;c++)_[c]=arguments[c+2];s.children=_}if(t&&t.defaultProps)for(u in o=t.defaultProps,o)s[u]===void 0&&(s[u]=o[u]);return R(t,f,void 0,void 0,null,s)},r.createRef=function(){return{current:null}},r.forwardRef=function(t){return{$$typeof:U,render:t}},r.isValidElement=d,r.lazy=function(t){return{$$typeof:C,_payload:{_status:-1,_result:t},_init:B}},r.memo=function(t,e){return{$$typeof:D,type:t,compare:e===void 0?null:e}},r.startTransition=function(t){var e=i.T,n={};i.T=n;try{var u=t(),s=i.S;s!==null&&s(n,u),typeof u=="object"&&u!==null&&typeof u.then=="function"&&u.then(J,$)}catch(f){$(f)}finally{i.T=e}},r.unstable_useCacheRefresh=function(){return i.H.useCacheRefresh()},r.use=function(t){return i.H.use(t)},r.useActionState=function(t,e,n){return i.H.useActionState(t,e,n)},r.useCallback=function(t,e){return i.H.useCallback(t,e)},r.useContext=function(t){return i.H.useContext(t)},r.useDebugValue=function(){},r.useDeferredValue=function(t,e){return i.H.useDeferredValue(t,e)},r.useEffect=function(t,e,n){var u=i.H;if(typeof n=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return u.useEffect(t,e)},r.useId=function(){return i.H.useId()},r.useImperativeHandle=function(t,e,n){return i.H.useImperativeHandle(t,e,n)},r.useInsertionEffect=function(t,e){return i.H.useInsertionEffect(t,e)},r.useLayoutEffect=function(t,e){return i.H.useLayoutEffect(t,e)},r.useMemo=function(t,e){return i.H.useMemo(t,e)},r.useOptimistic=function(t,e){return i.H.useOptimistic(t,e)},r.useReducer=function(t,e,n){return i.H.useReducer(t,e,n)},r.useRef=function(t){return i.H.useRef(t)},r.useState=function(t){return i.H.useState(t)},r.useSyncExternalStore=function(t,e,n){return i.H.useSyncExternalStore(t,e,n)},r.useTransition=function(){return i.H.useTransition()},r.version="19.1.0"},51826:(T,r,a)=>{T.exports=a(30115)}}]);
