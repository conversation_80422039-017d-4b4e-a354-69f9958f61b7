"use strict";(self.webpackChunk_articulate_mondrian_bundles=self.webpackChunk_articulate_mondrian_bundles||[]).push([[142,761],{2142:(r,t,e)=>{e.r(t),e.d(t,{derived:()=>u.un,fromStore:()=>o,get:()=>u.Jt,readable:()=>u.HD,readonly:()=>u.tB,toStore:()=>i,writable:()=>u.T5});var n=e(4845),u=e(9930),s=e(3490),c=e(5041);function i(r,t){var e=s.Fg,c=s.hp,i=r();const o=(0,u.T5)(i,(t=>{var u,o=i!==r(),a=s.hp,b=s.Fg;(0,s.G0)(c),(0,s.gU)(e);try{u=(0,n.Fc)((()=>{(0,n.VB)((()=>{const e=r();o&&t(e)}))}))}finally{(0,s.G0)(a),(0,s.gU)(b)}return o=!0,u}));return t?{set:t,update:e=>t(e(r())),subscribe:o.subscribe}:{subscribe:o.subscribe}}function o(r){let t;const e=function(r){let t,e=0,u=(0,c.sP)(0);return()=>{(0,n.oJ)()&&((0,s.Jt)(u),(0,n.VB)((()=>(0===e&&(t=(0,s.vz)((()=>r((()=>{return r=u,void(0,c.hZ)(r,r.v+1);var r}))))),e+=1,()=>{(0,s.io)().then((()=>{e-=1,0===e&&(t?.(),t=void 0)}))}))))}}((e=>{let n=!1;const u=r.subscribe((r=>{t=r,n&&e()}));return n=!0,u}));function i(){return(0,n.oJ)()?(e(),t):(0,u.Jt)(r)}return"set"in r?{get current(){return i()},set current(t){r.set(t)}}:{get current(){return i()}}}}}]);