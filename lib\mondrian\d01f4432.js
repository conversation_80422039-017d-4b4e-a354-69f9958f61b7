/*! For license information please see d01f4432.js.LICENSE.txt */
(self.webpackChunk_articulate_mondrian_bundles=self.webpackChunk_articulate_mondrian_bundles||[]).push([[42],{9205:(t,e,n)=>{e.formatArgs=function(e){if(e[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+e[0]+(this.useColors?"%c ":" ")+"+"+t.exports.humanize(this.diff),!this.useColors)return;const n="color: "+this.color;e.splice(1,0,n,"color: inherit");let r=0,o=0;e[0].replace(/%[a-zA-Z%]/g,(t=>{"%%"!==t&&(r++,"%c"===t&&(o=r))})),e.splice(o,0,n)},e.save=function(t){try{t?e.storage.setItem("debug",t):e.storage.removeItem("debug")}catch(t){}},e.load=function(){let t;try{t=e.storage.getItem("debug")||e.storage.getItem("DEBUG")}catch(t){}return!t&&"undefined"!=typeof process&&"env"in process&&(t=process.env.DEBUG),t},e.useColors=function(){if("undefined"!=typeof window&&window.process&&("renderer"===window.process.type||window.process.__nwjs))return!0;if("undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))return!1;let t;return"undefined"!=typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"undefined"!=typeof window&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||"undefined"!=typeof navigator&&navigator.userAgent&&(t=navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/))&&parseInt(t[1],10)>=31||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/)},e.storage=function(){try{return localStorage}catch(t){}}(),e.destroy=(()=>{let t=!1;return()=>{t||(t=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})(),e.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],e.log=console.debug||console.log||(()=>{}),t.exports=n(8916)(e);const{formatters:r}=t.exports;r.j=function(t){try{return JSON.stringify(t)}catch(t){return"[UnexpectedJSONParseError]: "+t.message}}},8916:(t,e,n)=>{t.exports=function(t){function e(t){let n,o,i,s=null;function a(...t){if(!a.enabled)return;const r=a,o=Number(new Date),i=o-(n||o);r.diff=i,r.prev=n,r.curr=o,n=o,t[0]=e.coerce(t[0]),"string"!=typeof t[0]&&t.unshift("%O");let s=0;t[0]=t[0].replace(/%([a-zA-Z%])/g,((n,o)=>{if("%%"===n)return"%";s++;const i=e.formatters[o];if("function"==typeof i){const e=t[s];n=i.call(r,e),t.splice(s,1),s--}return n})),e.formatArgs.call(r,t),(r.log||e.log).apply(r,t)}return a.namespace=t,a.useColors=e.useColors(),a.color=e.selectColor(t),a.extend=r,a.destroy=e.destroy,Object.defineProperty(a,"enabled",{enumerable:!0,configurable:!1,get:()=>null!==s?s:(o!==e.namespaces&&(o=e.namespaces,i=e.enabled(t)),i),set:t=>{s=t}}),"function"==typeof e.init&&e.init(a),a}function r(t,n){const r=e(this.namespace+(void 0===n?":":n)+t);return r.log=this.log,r}function o(t,e){let n=0,r=0,o=-1,i=0;for(;n<t.length;)if(r<e.length&&(e[r]===t[n]||"*"===e[r]))"*"===e[r]?(o=r,i=n,r++):(n++,r++);else{if(-1===o)return!1;r=o+1,i++,n=i}for(;r<e.length&&"*"===e[r];)r++;return r===e.length}return e.debug=e,e.default=e,e.coerce=function(t){return t instanceof Error?t.stack||t.message:t},e.disable=function(){const t=[...e.names,...e.skips.map((t=>"-"+t))].join(",");return e.enable(""),t},e.enable=function(t){e.save(t),e.namespaces=t,e.names=[],e.skips=[];const n=("string"==typeof t?t:"").trim().replace(/\s+/g,",").split(",").filter(Boolean);for(const t of n)"-"===t[0]?e.skips.push(t.slice(1)):e.names.push(t)},e.enabled=function(t){for(const n of e.skips)if(o(t,n))return!1;for(const n of e.names)if(o(t,n))return!0;return!1},e.humanize=n(5693),e.destroy=function(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")},Object.keys(t).forEach((n=>{e[n]=t[n]})),e.names=[],e.skips=[],e.formatters={},e.selectColor=function(t){let n=0;for(let e=0;e<t.length;e++)n=(n<<5)-n+t.charCodeAt(e),n|=0;return e.colors[Math.abs(n)%e.colors.length]},e.enable(e.load()),e}},3592:(t,e,n)=>{var r=n(7922)(n(5289),"DataView");t.exports=r},2993:(t,e,n)=>{var r=n(6860),o=n(8682),i=n(7429),s=n(537),a=n(4769);function l(t){var e=-1,n=null==t?0:t.length;for(this.clear();++e<n;){var r=t[e];this.set(r[0],r[1])}}l.prototype.clear=r,l.prototype.delete=o,l.prototype.get=i,l.prototype.has=s,l.prototype.set=a,t.exports=l},8120:(t,e,n)=>{var r=n(2372),o=n(3157);function i(t){this.__wrapped__=t,this.__actions__=[],this.__dir__=1,this.__filtered__=!1,this.__iteratees__=[],this.__takeCount__=4294967295,this.__views__=[]}i.prototype=r(o.prototype),i.prototype.constructor=i,t.exports=i},179:(t,e,n)=>{var r=n(9106),o=n(7956),i=n(5063),s=n(5659),a=n(403);function l(t){var e=-1,n=null==t?0:t.length;for(this.clear();++e<n;){var r=t[e];this.set(r[0],r[1])}}l.prototype.clear=r,l.prototype.delete=o,l.prototype.get=i,l.prototype.has=s,l.prototype.set=a,t.exports=l},4693:(t,e,n)=>{var r=n(2372),o=n(3157);function i(t,e){this.__wrapped__=t,this.__actions__=[],this.__chain__=!!e,this.__index__=0,this.__values__=void 0}i.prototype=r(o.prototype),i.prototype.constructor=i,t.exports=i},723:(t,e,n)=>{var r=n(7922)(n(5289),"Map");t.exports=r},6369:(t,e,n)=>{var r=n(188),o=n(8250),i=n(8677),s=n(9721),a=n(3297);function l(t){var e=-1,n=null==t?0:t.length;for(this.clear();++e<n;){var r=t[e];this.set(r[0],r[1])}}l.prototype.clear=r,l.prototype.delete=o,l.prototype.get=i,l.prototype.has=s,l.prototype.set=a,t.exports=l},6808:(t,e,n)=>{var r=n(7922)(n(5289),"Promise");t.exports=r},1101:(t,e,n)=>{var r=n(7922)(n(5289),"Set");t.exports=r},6423:(t,e,n)=>{var r=n(6369),o=n(6744),i=n(8455);function s(t){var e=-1,n=null==t?0:t.length;for(this.__data__=new r;++e<n;)this.add(t[e])}s.prototype.add=s.prototype.push=o,s.prototype.has=i,t.exports=s},2309:(t,e,n)=>{var r=n(179),o=n(3768),i=n(4062),s=n(377),a=n(6485),l=n(3373);function c(t){var e=this.__data__=new r(t);this.size=e.size}c.prototype.clear=o,c.prototype.delete=i,c.prototype.get=s,c.prototype.has=a,c.prototype.set=l,t.exports=c},1741:(t,e,n)=>{var r=n(5289).Symbol;t.exports=r},7264:(t,e,n)=>{var r=n(5289).Uint8Array;t.exports=r},5747:(t,e,n)=>{var r=n(7922)(n(5289),"WeakMap");t.exports=r},6365:t=>{t.exports=function(t,e,n){switch(n.length){case 0:return t.call(e);case 1:return t.call(e,n[0]);case 2:return t.call(e,n[0],n[1]);case 3:return t.call(e,n[0],n[1],n[2])}return t.apply(e,n)}},7045:t=>{t.exports=function(t,e,n,r){for(var o=-1,i=null==t?0:t.length;++o<i;){var s=t[o];e(r,s,n(s),t)}return r}},1750:t=>{t.exports=function(t,e){for(var n=-1,r=null==t?0:t.length,o=0,i=[];++n<r;){var s=t[n];e(s,n,t)&&(i[o++]=s)}return i}},7643:(t,e,n)=>{var r=n(2700),o=n(6888),i=n(4509),s=n(5628),a=n(29),l=n(3371),c=Object.prototype.hasOwnProperty;t.exports=function(t,e){var n=i(t),d=!n&&o(t),u=!n&&!d&&s(t),h=!n&&!d&&!u&&l(t),p=n||d||u||h,f=p?r(t.length,String):[],m=f.length;for(var g in t)!e&&!c.call(t,g)||p&&("length"==g||u&&("offset"==g||"parent"==g)||h&&("buffer"==g||"byteLength"==g||"byteOffset"==g)||a(g,m))||f.push(g);return f}},7888:t=>{t.exports=function(t,e){for(var n=-1,r=null==t?0:t.length,o=Array(r);++n<r;)o[n]=e(t[n],n,t);return o}},4484:t=>{t.exports=function(t,e){for(var n=-1,r=e.length,o=t.length;++n<r;)t[o+n]=e[n];return t}},4940:t=>{t.exports=function(t,e){for(var n=-1,r=null==t?0:t.length;++n<r;)if(e(t[n],n,t))return!0;return!1}},6305:(t,e,n)=>{var r=n(9604),o=n(7332);t.exports=function(t,e,n){(void 0!==n&&!o(t[e],n)||void 0===n&&!(e in t))&&r(t,e,n)}},1967:(t,e,n)=>{var r=n(9604),o=n(7332),i=Object.prototype.hasOwnProperty;t.exports=function(t,e,n){var s=t[e];i.call(t,e)&&o(s,n)&&(void 0!==n||e in t)||r(t,e,n)}},2077:(t,e,n)=>{var r=n(7332);t.exports=function(t,e){for(var n=t.length;n--;)if(r(t[n][0],e))return n;return-1}},7593:(t,e,n)=>{var r=n(1441);t.exports=function(t,e,n,o){return r(t,(function(t,r,i){e(o,t,n(t),i)})),o}},9604:(t,e,n)=>{var r=n(2687);t.exports=function(t,e,n){"__proto__"==e&&r?r(t,e,{configurable:!0,enumerable:!0,value:n,writable:!0}):t[e]=n}},2372:(t,e,n)=>{var r=n(9049),o=Object.create,i=function(){function t(){}return function(e){if(!r(e))return{};if(o)return o(e);t.prototype=e;var n=new t;return t.prototype=void 0,n}}();t.exports=i},1441:(t,e,n)=>{var r=n(197),o=n(9157)(r);t.exports=o},6788:(t,e,n)=>{var r=n(4484),o=n(1327);t.exports=function t(e,n,i,s,a){var l=-1,c=e.length;for(i||(i=o),a||(a=[]);++l<c;){var d=e[l];n>0&&i(d)?n>1?t(d,n-1,i,s,a):r(a,d):s||(a[a.length]=d)}return a}},5754:(t,e,n)=>{var r=n(5345)();t.exports=r},197:(t,e,n)=>{var r=n(5754),o=n(4026);t.exports=function(t,e){return t&&r(t,e,o)}},7802:(t,e,n)=>{var r=n(8461),o=n(9897);t.exports=function(t,e){for(var n=0,i=(e=r(e,t)).length;null!=t&&n<i;)t=t[o(e[n++])];return n&&n==i?t:void 0}},4035:(t,e,n)=>{var r=n(4484),o=n(4509);t.exports=function(t,e,n){var i=e(t);return o(t)?i:r(i,n(t))}},9052:(t,e,n)=>{var r=n(1741),o=n(2143),i=n(1274),s=r?r.toStringTag:void 0;t.exports=function(t){return null==t?void 0===t?"[object Undefined]":"[object Null]":s&&s in Object(t)?o(t):i(t)}},5801:t=>{t.exports=function(t,e){return null!=t&&e in Object(t)}},546:(t,e,n)=>{var r=n(9052),o=n(7926);t.exports=function(t){return o(t)&&"[object Arguments]"==r(t)}},3242:(t,e,n)=>{var r=n(1616),o=n(7926);t.exports=function t(e,n,i,s,a){return e===n||(null==e||null==n||!o(e)&&!o(n)?e!=e&&n!=n:r(e,n,i,s,t,a))}},1616:(t,e,n)=>{var r=n(2309),o=n(5355),i=n(2862),s=n(1861),a=n(6689),l=n(4509),c=n(5628),d=n(3371),u="[object Arguments]",h="[object Array]",p="[object Object]",f=Object.prototype.hasOwnProperty;t.exports=function(t,e,n,m,g,y){var v=l(t),b=l(e),w=v?h:a(t),x=b?h:a(e),k=(w=w==u?p:w)==p,S=(x=x==u?p:x)==p,M=w==x;if(M&&c(t)){if(!c(e))return!1;v=!0,k=!1}if(M&&!k)return y||(y=new r),v||d(t)?o(t,e,n,m,g,y):i(t,e,w,n,m,g,y);if(!(1&n)){var C=k&&f.call(t,"__wrapped__"),A=S&&f.call(e,"__wrapped__");if(C||A){var T=C?t.value():t,O=A?e.value():e;return y||(y=new r),g(T,O,n,m,y)}}return!!M&&(y||(y=new r),s(t,e,n,m,g,y))}},6827:(t,e,n)=>{var r=n(2309),o=n(3242);t.exports=function(t,e,n,i){var s=n.length,a=s,l=!i;if(null==t)return!a;for(t=Object(t);s--;){var c=n[s];if(l&&c[2]?c[1]!==t[c[0]]:!(c[0]in t))return!1}for(;++s<a;){var d=(c=n[s])[0],u=t[d],h=c[1];if(l&&c[2]){if(void 0===u&&!(d in t))return!1}else{var p=new r;if(i)var f=i(u,h,d,t,e,p);if(!(void 0===f?o(h,u,3,i,p):f))return!1}}return!0}},8151:(t,e,n)=>{var r=n(4174),o=n(9804),i=n(9049),s=n(7677),a=/^\[object .+?Constructor\]$/,l=Function.prototype,c=Object.prototype,d=l.toString,u=c.hasOwnProperty,h=RegExp("^"+d.call(u).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");t.exports=function(t){return!(!i(t)||o(t))&&(r(t)?h:a).test(s(t))}},7929:(t,e,n)=>{var r=n(9052),o=n(9906),i=n(7926),s={};s["[object Float32Array]"]=s["[object Float64Array]"]=s["[object Int8Array]"]=s["[object Int16Array]"]=s["[object Int32Array]"]=s["[object Uint8Array]"]=s["[object Uint8ClampedArray]"]=s["[object Uint16Array]"]=s["[object Uint32Array]"]=!0,s["[object Arguments]"]=s["[object Array]"]=s["[object ArrayBuffer]"]=s["[object Boolean]"]=s["[object DataView]"]=s["[object Date]"]=s["[object Error]"]=s["[object Function]"]=s["[object Map]"]=s["[object Number]"]=s["[object Object]"]=s["[object RegExp]"]=s["[object Set]"]=s["[object String]"]=s["[object WeakMap]"]=!1,t.exports=function(t){return i(t)&&o(t.length)&&!!s[r(t)]}},1057:(t,e,n)=>{var r=n(5483),o=n(6590),i=n(780),s=n(4509),a=n(4323);t.exports=function(t){return"function"==typeof t?t:null==t?i:"object"==typeof t?s(t)?o(t[0],t[1]):r(t):a(t)}},8972:(t,e,n)=>{var r=n(6811),o=n(8398),i=Object.prototype.hasOwnProperty;t.exports=function(t){if(!r(t))return o(t);var e=[];for(var n in Object(t))i.call(t,n)&&"constructor"!=n&&e.push(n);return e}},2995:(t,e,n)=>{var r=n(9049),o=n(6811),i=n(5561),s=Object.prototype.hasOwnProperty;t.exports=function(t){if(!r(t))return i(t);var e=o(t),n=[];for(var a in t)("constructor"!=a||!e&&s.call(t,a))&&n.push(a);return n}},3157:t=>{t.exports=function(){}},5732:(t,e,n)=>{var r=n(1441),o=n(1162);t.exports=function(t,e){var n=-1,i=o(t)?Array(t.length):[];return r(t,(function(t,r,o){i[++n]=e(t,r,o)})),i}},5483:(t,e,n)=>{var r=n(6827),o=n(6980),i=n(1521);t.exports=function(t){var e=o(t);return 1==e.length&&e[0][2]?i(e[0][0],e[0][1]):function(n){return n===t||r(n,t,e)}}},6590:(t,e,n)=>{var r=n(3242),o=n(4304),i=n(8040),s=n(1598),a=n(5464),l=n(1521),c=n(9897);t.exports=function(t,e){return s(t)&&a(e)?l(c(t),e):function(n){var s=o(n,t);return void 0===s&&s===e?i(n,t):r(e,s,3)}}},3814:(t,e,n)=>{var r=n(2309),o=n(6305),i=n(5754),s=n(5764),a=n(9049),l=n(4893),c=n(6002);t.exports=function t(e,n,d,u,h){e!==n&&i(n,(function(i,l){if(h||(h=new r),a(i))s(e,n,l,d,t,u,h);else{var p=u?u(c(e,l),i,l+"",e,n,h):void 0;void 0===p&&(p=i),o(e,l,p)}}),l)}},5764:(t,e,n)=>{var r=n(6305),o=n(1190),i=n(1869),s=n(3987),a=n(5901),l=n(6888),c=n(4509),d=n(513),u=n(5628),h=n(4174),p=n(9049),f=n(9335),m=n(3371),g=n(6002),y=n(8432);t.exports=function(t,e,n,v,b,w,x){var k=g(t,n),S=g(e,n),M=x.get(S);if(M)r(t,n,M);else{var C=w?w(k,S,n+"",t,e,x):void 0,A=void 0===C;if(A){var T=c(S),O=!T&&u(S),E=!T&&!O&&m(S);C=S,T||O||E?c(k)?C=k:d(k)?C=s(k):O?(A=!1,C=o(S,!0)):E?(A=!1,C=i(S,!0)):C=[]:f(S)||l(S)?(C=k,l(k)?C=y(k):p(k)&&!h(k)||(C=a(S))):A=!1}A&&(x.set(S,C),b(C,S,v,w,x),x.delete(S)),r(t,n,C)}}},4087:(t,e,n)=>{var r=n(7888),o=n(7802),i=n(1057),s=n(5732),a=n(6669),l=n(2521),c=n(8662),d=n(780),u=n(4509);t.exports=function(t,e,n){e=e.length?r(e,(function(t){return u(t)?function(e){return o(e,1===t.length?t[0]:t)}:t})):[d];var h=-1;e=r(e,l(i));var p=s(t,(function(t,n,o){return{criteria:r(e,(function(e){return e(t)})),index:++h,value:t}}));return a(p,(function(t,e){return c(t,e,n)}))}},696:(t,e,n)=>{var r=n(7802),o=n(8086),i=n(8461);t.exports=function(t,e,n){for(var s=-1,a=e.length,l={};++s<a;){var c=e[s],d=r(t,c);n(d,c)&&o(l,i(c,t),d)}return l}},8553:t=>{t.exports=function(t){return function(e){return null==e?void 0:e[t]}}},6691:(t,e,n)=>{var r=n(7802);t.exports=function(t){return function(e){return r(e,t)}}},419:t=>{var e=Math.ceil,n=Math.max;t.exports=function(t,r,o,i){for(var s=-1,a=n(e((r-t)/(o||1)),0),l=Array(a);a--;)l[i?a:++s]=t,t+=o;return l}},5042:(t,e,n)=>{var r=n(780),o=n(1201),i=n(8261);t.exports=function(t,e){return i(o(t,e,r),t+"")}},8086:(t,e,n)=>{var r=n(1967),o=n(8461),i=n(29),s=n(9049),a=n(9897);t.exports=function(t,e,n,l){if(!s(t))return t;for(var c=-1,d=(e=o(e,t)).length,u=d-1,h=t;null!=h&&++c<d;){var p=a(e[c]),f=n;if("__proto__"===p||"constructor"===p||"prototype"===p)return t;if(c!=u){var m=h[p];void 0===(f=l?l(m,p,h):void 0)&&(f=s(m)?m:i(e[c+1])?[]:{})}r(h,p,f),h=h[p]}return t}},4894:(t,e,n)=>{var r=n(4642),o=n(2687),i=n(780),s=o?function(t,e){return o(t,"toString",{configurable:!0,enumerable:!1,value:r(e),writable:!0})}:i;t.exports=s},6669:t=>{t.exports=function(t,e){var n=t.length;for(t.sort(e);n--;)t[n]=t[n].value;return t}},2700:t=>{t.exports=function(t,e){for(var n=-1,r=Array(t);++n<t;)r[n]=e(n);return r}},56:(t,e,n)=>{var r=n(1741),o=n(7888),i=n(4509),s=n(3062),a=r?r.prototype:void 0,l=a?a.toString:void 0;t.exports=function t(e){if("string"==typeof e)return e;if(i(e))return o(e,t)+"";if(s(e))return l?l.call(e):"";var n=e+"";return"0"==n&&1/e==-1/0?"-0":n}},7756:(t,e,n)=>{var r=n(6044),o=/^\s+/;t.exports=function(t){return t?t.slice(0,r(t)+1).replace(o,""):t}},2521:t=>{t.exports=function(t){return function(e){return t(e)}}},7519:t=>{t.exports=function(t,e){return t.has(e)}},8461:(t,e,n)=>{var r=n(4509),o=n(1598),i=n(7598),s=n(8514);t.exports=function(t,e){return r(t)?t:o(t,e)?[t]:i(s(t))}},6841:(t,e,n)=>{var r=n(7264);t.exports=function(t){var e=new t.constructor(t.byteLength);return new r(e).set(new r(t)),e}},1190:(t,e,n)=>{t=n.nmd(t);var r=n(5289),o=e&&!e.nodeType&&e,i=o&&t&&!t.nodeType&&t,s=i&&i.exports===o?r.Buffer:void 0,a=s?s.allocUnsafe:void 0;t.exports=function(t,e){if(e)return t.slice();var n=t.length,r=a?a(n):new t.constructor(n);return t.copy(r),r}},1869:(t,e,n)=>{var r=n(6841);t.exports=function(t,e){var n=e?r(t.buffer):t.buffer;return new t.constructor(n,t.byteOffset,t.length)}},1462:(t,e,n)=>{var r=n(3062);t.exports=function(t,e){if(t!==e){var n=void 0!==t,o=null===t,i=t==t,s=r(t),a=void 0!==e,l=null===e,c=e==e,d=r(e);if(!l&&!d&&!s&&t>e||s&&a&&c&&!l&&!d||o&&a&&c||!n&&c||!i)return 1;if(!o&&!s&&!d&&t<e||d&&n&&i&&!o&&!s||l&&n&&i||!a&&i||!c)return-1}return 0}},8662:(t,e,n)=>{var r=n(1462);t.exports=function(t,e,n){for(var o=-1,i=t.criteria,s=e.criteria,a=i.length,l=n.length;++o<a;){var c=r(i[o],s[o]);if(c)return o>=l?c:c*("desc"==n[o]?-1:1)}return t.index-e.index}},3987:t=>{t.exports=function(t,e){var n=-1,r=t.length;for(e||(e=Array(r));++n<r;)e[n]=t[n];return e}},9571:(t,e,n)=>{var r=n(1967),o=n(9604);t.exports=function(t,e,n,i){var s=!n;n||(n={});for(var a=-1,l=e.length;++a<l;){var c=e[a],d=i?i(n[c],t[c],c,n,t):void 0;void 0===d&&(d=t[c]),s?o(n,c,d):r(n,c,d)}return n}},9021:(t,e,n)=>{var r=n(5289)["__core-js_shared__"];t.exports=r},4956:(t,e,n)=>{var r=n(7045),o=n(7593),i=n(1057),s=n(4509);t.exports=function(t,e){return function(n,a){var l=s(n)?r:o,c=e?e():{};return l(n,t,i(a,2),c)}}},7659:(t,e,n)=>{var r=n(5042),o=n(7788);t.exports=function(t){return r((function(e,n){var r=-1,i=n.length,s=i>1?n[i-1]:void 0,a=i>2?n[2]:void 0;for(s=t.length>3&&"function"==typeof s?(i--,s):void 0,a&&o(n[0],n[1],a)&&(s=i<3?void 0:s,i=1),e=Object(e);++r<i;){var l=n[r];l&&t(e,l,r,s)}return e}))}},9157:(t,e,n)=>{var r=n(1162);t.exports=function(t,e){return function(n,o){if(null==n)return n;if(!r(n))return t(n,o);for(var i=n.length,s=e?i:-1,a=Object(n);(e?s--:++s<i)&&!1!==o(a[s],s,a););return n}}},5345:t=>{t.exports=function(t){return function(e,n,r){for(var o=-1,i=Object(e),s=r(e),a=s.length;a--;){var l=s[t?a:++o];if(!1===n(i[l],l,i))break}return e}}},301:(t,e,n)=>{var r=n(4693),o=n(7884),i=n(9033),s=n(1136),a=n(4509),l=n(7147);t.exports=function(t){return o((function(e){var n=e.length,o=n,c=r.prototype.thru;for(t&&e.reverse();o--;){var d=e[o];if("function"!=typeof d)throw new TypeError("Expected a function");if(c&&!u&&"wrapper"==s(d))var u=new r([],!0)}for(o=u?o:n;++o<n;){d=e[o];var h=s(d),p="wrapper"==h?i(d):void 0;u=p&&l(p[0])&&424==p[1]&&!p[4].length&&1==p[9]?u[s(p[0])].apply(u,p[3]):1==d.length&&l(d)?u[h]():u.thru(d)}return function(){var t=arguments,r=t[0];if(u&&1==t.length&&a(r))return u.plant(r).value();for(var o=0,i=n?e[o].apply(this,t):r;++o<n;)i=e[o].call(this,i);return i}}))}},9760:(t,e,n)=>{var r=n(419),o=n(7788),i=n(812);t.exports=function(t){return function(e,n,s){return s&&"number"!=typeof s&&o(e,n,s)&&(n=s=void 0),e=i(e),void 0===n?(n=e,e=0):n=i(n),s=void 0===s?e<n?1:-1:i(s),r(e,n,s,t)}}},2687:(t,e,n)=>{var r=n(7922),o=function(){try{var t=r(Object,"defineProperty");return t({},"",{}),t}catch(t){}}();t.exports=o},5355:(t,e,n)=>{var r=n(6423),o=n(4940),i=n(7519);t.exports=function(t,e,n,s,a,l){var c=1&n,d=t.length,u=e.length;if(d!=u&&!(c&&u>d))return!1;var h=l.get(t),p=l.get(e);if(h&&p)return h==e&&p==t;var f=-1,m=!0,g=2&n?new r:void 0;for(l.set(t,e),l.set(e,t);++f<d;){var y=t[f],v=e[f];if(s)var b=c?s(v,y,f,e,t,l):s(y,v,f,t,e,l);if(void 0!==b){if(b)continue;m=!1;break}if(g){if(!o(e,(function(t,e){if(!i(g,e)&&(y===t||a(y,t,n,s,l)))return g.push(e)}))){m=!1;break}}else if(y!==v&&!a(y,v,n,s,l)){m=!1;break}}return l.delete(t),l.delete(e),m}},2862:(t,e,n)=>{var r=n(1741),o=n(7264),i=n(7332),s=n(5355),a=n(8873),l=n(3147),c=r?r.prototype:void 0,d=c?c.valueOf:void 0;t.exports=function(t,e,n,r,c,u,h){switch(n){case"[object DataView]":if(t.byteLength!=e.byteLength||t.byteOffset!=e.byteOffset)return!1;t=t.buffer,e=e.buffer;case"[object ArrayBuffer]":return!(t.byteLength!=e.byteLength||!u(new o(t),new o(e)));case"[object Boolean]":case"[object Date]":case"[object Number]":return i(+t,+e);case"[object Error]":return t.name==e.name&&t.message==e.message;case"[object RegExp]":case"[object String]":return t==e+"";case"[object Map]":var p=a;case"[object Set]":var f=1&r;if(p||(p=l),t.size!=e.size&&!f)return!1;var m=h.get(t);if(m)return m==e;r|=2,h.set(t,e);var g=s(p(t),p(e),r,c,u,h);return h.delete(t),g;case"[object Symbol]":if(d)return d.call(t)==d.call(e)}return!1}},1861:(t,e,n)=>{var r=n(9158),o=Object.prototype.hasOwnProperty;t.exports=function(t,e,n,i,s,a){var l=1&n,c=r(t),d=c.length;if(d!=r(e).length&&!l)return!1;for(var u=d;u--;){var h=c[u];if(!(l?h in e:o.call(e,h)))return!1}var p=a.get(t),f=a.get(e);if(p&&f)return p==e&&f==t;var m=!0;a.set(t,e),a.set(e,t);for(var g=l;++u<d;){var y=t[h=c[u]],v=e[h];if(i)var b=l?i(v,y,h,e,t,a):i(y,v,h,t,e,a);if(!(void 0===b?y===v||s(y,v,n,i,a):b)){m=!1;break}g||(g="constructor"==h)}if(m&&!g){var w=t.constructor,x=e.constructor;w==x||!("constructor"in t)||!("constructor"in e)||"function"==typeof w&&w instanceof w&&"function"==typeof x&&x instanceof x||(m=!1)}return a.delete(t),a.delete(e),m}},7884:(t,e,n)=>{var r=n(2046),o=n(1201),i=n(8261);t.exports=function(t){return i(o(t,void 0,r),t+"")}},9348:(t,e,n)=>{var r="object"==typeof n.g&&n.g&&n.g.Object===Object&&n.g;t.exports=r},9158:(t,e,n)=>{var r=n(4035),o=n(9516),i=n(4026);t.exports=function(t){return r(t,i,o)}},6353:(t,e,n)=>{var r=n(4035),o=n(2579),i=n(4893);t.exports=function(t){return r(t,i,o)}},9033:(t,e,n)=>{var r=n(5508),o=n(890),i=r?function(t){return r.get(t)}:o;t.exports=i},1136:(t,e,n)=>{var r=n(121),o=Object.prototype.hasOwnProperty;t.exports=function(t){for(var e=t.name+"",n=r[e],i=o.call(r,e)?n.length:0;i--;){var s=n[i],a=s.func;if(null==a||a==t)return s.name}return e}},7135:(t,e,n)=>{var r=n(3550);t.exports=function(t,e){var n=t.__data__;return r(e)?n["string"==typeof e?"string":"hash"]:n.map}},6980:(t,e,n)=>{var r=n(5464),o=n(4026);t.exports=function(t){for(var e=o(t),n=e.length;n--;){var i=e[n],s=t[i];e[n]=[i,s,r(s)]}return e}},7922:(t,e,n)=>{var r=n(8151),o=n(1236);t.exports=function(t,e){var n=o(t,e);return r(n)?n:void 0}},3539:(t,e,n)=>{var r=n(5787)(Object.getPrototypeOf,Object);t.exports=r},2143:(t,e,n)=>{var r=n(1741),o=Object.prototype,i=o.hasOwnProperty,s=o.toString,a=r?r.toStringTag:void 0;t.exports=function(t){var e=i.call(t,a),n=t[a];try{t[a]=void 0;var r=!0}catch(t){}var o=s.call(t);return r&&(e?t[a]=n:delete t[a]),o}},9516:(t,e,n)=>{var r=n(1750),o=n(6269),i=Object.prototype.propertyIsEnumerable,s=Object.getOwnPropertySymbols,a=s?function(t){return null==t?[]:(t=Object(t),r(s(t),(function(e){return i.call(t,e)})))}:o;t.exports=a},2579:(t,e,n)=>{var r=n(4484),o=n(3539),i=n(9516),s=n(6269),a=Object.getOwnPropertySymbols?function(t){for(var e=[];t;)r(e,i(t)),t=o(t);return e}:s;t.exports=a},6689:(t,e,n)=>{var r=n(3592),o=n(723),i=n(6808),s=n(1101),a=n(5747),l=n(9052),c=n(7677),d="[object Map]",u="[object Promise]",h="[object Set]",p="[object WeakMap]",f="[object DataView]",m=c(r),g=c(o),y=c(i),v=c(s),b=c(a),w=l;(r&&w(new r(new ArrayBuffer(1)))!=f||o&&w(new o)!=d||i&&w(i.resolve())!=u||s&&w(new s)!=h||a&&w(new a)!=p)&&(w=function(t){var e=l(t),n="[object Object]"==e?t.constructor:void 0,r=n?c(n):"";if(r)switch(r){case m:return f;case g:return d;case y:return u;case v:return h;case b:return p}return e}),t.exports=w},1236:t=>{t.exports=function(t,e){return null==t?void 0:t[e]}},6178:(t,e,n)=>{var r=n(8461),o=n(6888),i=n(4509),s=n(29),a=n(9906),l=n(9897);t.exports=function(t,e,n){for(var c=-1,d=(e=r(e,t)).length,u=!1;++c<d;){var h=l(e[c]);if(!(u=null!=t&&n(t,h)))break;t=t[h]}return u||++c!=d?u:!!(d=null==t?0:t.length)&&a(d)&&s(h,d)&&(i(t)||o(t))}},6860:(t,e,n)=>{var r=n(958);t.exports=function(){this.__data__=r?r(null):{},this.size=0}},8682:t=>{t.exports=function(t){var e=this.has(t)&&delete this.__data__[t];return this.size-=e?1:0,e}},7429:(t,e,n)=>{var r=n(958),o=Object.prototype.hasOwnProperty;t.exports=function(t){var e=this.__data__;if(r){var n=e[t];return"__lodash_hash_undefined__"===n?void 0:n}return o.call(e,t)?e[t]:void 0}},537:(t,e,n)=>{var r=n(958),o=Object.prototype.hasOwnProperty;t.exports=function(t){var e=this.__data__;return r?void 0!==e[t]:o.call(e,t)}},4769:(t,e,n)=>{var r=n(958);t.exports=function(t,e){var n=this.__data__;return this.size+=this.has(t)?0:1,n[t]=r&&void 0===e?"__lodash_hash_undefined__":e,this}},5901:(t,e,n)=>{var r=n(2372),o=n(3539),i=n(6811);t.exports=function(t){return"function"!=typeof t.constructor||i(t)?{}:r(o(t))}},1327:(t,e,n)=>{var r=n(1741),o=n(6888),i=n(4509),s=r?r.isConcatSpreadable:void 0;t.exports=function(t){return i(t)||o(t)||!!(s&&t&&t[s])}},29:t=>{var e=/^(?:0|[1-9]\d*)$/;t.exports=function(t,n){var r=typeof t;return!!(n=null==n?9007199254740991:n)&&("number"==r||"symbol"!=r&&e.test(t))&&t>-1&&t%1==0&&t<n}},7788:(t,e,n)=>{var r=n(7332),o=n(1162),i=n(29),s=n(9049);t.exports=function(t,e,n){if(!s(n))return!1;var a=typeof e;return!!("number"==a?o(n)&&i(e,n.length):"string"==a&&e in n)&&r(n[e],t)}},1598:(t,e,n)=>{var r=n(4509),o=n(3062),i=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,s=/^\w*$/;t.exports=function(t,e){if(r(t))return!1;var n=typeof t;return!("number"!=n&&"symbol"!=n&&"boolean"!=n&&null!=t&&!o(t))||s.test(t)||!i.test(t)||null!=e&&t in Object(e)}},3550:t=>{t.exports=function(t){var e=typeof t;return"string"==e||"number"==e||"symbol"==e||"boolean"==e?"__proto__"!==t:null===t}},7147:(t,e,n)=>{var r=n(8120),o=n(9033),i=n(1136),s=n(466);t.exports=function(t){var e=i(t),n=s[e];if("function"!=typeof n||!(e in r.prototype))return!1;if(t===n)return!0;var a=o(n);return!!a&&t===a[0]}},9804:(t,e,n)=>{var r,o=n(9021),i=(r=/[^.]+$/.exec(o&&o.keys&&o.keys.IE_PROTO||""))?"Symbol(src)_1."+r:"";t.exports=function(t){return!!i&&i in t}},6811:t=>{var e=Object.prototype;t.exports=function(t){var n=t&&t.constructor;return t===("function"==typeof n&&n.prototype||e)}},5464:(t,e,n)=>{var r=n(9049);t.exports=function(t){return t==t&&!r(t)}},9106:t=>{t.exports=function(){this.__data__=[],this.size=0}},7956:(t,e,n)=>{var r=n(2077),o=Array.prototype.splice;t.exports=function(t){var e=this.__data__,n=r(e,t);return!(n<0||(n==e.length-1?e.pop():o.call(e,n,1),--this.size,0))}},5063:(t,e,n)=>{var r=n(2077);t.exports=function(t){var e=this.__data__,n=r(e,t);return n<0?void 0:e[n][1]}},5659:(t,e,n)=>{var r=n(2077);t.exports=function(t){return r(this.__data__,t)>-1}},403:(t,e,n)=>{var r=n(2077);t.exports=function(t,e){var n=this.__data__,o=r(n,t);return o<0?(++this.size,n.push([t,e])):n[o][1]=e,this}},188:(t,e,n)=>{var r=n(2993),o=n(179),i=n(723);t.exports=function(){this.size=0,this.__data__={hash:new r,map:new(i||o),string:new r}}},8250:(t,e,n)=>{var r=n(7135);t.exports=function(t){var e=r(this,t).delete(t);return this.size-=e?1:0,e}},8677:(t,e,n)=>{var r=n(7135);t.exports=function(t){return r(this,t).get(t)}},9721:(t,e,n)=>{var r=n(7135);t.exports=function(t){return r(this,t).has(t)}},3297:(t,e,n)=>{var r=n(7135);t.exports=function(t,e){var n=r(this,t),o=n.size;return n.set(t,e),this.size+=n.size==o?0:1,this}},8873:t=>{t.exports=function(t){var e=-1,n=Array(t.size);return t.forEach((function(t,r){n[++e]=[r,t]})),n}},1521:t=>{t.exports=function(t,e){return function(n){return null!=n&&n[t]===e&&(void 0!==e||t in Object(n))}}},8116:(t,e,n)=>{var r=n(2204);t.exports=function(t){var e=r(t,(function(t){return 500===n.size&&n.clear(),t})),n=e.cache;return e}},5508:(t,e,n)=>{var r=n(5747),o=r&&new r;t.exports=o},958:(t,e,n)=>{var r=n(7922)(Object,"create");t.exports=r},8398:(t,e,n)=>{var r=n(5787)(Object.keys,Object);t.exports=r},5561:t=>{t.exports=function(t){var e=[];if(null!=t)for(var n in Object(t))e.push(n);return e}},6005:(t,e,n)=>{t=n.nmd(t);var r=n(9348),o=e&&!e.nodeType&&e,i=o&&t&&!t.nodeType&&t,s=i&&i.exports===o&&r.process,a=function(){try{return i&&i.require&&i.require("util").types||s&&s.binding&&s.binding("util")}catch(t){}}();t.exports=a},1274:t=>{var e=Object.prototype.toString;t.exports=function(t){return e.call(t)}},5787:t=>{t.exports=function(t,e){return function(n){return t(e(n))}}},1201:(t,e,n)=>{var r=n(6365),o=Math.max;t.exports=function(t,e,n){return e=o(void 0===e?t.length-1:e,0),function(){for(var i=arguments,s=-1,a=o(i.length-e,0),l=Array(a);++s<a;)l[s]=i[e+s];s=-1;for(var c=Array(e+1);++s<e;)c[s]=i[s];return c[e]=n(l),r(t,this,c)}}},121:t=>{t.exports={}},5289:(t,e,n)=>{var r=n(9348),o="object"==typeof self&&self&&self.Object===Object&&self,i=r||o||Function("return this")();t.exports=i},6002:t=>{t.exports=function(t,e){if(("constructor"!==e||"function"!=typeof t[e])&&"__proto__"!=e)return t[e]}},6744:t=>{t.exports=function(t){return this.__data__.set(t,"__lodash_hash_undefined__"),this}},8455:t=>{t.exports=function(t){return this.__data__.has(t)}},3147:t=>{t.exports=function(t){var e=-1,n=Array(t.size);return t.forEach((function(t){n[++e]=t})),n}},8261:(t,e,n)=>{var r=n(4894),o=n(6175)(r);t.exports=o},6175:t=>{var e=Date.now;t.exports=function(t){var n=0,r=0;return function(){var o=e(),i=16-(o-r);if(r=o,i>0){if(++n>=800)return arguments[0]}else n=0;return t.apply(void 0,arguments)}}},3768:(t,e,n)=>{var r=n(179);t.exports=function(){this.__data__=new r,this.size=0}},4062:t=>{t.exports=function(t){var e=this.__data__,n=e.delete(t);return this.size=e.size,n}},377:t=>{t.exports=function(t){return this.__data__.get(t)}},6485:t=>{t.exports=function(t){return this.__data__.has(t)}},3373:(t,e,n)=>{var r=n(179),o=n(723),i=n(6369);t.exports=function(t,e){var n=this.__data__;if(n instanceof r){var s=n.__data__;if(!o||s.length<199)return s.push([t,e]),this.size=++n.size,this;n=this.__data__=new i(s)}return n.set(t,e),this.size=n.size,this}},7598:(t,e,n)=>{var r=n(8116),o=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,i=/\\(\\)?/g,s=r((function(t){var e=[];return 46===t.charCodeAt(0)&&e.push(""),t.replace(o,(function(t,n,r,o){e.push(r?o.replace(i,"$1"):n||t)})),e}));t.exports=s},9897:(t,e,n)=>{var r=n(3062);t.exports=function(t){if("string"==typeof t||r(t))return t;var e=t+"";return"0"==e&&1/t==-1/0?"-0":e}},7677:t=>{var e=Function.prototype.toString;t.exports=function(t){if(null!=t){try{return e.call(t)}catch(t){}try{return t+""}catch(t){}}return""}},6044:t=>{var e=/\s/;t.exports=function(t){for(var n=t.length;n--&&e.test(t.charAt(n)););return n}},3189:(t,e,n)=>{var r=n(8120),o=n(4693),i=n(3987);t.exports=function(t){if(t instanceof r)return t.clone();var e=new o(t.__wrapped__,t.__chain__);return e.__actions__=i(t.__actions__),e.__index__=t.__index__,e.__values__=t.__values__,e}},4642:t=>{t.exports=function(t){return function(){return t}}},4993:(t,e,n)=>{var r=n(9049),o=n(6947),i=n(9570),s=Math.max,a=Math.min;t.exports=function(t,e,n){var l,c,d,u,h,p,f=0,m=!1,g=!1,y=!0;if("function"!=typeof t)throw new TypeError("Expected a function");function v(e){var n=l,r=c;return l=c=void 0,f=e,u=t.apply(r,n)}function b(t){var n=t-p;return void 0===p||n>=e||n<0||g&&t-f>=d}function w(){var t=o();if(b(t))return x(t);h=setTimeout(w,function(t){var n=e-(t-p);return g?a(n,d-(t-f)):n}(t))}function x(t){return h=void 0,y&&l?v(t):(l=c=void 0,u)}function k(){var t=o(),n=b(t);if(l=arguments,c=this,p=t,n){if(void 0===h)return function(t){return f=t,h=setTimeout(w,e),m?v(t):u}(p);if(g)return clearTimeout(h),h=setTimeout(w,e),v(p)}return void 0===h&&(h=setTimeout(w,e)),u}return e=i(e)||0,r(n)&&(m=!!n.leading,d=(g="maxWait"in n)?s(i(n.maxWait)||0,e):d,y="trailing"in n?!!n.trailing:y),k.cancel=function(){void 0!==h&&clearTimeout(h),f=0,l=p=c=h=void 0},k.flush=function(){return void 0===h?u:x(o())},k}},7332:t=>{t.exports=function(t,e){return t===e||t!=t&&e!=e}},3092:(t,e,n)=>{var r=n(8514),o=/[\\^$.*+?()[\]{}|]/g,i=RegExp(o.source);t.exports=function(t){return(t=r(t))&&i.test(t)?t.replace(o,"\\$&"):t}},2046:(t,e,n)=>{var r=n(6788);t.exports=function(t){return null!=t&&t.length?r(t,1):[]}},2:(t,e,n)=>{var r=n(301)();t.exports=r},4304:(t,e,n)=>{var r=n(7802);t.exports=function(t,e,n){var o=null==t?void 0:r(t,e);return void 0===o?n:o}},3134:(t,e,n)=>{var r=n(9604),o=n(4956),i=Object.prototype.hasOwnProperty,s=o((function(t,e,n){i.call(t,n)?t[n].push(e):r(t,n,[e])}));t.exports=s},8040:(t,e,n)=>{var r=n(5801),o=n(6178);t.exports=function(t,e){return null!=t&&o(t,e,r)}},780:t=>{t.exports=function(t){return t}},6888:(t,e,n)=>{var r=n(546),o=n(7926),i=Object.prototype,s=i.hasOwnProperty,a=i.propertyIsEnumerable,l=r(function(){return arguments}())?r:function(t){return o(t)&&s.call(t,"callee")&&!a.call(t,"callee")};t.exports=l},4509:t=>{var e=Array.isArray;t.exports=e},1162:(t,e,n)=>{var r=n(4174),o=n(9906);t.exports=function(t){return null!=t&&o(t.length)&&!r(t)}},513:(t,e,n)=>{var r=n(1162),o=n(7926);t.exports=function(t){return o(t)&&r(t)}},5628:(t,e,n)=>{t=n.nmd(t);var r=n(5289),o=n(4611),i=e&&!e.nodeType&&e,s=i&&t&&!t.nodeType&&t,a=s&&s.exports===i?r.Buffer:void 0,l=(a?a.isBuffer:void 0)||o;t.exports=l},4174:(t,e,n)=>{var r=n(9052),o=n(9049);t.exports=function(t){if(!o(t))return!1;var e=r(t);return"[object Function]"==e||"[object GeneratorFunction]"==e||"[object AsyncFunction]"==e||"[object Proxy]"==e}},9906:t=>{t.exports=function(t){return"number"==typeof t&&t>-1&&t%1==0&&t<=9007199254740991}},4015:t=>{t.exports=function(t){return null==t}},9049:t=>{t.exports=function(t){var e=typeof t;return null!=t&&("object"==e||"function"==e)}},7926:t=>{t.exports=function(t){return null!=t&&"object"==typeof t}},9335:(t,e,n)=>{var r=n(9052),o=n(3539),i=n(7926),s=Function.prototype,a=Object.prototype,l=s.toString,c=a.hasOwnProperty,d=l.call(Object);t.exports=function(t){if(!i(t)||"[object Object]"!=r(t))return!1;var e=o(t);if(null===e)return!0;var n=c.call(e,"constructor")&&e.constructor;return"function"==typeof n&&n instanceof n&&l.call(n)==d}},3062:(t,e,n)=>{var r=n(9052),o=n(7926);t.exports=function(t){return"symbol"==typeof t||o(t)&&"[object Symbol]"==r(t)}},3371:(t,e,n)=>{var r=n(7929),o=n(2521),i=n(6005),s=i&&i.isTypedArray,a=s?o(s):r;t.exports=a},4026:(t,e,n)=>{var r=n(7643),o=n(8972),i=n(1162);t.exports=function(t){return i(t)?r(t):o(t)}},4893:(t,e,n)=>{var r=n(7643),o=n(2995),i=n(1162);t.exports=function(t){return i(t)?r(t,!0):o(t)}},4816:(t,e,n)=>{var r=n(9604),o=n(197),i=n(1057);t.exports=function(t,e){var n={};return e=i(e,3),o(t,(function(t,o,i){r(n,o,e(t,o,i))})),n}},2204:(t,e,n)=>{var r=n(6369);function o(t,e){if("function"!=typeof t||null!=e&&"function"!=typeof e)throw new TypeError("Expected a function");var n=function(){var r=arguments,o=e?e.apply(this,r):r[0],i=n.cache;if(i.has(o))return i.get(o);var s=t.apply(this,r);return n.cache=i.set(o,s)||i,s};return n.cache=new(o.Cache||r),n}o.Cache=r,t.exports=o},9840:(t,e,n)=>{var r=n(3814),o=n(7659)((function(t,e,n){r(t,e,n)}));t.exports=o},890:t=>{t.exports=function(){}},6947:(t,e,n)=>{var r=n(5289);t.exports=function(){return r.Date.now()}},3330:(t,e,n)=>{var r=n(7888),o=n(1057),i=n(696),s=n(6353);t.exports=function(t,e){if(null==t)return{};var n=r(s(t),(function(t){return[t]}));return e=o(e),i(t,n,(function(t,n){return e(t,n[0])}))}},4323:(t,e,n)=>{var r=n(8553),o=n(6691),i=n(1598),s=n(9897);t.exports=function(t){return i(t)?r(s(t)):o(t)}},3537:(t,e,n)=>{var r=n(9760)();t.exports=r},8683:(t,e,n)=>{var r=n(6788),o=n(4087),i=n(5042),s=n(7788),a=i((function(t,e){if(null==t)return[];var n=e.length;return n>1&&s(t,e[0],e[1])?e=[]:n>2&&s(e[0],e[1],e[2])&&(e=[e[0]]),o(t,r(e,1),[])}));t.exports=a},6269:t=>{t.exports=function(){return[]}},4611:t=>{t.exports=function(){return!1}},2602:(t,e,n)=>{var r=n(4993),o=n(9049);t.exports=function(t,e,n){var i=!0,s=!0;if("function"!=typeof t)throw new TypeError("Expected a function");return o(n)&&(i="leading"in n?!!n.leading:i,s="trailing"in n?!!n.trailing:s),r(t,e,{leading:i,maxWait:e,trailing:s})}},812:(t,e,n)=>{var r=n(9570),o=1/0;t.exports=function(t){return t?(t=r(t))===o||t===-1/0?17976931348623157e292*(t<0?-1:1):t==t?t:0:0===t?t:0}},9570:(t,e,n)=>{var r=n(7756),o=n(9049),i=n(3062),s=/^[-+]0x[0-9a-f]+$/i,a=/^0b[01]+$/i,l=/^0o[0-7]+$/i,c=parseInt;t.exports=function(t){if("number"==typeof t)return t;if(i(t))return NaN;if(o(t)){var e="function"==typeof t.valueOf?t.valueOf():t;t=o(e)?e+"":e}if("string"!=typeof t)return 0===t?t:+t;t=r(t);var n=a.test(t);return n||l.test(t)?c(t.slice(2),n?2:8):s.test(t)?NaN:+t}},8432:(t,e,n)=>{var r=n(9571),o=n(4893);t.exports=function(t){return r(t,o(t))}},8514:(t,e,n)=>{var r=n(56);t.exports=function(t){return null==t?"":r(t)}},2732:(t,e,n)=>{var r=n(8514),o=0;t.exports=function(t){var e=++o;return r(t)+e}},466:(t,e,n)=>{var r=n(8120),o=n(4693),i=n(3157),s=n(4509),a=n(7926),l=n(3189),c=Object.prototype.hasOwnProperty;function d(t){if(a(t)&&!s(t)&&!(t instanceof r)){if(t instanceof o)return t;if(c.call(t,"__wrapped__"))return l(t)}return new o(t)}d.prototype=i.prototype,d.prototype.constructor=d,t.exports=d},5693:t=>{var e=1e3,n=60*e,r=60*n,o=24*r,i=7*o;function s(t,e,n,r){var o=e>=1.5*n;return Math.round(t/n)+" "+r+(o?"s":"")}t.exports=function(t,a){a=a||{};var l,c,d=typeof t;if("string"===d&&t.length>0)return function(t){if(!((t=String(t)).length>100)){var s=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(t);if(s){var a=parseFloat(s[1]);switch((s[2]||"ms").toLowerCase()){case"years":case"year":case"yrs":case"yr":case"y":return 315576e5*a;case"weeks":case"week":case"w":return a*i;case"days":case"day":case"d":return a*o;case"hours":case"hour":case"hrs":case"hr":case"h":return a*r;case"minutes":case"minute":case"mins":case"min":case"m":return a*n;case"seconds":case"second":case"secs":case"sec":case"s":return a*e;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return a;default:return}}}}(t);if("number"===d&&isFinite(t))return a.long?(l=t,(c=Math.abs(l))>=o?s(l,c,o,"day"):c>=r?s(l,c,r,"hour"):c>=n?s(l,c,n,"minute"):c>=e?s(l,c,e,"second"):l+" ms"):function(t){var i=Math.abs(t);return i>=o?Math.round(t/o)+"d":i>=r?Math.round(t/r)+"h":i>=n?Math.round(t/n)+"m":i>=e?Math.round(t/e)+"s":t+"ms"}(t);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(t))}},5792:(t,e,n)=>{"use strict";const r=n(7893),o=["Failed to fetch","NetworkError when attempting to fetch resource.","The Internet connection appears to be offline.","Network request failed"];class i extends Error{constructor(t){super(),t instanceof Error?(this.originalError=t,({message:t}=t)):(this.originalError=new Error(t),this.originalError.stack=this.stack),this.name="AbortError",this.message=t}}const s=(t,e)=>new Promise(((n,s)=>{e={onFailedAttempt:()=>{},retries:10,...e};const a=r.operation(e);a.attempt((async r=>{try{n(await t(r))}catch(t){if(!(t instanceof Error))return void s(new TypeError(`Non-error was thrown: "${t}". You should only throw errors.`));if(t instanceof i)a.stop(),s(t.originalError);else if(t instanceof TypeError&&(l=t.message,!o.includes(l)))a.stop(),s(t);else{((t,e,n)=>{const r=n.retries-(e-1);t.attemptNumber=e,t.retriesLeft=r})(t,r,e);try{await e.onFailedAttempt(t)}catch(t){return void s(t)}a.retry(t)||s(a.mainError())}}var l}))}));t.exports=s,t.exports.default=s,t.exports.AbortError=i},7893:(t,e,n)=>{t.exports=n(8203)},8203:(t,e,n)=>{var r=n(5573);e.operation=function(t){var n=e.timeouts(t);return new r(n,{forever:t&&(t.forever||t.retries===1/0),unref:t&&t.unref,maxRetryTime:t&&t.maxRetryTime})},e.timeouts=function(t){if(t instanceof Array)return[].concat(t);var e={retries:10,factor:2,minTimeout:1e3,maxTimeout:1/0,randomize:!1};for(var n in t)e[n]=t[n];if(e.minTimeout>e.maxTimeout)throw new Error("minTimeout is greater than maxTimeout");for(var r=[],o=0;o<e.retries;o++)r.push(this.createTimeout(o,e));return t&&t.forever&&!r.length&&r.push(this.createTimeout(o,e)),r.sort((function(t,e){return t-e})),r},e.createTimeout=function(t,e){var n=e.randomize?Math.random()+1:1,r=Math.round(n*Math.max(e.minTimeout,1)*Math.pow(e.factor,t));return Math.min(r,e.maxTimeout)},e.wrap=function(t,n,r){if(n instanceof Array&&(r=n,n=null),!r)for(var o in r=[],t)"function"==typeof t[o]&&r.push(o);for(var i=0;i<r.length;i++){var s=r[i],a=t[s];t[s]=function(r){var o=e.operation(n),i=Array.prototype.slice.call(arguments,1),s=i.pop();i.push((function(t){o.retry(t)||(t&&(arguments[0]=o.mainError()),s.apply(this,arguments))})),o.attempt((function(){r.apply(t,i)}))}.bind(t,a),t[s].options=n}}},5573:t=>{function e(t,e){"boolean"==typeof e&&(e={forever:e}),this._originalTimeouts=JSON.parse(JSON.stringify(t)),this._timeouts=t,this._options=e||{},this._maxRetryTime=e&&e.maxRetryTime||1/0,this._fn=null,this._errors=[],this._attempts=1,this._operationTimeout=null,this._operationTimeoutCb=null,this._timeout=null,this._operationStart=null,this._timer=null,this._options.forever&&(this._cachedTimeouts=this._timeouts.slice(0))}t.exports=e,e.prototype.reset=function(){this._attempts=1,this._timeouts=this._originalTimeouts.slice(0)},e.prototype.stop=function(){this._timeout&&clearTimeout(this._timeout),this._timer&&clearTimeout(this._timer),this._timeouts=[],this._cachedTimeouts=null},e.prototype.retry=function(t){if(this._timeout&&clearTimeout(this._timeout),!t)return!1;var e=(new Date).getTime();if(t&&e-this._operationStart>=this._maxRetryTime)return this._errors.push(t),this._errors.unshift(new Error("RetryOperation timeout occurred")),!1;this._errors.push(t);var n=this._timeouts.shift();if(void 0===n){if(!this._cachedTimeouts)return!1;this._errors.splice(0,this._errors.length-1),n=this._cachedTimeouts.slice(-1)}var r=this;return this._timer=setTimeout((function(){r._attempts++,r._operationTimeoutCb&&(r._timeout=setTimeout((function(){r._operationTimeoutCb(r._attempts)}),r._operationTimeout),r._options.unref&&r._timeout.unref()),r._fn(r._attempts)}),n),this._options.unref&&this._timer.unref(),!0},e.prototype.attempt=function(t,e){this._fn=t,e&&(e.timeout&&(this._operationTimeout=e.timeout),e.cb&&(this._operationTimeoutCb=e.cb));var n=this;this._operationTimeoutCb&&(this._timeout=setTimeout((function(){n._operationTimeoutCb()}),n._operationTimeout)),this._operationStart=(new Date).getTime(),this._fn(this._attempts)},e.prototype.try=function(t){console.log("Using RetryOperation.try() is deprecated"),this.attempt(t)},e.prototype.start=function(t){console.log("Using RetryOperation.start() is deprecated"),this.attempt(t)},e.prototype.start=e.prototype.try,e.prototype.errors=function(){return this._errors},e.prototype.attempts=function(){return this._attempts},e.prototype.mainError=function(){if(0===this._errors.length)return null;for(var t={},e=null,n=0,r=0;r<this._errors.length;r++){var o=this._errors[r],i=o.message,s=(t[i]||0)+1;t[i]=s,s>=n&&(e=o,n=s)}return e}},5494:(t,e,n)=>{"use strict";n.d(e,{ll:()=>nt,rD:()=>at,UU:()=>it,cY:()=>rt,BN:()=>ot,Ej:()=>st});const r=Math.min,o=Math.max,i=Math.round,s=Math.floor,a=t=>({x:t,y:t}),l={left:"right",right:"left",bottom:"top",top:"bottom"},c={start:"end",end:"start"};function d(t,e,n){return o(t,r(e,n))}function u(t,e){return"function"==typeof t?t(e):t}function h(t){return t.split("-")[0]}function p(t){return t.split("-")[1]}function f(t){return"x"===t?"y":"x"}function m(t){return"y"===t?"height":"width"}function g(t){return["top","bottom"].includes(h(t))?"y":"x"}function y(t){return f(g(t))}function v(t){return t.replace(/start|end/g,(t=>c[t]))}function b(t){return t.replace(/left|right|bottom|top/g,(t=>l[t]))}function w(t){const{x:e,y:n,width:r,height:o}=t;return{width:r,height:o,top:n,left:e,right:e+r,bottom:n+o,x:e,y:n}}function x(t,e,n){let{reference:r,floating:o}=t;const i=g(e),s=y(e),a=m(s),l=h(e),c="y"===i,d=r.x+r.width/2-o.width/2,u=r.y+r.height/2-o.height/2,f=r[a]/2-o[a]/2;let v;switch(l){case"top":v={x:d,y:r.y-o.height};break;case"bottom":v={x:d,y:r.y+r.height};break;case"right":v={x:r.x+r.width,y:u};break;case"left":v={x:r.x-o.width,y:u};break;default:v={x:r.x,y:r.y}}switch(p(e)){case"start":v[s]-=f*(n&&c?-1:1);break;case"end":v[s]+=f*(n&&c?-1:1)}return v}async function k(t,e){var n;void 0===e&&(e={});const{x:r,y:o,platform:i,rects:s,elements:a,strategy:l}=t,{boundary:c="clippingAncestors",rootBoundary:d="viewport",elementContext:h="floating",altBoundary:p=!1,padding:f=0}=u(e,t),m=function(t){return"number"!=typeof t?function(t){return{top:0,right:0,bottom:0,left:0,...t}}(t):{top:t,right:t,bottom:t,left:t}}(f),g=a[p?"floating"===h?"reference":"floating":h],y=w(await i.getClippingRect({element:null==(n=await(null==i.isElement?void 0:i.isElement(g)))||n?g:g.contextElement||await(null==i.getDocumentElement?void 0:i.getDocumentElement(a.floating)),boundary:c,rootBoundary:d,strategy:l})),v="floating"===h?{x:r,y:o,width:s.floating.width,height:s.floating.height}:s.reference,b=await(null==i.getOffsetParent?void 0:i.getOffsetParent(a.floating)),x=await(null==i.isElement?void 0:i.isElement(b))&&await(null==i.getScale?void 0:i.getScale(b))||{x:1,y:1},k=w(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:v,offsetParent:b,strategy:l}):v);return{top:(y.top-k.top+m.top)/x.y,bottom:(k.bottom-y.bottom+m.bottom)/x.y,left:(y.left-k.left+m.left)/x.x,right:(k.right-y.right+m.right)/x.x}}function S(t){return A(t)?(t.nodeName||"").toLowerCase():"#document"}function M(t){var e;return(null==t||null==(e=t.ownerDocument)?void 0:e.defaultView)||window}function C(t){var e;return null==(e=(A(t)?t.ownerDocument:t.document)||window.document)?void 0:e.documentElement}function A(t){return t instanceof Node||t instanceof M(t).Node}function T(t){return t instanceof Element||t instanceof M(t).Element}function O(t){return t instanceof HTMLElement||t instanceof M(t).HTMLElement}function E(t){return"undefined"!=typeof ShadowRoot&&(t instanceof ShadowRoot||t instanceof M(t).ShadowRoot)}function _(t){const{overflow:e,overflowX:n,overflowY:r,display:o}=D(t);return/auto|scroll|overlay|hidden|clip/.test(e+r+n)&&!["inline","contents"].includes(o)}function N(t){return["table","td","th"].includes(S(t))}function $(t){return[":popover-open",":modal"].some((e=>{try{return t.matches(e)}catch(t){return!1}}))}function R(t){const e=z(),n=T(t)?D(t):t;return"none"!==n.transform||"none"!==n.perspective||!!n.containerType&&"normal"!==n.containerType||!e&&!!n.backdropFilter&&"none"!==n.backdropFilter||!e&&!!n.filter&&"none"!==n.filter||["transform","perspective","filter"].some((t=>(n.willChange||"").includes(t)))||["paint","layout","strict","content"].some((t=>(n.contain||"").includes(t)))}function z(){return!("undefined"==typeof CSS||!CSS.supports)&&CSS.supports("-webkit-backdrop-filter","none")}function P(t){return["html","body","#document"].includes(S(t))}function D(t){return M(t).getComputedStyle(t)}function L(t){return T(t)?{scrollLeft:t.scrollLeft,scrollTop:t.scrollTop}:{scrollLeft:t.scrollX,scrollTop:t.scrollY}}function I(t){if("html"===S(t))return t;const e=t.assignedSlot||t.parentNode||E(t)&&t.host||C(t);return E(e)?e.host:e}function F(t){const e=I(t);return P(e)?t.ownerDocument?t.ownerDocument.body:t.body:O(e)&&_(e)?e:F(e)}function j(t,e,n){var r;void 0===e&&(e=[]),void 0===n&&(n=!0);const o=F(t),i=o===(null==(r=t.ownerDocument)?void 0:r.body),s=M(o);if(i){const t=B(s);return e.concat(s,s.visualViewport||[],_(o)?o:[],t&&n?j(t):[])}return e.concat(o,j(o,[],n))}function B(t){return Object.getPrototypeOf(t.parent)?t.frameElement:null}function H(t){const e=D(t);let n=parseFloat(e.width)||0,r=parseFloat(e.height)||0;const o=O(t),s=o?t.offsetWidth:n,a=o?t.offsetHeight:r,l=i(n)!==s||i(r)!==a;return l&&(n=s,r=a),{width:n,height:r,$:l}}function V(t){return T(t)?t:t.contextElement}function K(t){const e=V(t);if(!O(e))return a(1);const n=e.getBoundingClientRect(),{width:r,height:o,$:s}=H(e);let l=(s?i(n.width):n.width)/r,c=(s?i(n.height):n.height)/o;return l&&Number.isFinite(l)||(l=1),c&&Number.isFinite(c)||(c=1),{x:l,y:c}}const U=a(0);function J(t){const e=M(t);return z()&&e.visualViewport?{x:e.visualViewport.offsetLeft,y:e.visualViewport.offsetTop}:U}function W(t,e,n,r){void 0===e&&(e=!1),void 0===n&&(n=!1);const o=t.getBoundingClientRect(),i=V(t);let s=a(1);e&&(r?T(r)&&(s=K(r)):s=K(t));const l=function(t,e,n){return void 0===e&&(e=!1),!(!n||e&&n!==M(t))&&e}(i,n,r)?J(i):a(0);let c=(o.left+l.x)/s.x,d=(o.top+l.y)/s.y,u=o.width/s.x,h=o.height/s.y;if(i){const t=M(i),e=r&&T(r)?M(r):r;let n=t,o=B(n);for(;o&&r&&e!==n;){const t=K(o),e=o.getBoundingClientRect(),r=D(o),i=e.left+(o.clientLeft+parseFloat(r.paddingLeft))*t.x,s=e.top+(o.clientTop+parseFloat(r.paddingTop))*t.y;c*=t.x,d*=t.y,u*=t.x,h*=t.y,c+=i,d+=s,n=M(o),o=B(n)}}return w({width:u,height:h,x:c,y:d})}function q(t){return W(C(t)).left+L(t).scrollLeft}function Z(t,e,n){let r;if("viewport"===e)r=function(t,e){const n=M(t),r=C(t),o=n.visualViewport;let i=r.clientWidth,s=r.clientHeight,a=0,l=0;if(o){i=o.width,s=o.height;const t=z();(!t||t&&"fixed"===e)&&(a=o.offsetLeft,l=o.offsetTop)}return{width:i,height:s,x:a,y:l}}(t,n);else if("document"===e)r=function(t){const e=C(t),n=L(t),r=t.ownerDocument.body,i=o(e.scrollWidth,e.clientWidth,r.scrollWidth,r.clientWidth),s=o(e.scrollHeight,e.clientHeight,r.scrollHeight,r.clientHeight);let a=-n.scrollLeft+q(t);const l=-n.scrollTop;return"rtl"===D(r).direction&&(a+=o(e.clientWidth,r.clientWidth)-i),{width:i,height:s,x:a,y:l}}(C(t));else if(T(e))r=function(t,e){const n=W(t,!0,"fixed"===e),r=n.top+t.clientTop,o=n.left+t.clientLeft,i=O(t)?K(t):a(1);return{width:t.clientWidth*i.x,height:t.clientHeight*i.y,x:o*i.x,y:r*i.y}}(e,n);else{const n=J(t);r={...e,x:e.x-n.x,y:e.y-n.y}}return w(r)}function G(t,e){const n=I(t);return!(n===e||!T(n)||P(n))&&("fixed"===D(n).position||G(n,e))}function Y(t,e,n){const r=O(e),o=C(e),i="fixed"===n,s=W(t,!0,i,e);let l={scrollLeft:0,scrollTop:0};const c=a(0);if(r||!r&&!i)if(("body"!==S(e)||_(o))&&(l=L(e)),r){const t=W(e,!0,i,e);c.x=t.x+e.clientLeft,c.y=t.y+e.clientTop}else o&&(c.x=q(o));return{x:s.left+l.scrollLeft-c.x,y:s.top+l.scrollTop-c.y,width:s.width,height:s.height}}function X(t){return"static"===D(t).position}function Q(t,e){return O(t)&&"fixed"!==D(t).position?e?e(t):t.offsetParent:null}function tt(t,e){const n=M(t);if($(t))return n;if(!O(t)){let e=I(t);for(;e&&!P(e);){if(T(e)&&!X(e))return e;e=I(e)}return n}let r=Q(t,e);for(;r&&N(r)&&X(r);)r=Q(r,e);return r&&P(r)&&X(r)&&!R(r)?n:r||function(t){let e=I(t);for(;O(e)&&!P(e);){if(R(e))return e;if($(e))return null;e=I(e)}return null}(t)||n}const et={convertOffsetParentRelativeRectToViewportRelativeRect:function(t){let{elements:e,rect:n,offsetParent:r,strategy:o}=t;const i="fixed"===o,s=C(r),l=!!e&&$(e.floating);if(r===s||l&&i)return n;let c={scrollLeft:0,scrollTop:0},d=a(1);const u=a(0),h=O(r);if((h||!h&&!i)&&(("body"!==S(r)||_(s))&&(c=L(r)),O(r))){const t=W(r);d=K(r),u.x=t.x+r.clientLeft,u.y=t.y+r.clientTop}return{width:n.width*d.x,height:n.height*d.y,x:n.x*d.x-c.scrollLeft*d.x+u.x,y:n.y*d.y-c.scrollTop*d.y+u.y}},getDocumentElement:C,getClippingRect:function(t){let{element:e,boundary:n,rootBoundary:i,strategy:s}=t;const a=[..."clippingAncestors"===n?$(e)?[]:function(t,e){const n=e.get(t);if(n)return n;let r=j(t,[],!1).filter((t=>T(t)&&"body"!==S(t))),o=null;const i="fixed"===D(t).position;let s=i?I(t):t;for(;T(s)&&!P(s);){const e=D(s),n=R(s);n||"fixed"!==e.position||(o=null),(i?!n&&!o:!n&&"static"===e.position&&o&&["absolute","fixed"].includes(o.position)||_(s)&&!n&&G(t,s))?r=r.filter((t=>t!==s)):o=e,s=I(s)}return e.set(t,r),r}(e,this._c):[].concat(n),i],l=a[0],c=a.reduce(((t,n)=>{const i=Z(e,n,s);return t.top=o(i.top,t.top),t.right=r(i.right,t.right),t.bottom=r(i.bottom,t.bottom),t.left=o(i.left,t.left),t}),Z(e,l,s));return{width:c.right-c.left,height:c.bottom-c.top,x:c.left,y:c.top}},getOffsetParent:tt,getElementRects:async function(t){const e=this.getOffsetParent||tt,n=this.getDimensions,r=await n(t.floating);return{reference:Y(t.reference,await e(t.floating),t.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},getClientRects:function(t){return Array.from(t.getClientRects())},getDimensions:function(t){const{width:e,height:n}=H(t);return{width:e,height:n}},getScale:K,isElement:T,isRTL:function(t){return"rtl"===D(t).direction}};function nt(t,e,n,i){void 0===i&&(i={});const{ancestorScroll:a=!0,ancestorResize:l=!0,elementResize:c="function"==typeof ResizeObserver,layoutShift:d="function"==typeof IntersectionObserver,animationFrame:u=!1}=i,h=V(t),p=a||l?[...h?j(h):[],...j(e)]:[];p.forEach((t=>{a&&t.addEventListener("scroll",n,{passive:!0}),l&&t.addEventListener("resize",n)}));const f=h&&d?function(t,e){let n,i=null;const a=C(t);function l(){var t;clearTimeout(n),null==(t=i)||t.disconnect(),i=null}return function c(d,u){void 0===d&&(d=!1),void 0===u&&(u=1),l();const{left:h,top:p,width:f,height:m}=t.getBoundingClientRect();if(d||e(),!f||!m)return;const g={rootMargin:-s(p)+"px "+-s(a.clientWidth-(h+f))+"px "+-s(a.clientHeight-(p+m))+"px "+-s(h)+"px",threshold:o(0,r(1,u))||1};let y=!0;function v(t){const e=t[0].intersectionRatio;if(e!==u){if(!y)return c();e?c(!1,e):n=setTimeout((()=>{c(!1,1e-7)}),1e3)}y=!1}try{i=new IntersectionObserver(v,{...g,root:a.ownerDocument})}catch(t){i=new IntersectionObserver(v,g)}i.observe(t)}(!0),l}(h,n):null;let m,g=-1,y=null;c&&(y=new ResizeObserver((t=>{let[r]=t;r&&r.target===h&&y&&(y.unobserve(e),cancelAnimationFrame(g),g=requestAnimationFrame((()=>{var t;null==(t=y)||t.observe(e)}))),n()})),h&&!u&&y.observe(h),y.observe(e));let v=u?W(t):null;return u&&function e(){const r=W(t);!v||r.x===v.x&&r.y===v.y&&r.width===v.width&&r.height===v.height||n(),v=r,m=requestAnimationFrame(e)}(),n(),()=>{var t;p.forEach((t=>{a&&t.removeEventListener("scroll",n),l&&t.removeEventListener("resize",n)})),null==f||f(),null==(t=y)||t.disconnect(),y=null,u&&cancelAnimationFrame(m)}}const rt=function(t){return void 0===t&&(t=0),{name:"offset",options:t,async fn(e){var n,r;const{x:o,y:i,placement:s,middlewareData:a}=e,l=await async function(t,e){const{placement:n,platform:r,elements:o}=t,i=await(null==r.isRTL?void 0:r.isRTL(o.floating)),s=h(n),a=p(n),l="y"===g(n),c=["left","top"].includes(s)?-1:1,d=i&&l?-1:1,f=u(e,t);let{mainAxis:m,crossAxis:y,alignmentAxis:v}="number"==typeof f?{mainAxis:f,crossAxis:0,alignmentAxis:null}:{mainAxis:0,crossAxis:0,alignmentAxis:null,...f};return a&&"number"==typeof v&&(y="end"===a?-1*v:v),l?{x:y*d,y:m*c}:{x:m*c,y:y*d}}(e,t);return s===(null==(n=a.offset)?void 0:n.placement)&&null!=(r=a.arrow)&&r.alignmentOffset?{}:{x:o+l.x,y:i+l.y,data:{...l,placement:s}}}}},ot=function(t){return void 0===t&&(t={}),{name:"shift",options:t,async fn(e){const{x:n,y:r,placement:o}=e,{mainAxis:i=!0,crossAxis:s=!1,limiter:a={fn:t=>{let{x:e,y:n}=t;return{x:e,y:n}}},...l}=u(t,e),c={x:n,y:r},p=await k(e,l),m=g(h(o)),y=f(m);let v=c[y],b=c[m];if(i){const t="y"===y?"bottom":"right";v=d(v+p["y"===y?"top":"left"],v,v-p[t])}if(s){const t="y"===m?"bottom":"right";b=d(b+p["y"===m?"top":"left"],b,b-p[t])}const w=a.fn({...e,[y]:v,[m]:b});return{...w,data:{x:w.x-n,y:w.y-r}}}}},it=function(t){return void 0===t&&(t={}),{name:"flip",options:t,async fn(e){var n,r;const{placement:o,middlewareData:i,rects:s,initialPlacement:a,platform:l,elements:c}=e,{mainAxis:d=!0,crossAxis:f=!0,fallbackPlacements:w,fallbackStrategy:x="bestFit",fallbackAxisSideDirection:S="none",flipAlignment:M=!0,...C}=u(t,e);if(null!=(n=i.arrow)&&n.alignmentOffset)return{};const A=h(o),T=g(a),O=h(a)===a,E=await(null==l.isRTL?void 0:l.isRTL(c.floating)),_=w||(O||!M?[b(a)]:function(t){const e=b(t);return[v(t),e,v(e)]}(a)),N="none"!==S;!w&&N&&_.push(...function(t,e,n,r){const o=p(t);let i=function(t,e,n){const r=["left","right"],o=["right","left"],i=["top","bottom"],s=["bottom","top"];switch(t){case"top":case"bottom":return n?e?o:r:e?r:o;case"left":case"right":return e?i:s;default:return[]}}(h(t),"start"===n,r);return o&&(i=i.map((t=>t+"-"+o)),e&&(i=i.concat(i.map(v)))),i}(a,M,S,E));const $=[a,..._],R=await k(e,C),z=[];let P=(null==(r=i.flip)?void 0:r.overflows)||[];if(d&&z.push(R[A]),f){const t=function(t,e,n){void 0===n&&(n=!1);const r=p(t),o=y(t),i=m(o);let s="x"===o?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return e.reference[i]>e.floating[i]&&(s=b(s)),[s,b(s)]}(o,s,E);z.push(R[t[0]],R[t[1]])}if(P=[...P,{placement:o,overflows:z}],!z.every((t=>t<=0))){var D,L;const t=((null==(D=i.flip)?void 0:D.index)||0)+1,e=$[t];if(e)return{data:{index:t,overflows:P},reset:{placement:e}};let n=null==(L=P.filter((t=>t.overflows[0]<=0)).sort(((t,e)=>t.overflows[1]-e.overflows[1]))[0])?void 0:L.placement;if(!n)switch(x){case"bestFit":{var I;const t=null==(I=P.filter((t=>{if(N){const e=g(t.placement);return e===T||"y"===e}return!0})).map((t=>[t.placement,t.overflows.filter((t=>t>0)).reduce(((t,e)=>t+e),0)])).sort(((t,e)=>t[1]-e[1]))[0])?void 0:I[0];t&&(n=t);break}case"initialPlacement":n=a}if(o!==n)return{reset:{placement:n}}}return{}}}},st=function(t){return void 0===t&&(t={}),{name:"size",options:t,async fn(e){const{placement:n,rects:i,platform:s,elements:a}=e,{apply:l=(()=>{}),...c}=u(t,e),d=await k(e,c),f=h(n),m=p(n),y="y"===g(n),{width:v,height:b}=i.floating;let w,x;"top"===f||"bottom"===f?(w=f,x=m===(await(null==s.isRTL?void 0:s.isRTL(a.floating))?"start":"end")?"left":"right"):(x=f,w="end"===m?"top":"bottom");const S=b-d.top-d.bottom,M=v-d.left-d.right,C=r(b-d[w],S),A=r(v-d[x],M),T=!e.middlewareData.shift;let O=C,E=A;if(y?E=m||T?r(A,M):M:O=m||T?r(C,S):S,T&&!m){const t=o(d.left,0),e=o(d.right,0),n=o(d.top,0),r=o(d.bottom,0);y?E=v-2*(0!==t||0!==e?t+e:o(d.left,d.right)):O=b-2*(0!==n||0!==r?n+r:o(d.top,d.bottom))}await l({...e,availableWidth:E,availableHeight:O});const _=await s.getDimensions(a.floating);return v!==_.width||b!==_.height?{reset:{rects:!0}}:{}}}},at=(t,e,n)=>{const r=new Map,o={platform:et,...n},i={...o.platform,_c:r};return(async(t,e,n)=>{const{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:s}=n,a=i.filter(Boolean),l=await(null==s.isRTL?void 0:s.isRTL(e));let c=await s.getElementRects({reference:t,floating:e,strategy:o}),{x:d,y:u}=x(c,r,l),h=r,p={},f=0;for(let n=0;n<a.length;n++){const{name:i,fn:m}=a[n],{x:g,y,data:v,reset:b}=await m({x:d,y:u,initialPlacement:r,placement:h,strategy:o,middlewareData:p,rects:c,platform:s,elements:{reference:t,floating:e}});d=null!=g?g:d,u=null!=y?y:u,p={...p,[i]:{...p[i],...v}},b&&f<=50&&(f++,"object"==typeof b&&(b.placement&&(h=b.placement),b.rects&&(c=!0===b.rects?await s.getElementRects({reference:t,floating:e,strategy:o}):b.rects),({x:d,y:u}=x(c,h,l))),n=-1)}return{x:d,y:u,placement:h,strategy:o,middlewareData:p}})(t,e,{...o,platform:i})}},7601:(t,e,n)=>{"use strict";n.d(e,{mN:()=>C,AH:()=>l,W3:()=>k,Ec:()=>S});const r=globalThis,o=r.ShadowRoot&&(void 0===r.ShadyCSS||r.ShadyCSS.nativeShadow)&&"adoptedStyleSheets"in Document.prototype&&"replace"in CSSStyleSheet.prototype,i=Symbol(),s=new WeakMap;class a{constructor(t,e,n){if(this._$cssResult$=!0,n!==i)throw Error("CSSResult is not constructable. Use `unsafeCSS` or `css` instead.");this.cssText=t,this.t=e}get styleSheet(){let t=this.o;const e=this.t;if(o&&void 0===t){const n=void 0!==e&&1===e.length;n&&(t=s.get(e)),void 0===t&&((this.o=t=new CSSStyleSheet).replaceSync(this.cssText),n&&s.set(e,t))}return t}toString(){return this.cssText}}const l=(t,...e)=>{const n=1===t.length?t[0]:e.reduce(((e,n,r)=>e+(t=>{if(!0===t._$cssResult$)return t.cssText;if("number"==typeof t)return t;throw Error("Value passed to 'css' function must be a 'css' function result: "+t+". Use 'unsafeCSS' to pass non-literal values, but take care to ensure page security.")})(n)+t[r+1]),t[0]);return new a(n,t,i)},c=(t,e)=>{if(o)t.adoptedStyleSheets=e.map((t=>t instanceof CSSStyleSheet?t:t.styleSheet));else for(const n of e){const e=document.createElement("style"),o=r.litNonce;void 0!==o&&e.setAttribute("nonce",o),e.textContent=n.cssText,t.appendChild(e)}},d=o?t=>t:t=>t instanceof CSSStyleSheet?(t=>{let e="";for(const n of t.cssRules)e+=n.cssText;return(t=>new a("string"==typeof t?t:t+"",void 0,i))(e)})(t):t,{is:u,defineProperty:h,getOwnPropertyDescriptor:p,getOwnPropertyNames:f,getOwnPropertySymbols:m,getPrototypeOf:g}=Object,y=globalThis,v=y.trustedTypes,b=v?v.emptyScript:"",w=y.reactiveElementPolyfillSupport,x=(t,e)=>t,k={toAttribute(t,e){switch(e){case Boolean:t=t?b:null;break;case Object:case Array:t=null==t?t:JSON.stringify(t)}return t},fromAttribute(t,e){let n=t;switch(e){case Boolean:n=null!==t;break;case Number:n=null===t?null:Number(t);break;case Object:case Array:try{n=JSON.parse(t)}catch(t){n=null}}return n}},S=(t,e)=>!u(t,e),M={attribute:!0,type:String,converter:k,reflect:!1,hasChanged:S};Symbol.metadata??=Symbol("metadata"),y.litPropertyMetadata??=new WeakMap;class C extends HTMLElement{static addInitializer(t){this._$Ei(),(this.l??=[]).push(t)}static get observedAttributes(){return this.finalize(),this._$Eh&&[...this._$Eh.keys()]}static createProperty(t,e=M){if(e.state&&(e.attribute=!1),this._$Ei(),this.elementProperties.set(t,e),!e.noAccessor){const n=Symbol(),r=this.getPropertyDescriptor(t,n,e);void 0!==r&&h(this.prototype,t,r)}}static getPropertyDescriptor(t,e,n){const{get:r,set:o}=p(this.prototype,t)??{get(){return this[e]},set(t){this[e]=t}};return{get(){return r?.call(this)},set(e){const i=r?.call(this);o.call(this,e),this.requestUpdate(t,i,n)},configurable:!0,enumerable:!0}}static getPropertyOptions(t){return this.elementProperties.get(t)??M}static _$Ei(){if(this.hasOwnProperty(x("elementProperties")))return;const t=g(this);t.finalize(),void 0!==t.l&&(this.l=[...t.l]),this.elementProperties=new Map(t.elementProperties)}static finalize(){if(this.hasOwnProperty(x("finalized")))return;if(this.finalized=!0,this._$Ei(),this.hasOwnProperty(x("properties"))){const t=this.properties,e=[...f(t),...m(t)];for(const n of e)this.createProperty(n,t[n])}const t=this[Symbol.metadata];if(null!==t){const e=litPropertyMetadata.get(t);if(void 0!==e)for(const[t,n]of e)this.elementProperties.set(t,n)}this._$Eh=new Map;for(const[t,e]of this.elementProperties){const n=this._$Eu(t,e);void 0!==n&&this._$Eh.set(n,t)}this.elementStyles=this.finalizeStyles(this.styles)}static finalizeStyles(t){const e=[];if(Array.isArray(t)){const n=new Set(t.flat(1/0).reverse());for(const t of n)e.unshift(d(t))}else void 0!==t&&e.push(d(t));return e}static _$Eu(t,e){const n=e.attribute;return!1===n?void 0:"string"==typeof n?n:"string"==typeof t?t.toLowerCase():void 0}constructor(){super(),this._$Ep=void 0,this.isUpdatePending=!1,this.hasUpdated=!1,this._$Em=null,this._$Ev()}_$Ev(){this._$ES=new Promise((t=>this.enableUpdating=t)),this._$AL=new Map,this._$E_(),this.requestUpdate(),this.constructor.l?.forEach((t=>t(this)))}addController(t){(this._$EO??=new Set).add(t),void 0!==this.renderRoot&&this.isConnected&&t.hostConnected?.()}removeController(t){this._$EO?.delete(t)}_$E_(){const t=new Map,e=this.constructor.elementProperties;for(const n of e.keys())this.hasOwnProperty(n)&&(t.set(n,this[n]),delete this[n]);t.size>0&&(this._$Ep=t)}createRenderRoot(){const t=this.shadowRoot??this.attachShadow(this.constructor.shadowRootOptions);return c(t,this.constructor.elementStyles),t}connectedCallback(){this.renderRoot??=this.createRenderRoot(),this.enableUpdating(!0),this._$EO?.forEach((t=>t.hostConnected?.()))}enableUpdating(t){}disconnectedCallback(){this._$EO?.forEach((t=>t.hostDisconnected?.()))}attributeChangedCallback(t,e,n){this._$AK(t,n)}_$EC(t,e){const n=this.constructor.elementProperties.get(t),r=this.constructor._$Eu(t,n);if(void 0!==r&&!0===n.reflect){const o=(void 0!==n.converter?.toAttribute?n.converter:k).toAttribute(e,n.type);this._$Em=t,null==o?this.removeAttribute(r):this.setAttribute(r,o),this._$Em=null}}_$AK(t,e){const n=this.constructor,r=n._$Eh.get(t);if(void 0!==r&&this._$Em!==r){const t=n.getPropertyOptions(r),o="function"==typeof t.converter?{fromAttribute:t.converter}:void 0!==t.converter?.fromAttribute?t.converter:k;this._$Em=r,this[r]=o.fromAttribute(e,t.type),this._$Em=null}}requestUpdate(t,e,n){if(void 0!==t){if(n??=this.constructor.getPropertyOptions(t),!(n.hasChanged??S)(this[t],e))return;this.P(t,e,n)}!1===this.isUpdatePending&&(this._$ES=this._$ET())}P(t,e,n){this._$AL.has(t)||this._$AL.set(t,e),!0===n.reflect&&this._$Em!==t&&(this._$Ej??=new Set).add(t)}async _$ET(){this.isUpdatePending=!0;try{await this._$ES}catch(t){Promise.reject(t)}const t=this.scheduleUpdate();return null!=t&&await t,!this.isUpdatePending}scheduleUpdate(){return this.performUpdate()}performUpdate(){if(!this.isUpdatePending)return;if(!this.hasUpdated){if(this.renderRoot??=this.createRenderRoot(),this._$Ep){for(const[t,e]of this._$Ep)this[t]=e;this._$Ep=void 0}const t=this.constructor.elementProperties;if(t.size>0)for(const[e,n]of t)!0!==n.wrapped||this._$AL.has(e)||void 0===this[e]||this.P(e,this[e],n)}let t=!1;const e=this._$AL;try{t=this.shouldUpdate(e),t?(this.willUpdate(e),this._$EO?.forEach((t=>t.hostUpdate?.())),this.update(e)):this._$EU()}catch(e){throw t=!1,this._$EU(),e}t&&this._$AE(e)}willUpdate(t){}_$AE(t){this._$EO?.forEach((t=>t.hostUpdated?.())),this.hasUpdated||(this.hasUpdated=!0,this.firstUpdated(t)),this.updated(t)}_$EU(){this._$AL=new Map,this.isUpdatePending=!1}get updateComplete(){return this.getUpdateComplete()}getUpdateComplete(){return this._$ES}shouldUpdate(t){return!0}update(t){this._$Ej&&=this._$Ej.forEach((t=>this._$EC(t,this[t]))),this._$EU()}updated(t){}firstUpdated(t){}}C.elementStyles=[],C.shadowRootOptions={mode:"open"},C[x("elementProperties")]=new Map,C[x("finalized")]=new Map,w?.({ReactiveElement:C}),(y.reactiveElementVersions??=[]).push("2.0.4")},4198:(t,e,n)=>{"use strict";n.d(e,{YY:()=>it,CU:()=>Ft,bP:()=>jt,gk:()=>H,T7:()=>kt,Nx:()=>St,gu:()=>At,FF:()=>Tt,iI:()=>D,z6:()=>xt,hO:()=>Ot,_w:()=>Ct,OX:()=>Pt,Zc:()=>Bt,KV:()=>j,jT:()=>Dt,JJ:()=>Lt,tG:()=>It});var r=n(2559),o=(n(5873),n(1804)),i=n(9679),s=n(196);const a=(t,e)=>!t.selection.empty&&(e&&e(t.tr.deleteSelection().scrollIntoView()),!0);function l(t,e){let{$cursor:n}=t.selection;return!n||(e?!e.endOfTextblock("backward",t):n.parentOffset>0)?null:n}const c=(t,e,n)=>{let o=l(t,n);if(!o)return!1;let a=p(o);if(!a){let n=o.blockRange(),r=n&&(0,s.jP)(n);return null!=r&&(e&&e(t.tr.lift(n,r).scrollIntoView()),!0)}let c=a.nodeBefore;if(!c.type.spec.isolating&&M(t,a,e))return!0;if(0==o.parent.content.size&&(u(c,"end")||r.nh.isSelectable(c))){let n=(0,s.$L)(t.doc,o.before(),o.after(),i.Ji.empty);if(n&&n.slice.size<n.to-n.from){if(e){let o=t.tr.step(n);o.setSelection(u(c,"end")?r.LN.findFrom(o.doc.resolve(o.mapping.map(a.pos,-1)),-1):r.nh.create(o.doc,a.pos-c.nodeSize)),e(o.scrollIntoView())}return!0}}return!(!c.isAtom||a.depth!=o.depth-1||(e&&e(t.tr.delete(a.pos-c.nodeSize,a.pos).scrollIntoView()),0))};function d(t,e,n){let o=e.nodeBefore,a=e.pos-1;for(;!o.isTextblock;a--){if(o.type.spec.isolating)return!1;let t=o.lastChild;if(!t)return!1;o=t}let l=e.nodeAfter,c=e.pos+1;for(;!l.isTextblock;c++){if(l.type.spec.isolating)return!1;let t=l.firstChild;if(!t)return!1;l=t}let d=(0,s.$L)(t.doc,a,c,i.Ji.empty);if(!d||d.from!=a||d instanceof s.Ln&&d.slice.size>=c-a)return!1;if(n){let e=t.tr.step(d);e.setSelection(r.U3.create(e.doc,a)),n(e.scrollIntoView())}return!0}function u(t,e,n=!1){for(let r=t;r;r="start"==e?r.firstChild:r.lastChild){if(r.isTextblock)return!0;if(n&&1!=r.childCount)return!1}return!1}const h=(t,e,n)=>{let{$head:o,empty:i}=t.selection,s=o;if(!i)return!1;if(o.parent.isTextblock){if(n?!n.endOfTextblock("backward",t):o.parentOffset>0)return!1;s=p(o)}let a=s&&s.nodeBefore;return!(!a||!r.nh.isSelectable(a)||(e&&e(t.tr.setSelection(r.nh.create(t.doc,s.pos-a.nodeSize)).scrollIntoView()),0))};function p(t){if(!t.parent.type.spec.isolating)for(let e=t.depth-1;e>=0;e--){if(t.index(e)>0)return t.doc.resolve(t.before(e+1));if(t.node(e).type.spec.isolating)break}return null}function f(t,e){let{$cursor:n}=t.selection;return!n||(e?!e.endOfTextblock("forward",t):n.parentOffset<n.parent.content.size)?null:n}const m=(t,e,n)=>{let o=f(t,n);if(!o)return!1;let a=y(o);if(!a)return!1;let l=a.nodeAfter;if(M(t,a,e))return!0;if(0==o.parent.content.size&&(u(l,"start")||r.nh.isSelectable(l))){let n=(0,s.$L)(t.doc,o.before(),o.after(),i.Ji.empty);if(n&&n.slice.size<n.to-n.from){if(e){let o=t.tr.step(n);o.setSelection(u(l,"start")?r.LN.findFrom(o.doc.resolve(o.mapping.map(a.pos)),1):r.nh.create(o.doc,o.mapping.map(a.pos))),e(o.scrollIntoView())}return!0}}return!(!l.isAtom||a.depth!=o.depth-1||(e&&e(t.tr.delete(a.pos,a.pos+l.nodeSize).scrollIntoView()),0))},g=(t,e,n)=>{let{$head:o,empty:i}=t.selection,s=o;if(!i)return!1;if(o.parent.isTextblock){if(n?!n.endOfTextblock("forward",t):o.parentOffset<o.parent.content.size)return!1;s=y(o)}let a=s&&s.nodeAfter;return!(!a||!r.nh.isSelectable(a)||(e&&e(t.tr.setSelection(r.nh.create(t.doc,s.pos)).scrollIntoView()),0))};function y(t){if(!t.parent.type.spec.isolating)for(let e=t.depth-1;e>=0;e--){let n=t.node(e);if(t.index(e)+1<n.childCount)return t.doc.resolve(t.after(e+1));if(n.type.spec.isolating)break}return null}const v=(t,e)=>{let{$head:n,$anchor:r}=t.selection;return!(!n.parent.type.spec.code||!n.sameParent(r)||(e&&e(t.tr.insertText("\n").scrollIntoView()),0))};function b(t){for(let e=0;e<t.edgeCount;e++){let{type:n}=t.edge(e);if(n.isTextblock&&!n.hasRequiredAttrs())return n}return null}const w=(t,e)=>{let{$head:n,$anchor:o}=t.selection;if(!n.parent.type.spec.code||!n.sameParent(o))return!1;let i=n.node(-1),s=n.indexAfter(-1),a=b(i.contentMatchAt(s));if(!a||!i.canReplaceWith(s,s,a))return!1;if(e){let o=n.after(),i=t.tr.replaceWith(o,o,a.createAndFill());i.setSelection(r.LN.near(i.doc.resolve(o),1)),e(i.scrollIntoView())}return!0},x=(t,e)=>{let n=t.selection,{$from:o,$to:i}=n;if(n instanceof r.i5||o.parent.inlineContent||i.parent.inlineContent)return!1;let s=b(i.parent.contentMatchAt(i.indexAfter()));if(!s||!s.isTextblock)return!1;if(e){let n=(!o.parentOffset&&i.index()<i.parent.childCount?o:i).pos,a=t.tr.insert(n,s.createAndFill());a.setSelection(r.U3.create(a.doc,n+1)),e(a.scrollIntoView())}return!0},k=(t,e)=>{let{$cursor:n}=t.selection;if(!n||n.parent.content.size)return!1;if(n.depth>1&&n.after()!=n.end(-1)){let r=n.before();if((0,s.zy)(t.doc,r))return e&&e(t.tr.split(r).scrollIntoView()),!0}let r=n.blockRange(),o=r&&(0,s.jP)(r);return null!=o&&(e&&e(t.tr.lift(r,o).scrollIntoView()),!0)};var S;function M(t,e,n){let o,a,l=e.nodeBefore,c=e.nodeAfter;if(l.type.spec.isolating||c.type.spec.isolating)return!1;if(function(t,e,n){let r=e.nodeBefore,o=e.nodeAfter,i=e.index();return!(!(r&&o&&r.type.compatibleContent(o.type))||(!r.content.size&&e.parent.canReplace(i-1,i)?(n&&n(t.tr.delete(e.pos-r.nodeSize,e.pos).scrollIntoView()),0):!e.parent.canReplace(i,i+1)||!o.isTextblock&&!(0,s.n9)(t.doc,e.pos)||(n&&n(t.tr.clearIncompatible(e.pos,r.type,r.contentMatchAt(r.childCount)).join(e.pos).scrollIntoView()),0)))}(t,e,n))return!0;let d=e.parent.canReplace(e.index(),e.index()+1);if(d&&(o=(a=l.contentMatchAt(l.childCount)).findWrapping(c.type))&&a.matchType(o[0]||c.type).validEnd){if(n){let r=e.pos+c.nodeSize,a=i.FK.empty;for(let t=o.length-1;t>=0;t--)a=i.FK.from(o[t].create(null,a));a=i.FK.from(l.copy(a));let d=t.tr.step(new s.Wg(e.pos-1,r,e.pos,r,new i.Ji(a,1,0),o.length,!0)),u=r+2*o.length;(0,s.n9)(d.doc,u)&&d.join(u),n(d.scrollIntoView())}return!0}let h=r.LN.findFrom(e,1),p=h&&h.$from.blockRange(h.$to),f=p&&(0,s.jP)(p);if(null!=f&&f>=e.depth)return n&&n(t.tr.lift(p,f).scrollIntoView()),!0;if(d&&u(c,"start",!0)&&u(l,"end")){let r=l,o=[];for(;o.push(r),!r.isTextblock;)r=r.lastChild;let a=c,d=1;for(;!a.isTextblock;a=a.firstChild)d++;if(r.canReplace(r.childCount,r.childCount,a.content)){if(n){let r=i.FK.empty;for(let t=o.length-1;t>=0;t--)r=i.FK.from(o[t].copy(r));n(t.tr.step(new s.Wg(e.pos-o.length,e.pos+c.nodeSize,e.pos+d,e.pos+c.nodeSize-d,new i.Ji(r,o.length,0),0,!0)).scrollIntoView())}return!0}}return!1}function C(t){return function(e,n){let o=e.selection,i=t<0?o.$from:o.$to,s=i.depth;for(;i.node(s).isInline;){if(!s)return!1;s--}return!!i.node(s).isTextblock&&(n&&n(e.tr.setSelection(r.U3.create(e.doc,t<0?i.start(s):i.end(s)))),!0)}}const A=C(-1),T=C(1);function O(t,e=null){return function(n,r){let o=!1;for(let r=0;r<n.selection.ranges.length&&!o;r++){let{$from:{pos:i},$to:{pos:s}}=n.selection.ranges[r];n.doc.nodesBetween(i,s,((r,i)=>{if(o)return!1;if(r.isTextblock&&!r.hasMarkup(t,e))if(r.type==t)o=!0;else{let e=n.doc.resolve(i),r=e.index();o=e.parent.canReplaceWith(r,r+1,t)}}))}if(!o)return!1;if(r){let o=n.tr;for(let r=0;r<n.selection.ranges.length;r++){let{$from:{pos:i},$to:{pos:s}}=n.selection.ranges[r];o.setBlockType(i,s,t,e)}r(o.scrollIntoView())}return!0}}function E(...t){return function(e,n,r){for(let o=0;o<t.length;o++)if(t[o](e,n,r))return!0;return!1}}let _=E(a,c,h),N=E(a,m,g);const $={Enter:E(v,x,k,((t,e)=>{let{$from:n,$to:o}=t.selection;if(t.selection instanceof r.nh&&t.selection.node.isBlock)return!(!n.parentOffset||!(0,s.zy)(t.doc,n.pos)||(e&&e(t.tr.split(n.pos).scrollIntoView()),0));if(!n.parent.isBlock)return!1;if(e){let i=o.parentOffset==o.parent.content.size,a=t.tr;(t.selection instanceof r.U3||t.selection instanceof r.i5)&&a.deleteSelection();let l=0==n.depth?null:b(n.node(-1).contentMatchAt(n.indexAfter(-1))),c=S?[S]:i&&l?[{type:l}]:void 0,d=(0,s.zy)(a.doc,a.mapping.map(n.pos),1,c);if(c||d||!(0,s.zy)(a.doc,a.mapping.map(n.pos),1,l?[{type:l}]:void 0)||(l&&(c=[{type:l}]),d=!0),d&&(a.split(a.mapping.map(n.pos),1,c),!i&&!n.parentOffset&&n.parent.type!=l)){let t=a.mapping.map(n.before()),e=a.doc.resolve(t);l&&n.node(-1).canReplaceWith(e.index(),e.index()+1,l)&&a.setNodeMarkup(a.mapping.map(n.before()),l)}e(a.scrollIntoView())}return!0})),"Mod-Enter":w,Backspace:_,"Mod-Backspace":_,"Shift-Backspace":_,Delete:N,"Mod-Delete":N,"Mod-a":(t,e)=>(e&&e(t.tr.setSelection(new r.i5(t.doc))),!0)},R={"Ctrl-h":$.Backspace,"Alt-Backspace":$["Mod-Backspace"],"Ctrl-d":$.Delete,"Ctrl-Alt-Backspace":$["Mod-Delete"],"Alt-Delete":$["Mod-Delete"],"Alt-d":$["Mod-Delete"],"Ctrl-a":A,"Ctrl-e":T};for(let t in $)R[t]=$[t];function z(t){const{state:e,transaction:n}=t;let{selection:r}=n,{doc:o}=n,{storedMarks:i}=n;return{...e,apply:e.apply.bind(e),applyTransaction:e.applyTransaction.bind(e),plugins:e.plugins,schema:e.schema,reconfigure:e.reconfigure.bind(e),toJSON:e.toJSON.bind(e),get storedMarks(){return i},get selection(){return r},get doc(){return o},get tr(){return r=n.selection,o=n.doc,i=n.storedMarks,n}}}"undefined"!=typeof navigator?/Mac|iP(hone|[oa]d)/.test(navigator.platform):"undefined"!=typeof os&&os.platform&&os.platform();class P{constructor(t){this.editor=t.editor,this.rawCommands=this.editor.extensionManager.commands,this.customState=t.state}get hasCustomState(){return!!this.customState}get state(){return this.customState||this.editor.state}get commands(){const{rawCommands:t,editor:e,state:n}=this,{view:r}=e,{tr:o}=n,i=this.buildProps(o);return Object.fromEntries(Object.entries(t).map((([t,e])=>[t,(...t)=>{const n=e(...t)(i);return o.getMeta("preventDispatch")||this.hasCustomState||r.dispatch(o),n}])))}get chain(){return()=>this.createChain()}get can(){return()=>this.createCan()}createChain(t,e=!0){const{rawCommands:n,editor:r,state:o}=this,{view:i}=r,s=[],a=!!t,l=t||o.tr,c={...Object.fromEntries(Object.entries(n).map((([t,n])=>[t,(...t)=>{const r=this.buildProps(l,e),o=n(...t)(r);return s.push(o),c}]))),run:()=>(a||!e||l.getMeta("preventDispatch")||this.hasCustomState||i.dispatch(l),s.every((t=>!0===t)))};return c}createCan(t){const{rawCommands:e,state:n}=this,r=!1,o=t||n.tr,i=this.buildProps(o,r);return{...Object.fromEntries(Object.entries(e).map((([t,e])=>[t,(...t)=>e(...t)({...i,dispatch:void 0})]))),chain:()=>this.createChain(o,r)}}buildProps(t,e=!0){const{rawCommands:n,editor:r,state:o}=this,{view:i}=r,s={tr:t,editor:r,view:i,state:z({state:o,transaction:t}),dispatch:e?()=>{}:void 0,chain:()=>this.createChain(t,e),can:()=>this.createCan(t),get commands(){return Object.fromEntries(Object.entries(n).map((([t,e])=>[t,(...t)=>e(...t)(s)])))}};return s}}function D(t,e,n){return void 0===t.config[e]&&t.parent?D(t.parent,e,n):"function"==typeof t.config[e]?t.config[e].bind({...n,parent:t.parent?D(t.parent,e,n):null}):t.config[e]}function L(t){return{baseExtensions:t.filter((t=>"extension"===t.type)),nodeExtensions:t.filter((t=>"node"===t.type)),markExtensions:t.filter((t=>"mark"===t.type))}}function I(t){const e=[],{nodeExtensions:n,markExtensions:r}=L(t),o=[...n,...r],i={default:null,rendered:!0,renderHTML:null,parseHTML:null,keepOnSplit:!0,isRequired:!1};return t.forEach((t=>{const n=D(t,"addGlobalAttributes",{name:t.name,options:t.options,storage:t.storage});n&&n().forEach((t=>{t.types.forEach((n=>{Object.entries(t.attributes).forEach((([t,r])=>{e.push({type:n,name:t,attribute:{...i,...r}})}))}))}))})),o.forEach((t=>{const n={name:t.name,options:t.options,storage:t.storage},r=D(t,"addAttributes",n);if(!r)return;const o=r();Object.entries(o).forEach((([n,r])=>{const o={...i,...r};"function"==typeof(null==o?void 0:o.default)&&(o.default=o.default()),(null==o?void 0:o.isRequired)&&void 0===(null==o?void 0:o.default)&&delete o.default,e.push({type:t.name,name:n,attribute:o})}))})),e}function F(t,e){if("string"==typeof t){if(!e.nodes[t])throw Error(`There is no node type named '${t}'. Maybe you forgot to add the extension?`);return e.nodes[t]}return t}function j(...t){return t.filter((t=>!!t)).reduce(((t,e)=>{const n={...t};return Object.entries(e).forEach((([t,e])=>{if(n[t])if("class"===t){const r=e?e.split(" "):[],o=n[t]?n[t].split(" "):[],i=r.filter((t=>!o.includes(t)));n[t]=[...o,...i].join(" ")}else n[t]="style"===t?[n[t],e].join("; "):e;else n[t]=e})),n}),{})}function B(t,e){return e.filter((t=>t.attribute.rendered)).map((e=>e.attribute.renderHTML?e.attribute.renderHTML(t.attrs)||{}:{[e.name]:t.attrs[e.name]})).reduce(((t,e)=>j(t,e)),{})}function H(t,e=void 0,...n){return function(t){return"function"==typeof t}(t)?e?t.bind(e)(...n):t(...n):t}function V(t,e){return t.style?t:{...t,getAttrs:n=>{const r=t.getAttrs?t.getAttrs(n):t.attrs;if(!1===r)return!1;const o=e.reduce(((t,e)=>{const r=e.attribute.parseHTML?e.attribute.parseHTML(n):function(t){return"string"!=typeof t?t:t.match(/^[+-]?(?:\d*\.)?\d+$/)?Number(t):"true"===t||"false"!==t&&t}(n.getAttribute(e.name));return null==r?t:{...t,[e.name]:r}}),{});return{...r,...o}}}}function K(t){return Object.fromEntries(Object.entries(t).filter((([t,e])=>("attrs"!==t||!function(t={}){return 0===Object.keys(t).length&&t.constructor===Object}(e))&&null!=e)))}function U(t,e){var n;const r=I(t),{nodeExtensions:o,markExtensions:s}=L(t),a=null===(n=o.find((t=>D(t,"topNode"))))||void 0===n?void 0:n.name,l=Object.fromEntries(o.map((n=>{const o=r.filter((t=>t.type===n.name)),i={name:n.name,options:n.options,storage:n.storage,editor:e},s=K({...t.reduce(((t,e)=>{const r=D(e,"extendNodeSchema",i);return{...t,...r?r(n):{}}}),{}),content:H(D(n,"content",i)),marks:H(D(n,"marks",i)),group:H(D(n,"group",i)),inline:H(D(n,"inline",i)),atom:H(D(n,"atom",i)),selectable:H(D(n,"selectable",i)),draggable:H(D(n,"draggable",i)),code:H(D(n,"code",i)),defining:H(D(n,"defining",i)),isolating:H(D(n,"isolating",i)),attrs:Object.fromEntries(o.map((t=>{var e;return[t.name,{default:null===(e=null==t?void 0:t.attribute)||void 0===e?void 0:e.default}]})))}),a=H(D(n,"parseHTML",i));a&&(s.parseDOM=a.map((t=>V(t,o))));const l=D(n,"renderHTML",i);l&&(s.toDOM=t=>l({node:t,HTMLAttributes:B(t,o)}));const c=D(n,"renderText",i);return c&&(s.toText=c),[n.name,s]}))),c=Object.fromEntries(s.map((n=>{const o=r.filter((t=>t.type===n.name)),i={name:n.name,options:n.options,storage:n.storage,editor:e},s=K({...t.reduce(((t,e)=>{const r=D(e,"extendMarkSchema",i);return{...t,...r?r(n):{}}}),{}),inclusive:H(D(n,"inclusive",i)),excludes:H(D(n,"excludes",i)),group:H(D(n,"group",i)),spanning:H(D(n,"spanning",i)),code:H(D(n,"code",i)),attrs:Object.fromEntries(o.map((t=>{var e;return[t.name,{default:null===(e=null==t?void 0:t.attribute)||void 0===e?void 0:e.default}]})))}),a=H(D(n,"parseHTML",i));a&&(s.parseDOM=a.map((t=>V(t,o))));const l=D(n,"renderHTML",i);return l&&(s.toDOM=t=>l({mark:t,HTMLAttributes:B(t,o)})),[n.name,s]})));return new i.Sj({topNode:a,nodes:l,marks:c})}function J(t,e){return e.nodes[t]||e.marks[t]||null}function W(t,e){return Array.isArray(e)?e.some((e=>("string"==typeof e?e:e.name)===t.name)):e}const q=(t,e=500)=>{let n="";const r=t.parentOffset;return t.parent.nodesBetween(Math.max(0,r-e),r,((t,e,o,i)=>{var s,a;const l=(null===(a=(s=t.type.spec).toText)||void 0===a?void 0:a.call(s,{node:t,pos:e,parent:o,index:i}))||t.textContent||"%leaf%";n+=l.slice(0,Math.max(0,r-e))})),n};function Z(t){return"[object RegExp]"===Object.prototype.toString.call(t)}class G{constructor(t){this.find=t.find,this.handler=t.handler}}const Y=(t,e)=>{if(Z(e))return e.exec(t);const n=e(t);if(!n)return null;const r=[n.text];return r.index=n.index,r.input=t,r.data=n.data,n.replaceWith&&(n.text.includes(n.replaceWith)||console.warn('[tiptap warn]: "inputRuleMatch.replaceWith" must be part of "inputRuleMatch.text".'),r.push(n.replaceWith)),r};function X(t){var e;const{editor:n,from:r,to:o,text:i,rules:s,plugin:a}=t,{view:l}=n;if(l.composing)return!1;const c=l.state.doc.resolve(r);if(c.parent.type.spec.code||(null===(e=c.nodeBefore||c.nodeAfter)||void 0===e?void 0:e.marks.find((t=>t.type.spec.code))))return!1;let d=!1;const u=q(c)+i;return s.forEach((t=>{if(d)return;const e=Y(u,t.find);if(!e)return;const s=l.state.tr,c=z({state:l.state,transaction:s}),h={from:r-(e[0].length-i.length),to:o},{commands:p,chain:f,can:m}=new P({editor:n,state:c});null!==t.handler({state:c,range:h,match:e,commands:p,chain:f,can:m})&&s.steps.length&&(s.setMeta(a,{transform:s,from:r,to:o,text:i}),l.dispatch(s),d=!0)})),d}function Q(t){const{editor:e,rules:n}=t,o=new r.k_({state:{init:()=>null,apply(t,r){const i=t.getMeta(o);if(i)return i;const s=t.getMeta("applyInputRules");return!!s&&setTimeout((()=>{const{from:t,text:r}=s,i=t+r.length;X({editor:e,from:t,to:i,text:r,rules:n,plugin:o})})),t.selectionSet||t.docChanged?null:r}},props:{handleTextInput:(t,r,i,s)=>X({editor:e,from:r,to:i,text:s,rules:n,plugin:o}),handleDOMEvents:{compositionend:t=>(setTimeout((()=>{const{$cursor:r}=t.state.selection;r&&X({editor:e,from:r.pos,to:r.pos,text:"",rules:n,plugin:o})})),!1)},handleKeyDown(t,r){if("Enter"!==r.key)return!1;const{$cursor:i}=t.state.selection;return!!i&&X({editor:e,from:i.pos,to:i.pos,text:"\n",rules:n,plugin:o})}},isInputRules:!0});return o}class tt{constructor(t){this.find=t.find,this.handler=t.handler}}function et(t){const{editor:e,rules:n}=t;let o=null,i=!1,s=!1,a="undefined"!=typeof ClipboardEvent?new ClipboardEvent("paste"):null,l="undefined"!=typeof DragEvent?new DragEvent("drop"):null;const c=({state:t,from:n,to:r,rule:o,pasteEvt:i})=>{const s=t.tr,c=z({state:t,transaction:s}),d=function(t){const{editor:e,state:n,from:r,to:o,rule:i,pasteEvent:s,dropEvent:a}=t,{commands:l,chain:c,can:d}=new P({editor:e,state:n}),u=[];return n.doc.nodesBetween(r,o,((t,e)=>{if(!t.isTextblock||t.type.spec.code)return;const h=Math.max(r,e),p=Math.min(o,e+t.content.size);((t,e,n)=>{if(Z(e))return[...t.matchAll(e)];const r=e(t,n);return r?r.map((e=>{const n=[e.text];return n.index=e.index,n.input=t,n.data=e.data,e.replaceWith&&(e.text.includes(e.replaceWith)||console.warn('[tiptap warn]: "pasteRuleMatch.replaceWith" must be part of "pasteRuleMatch.text".'),n.push(e.replaceWith)),n})):[]})(t.textBetween(h-e,p-e,void 0,"￼"),i.find,s).forEach((t=>{if(void 0===t.index)return;const e=h+t.index+1,r=e+t[0].length,o={from:n.tr.mapping.map(e),to:n.tr.mapping.map(r)},p=i.handler({state:n,range:o,match:t,commands:l,chain:c,can:d,pasteEvent:s,dropEvent:a});u.push(p)}))})),u.every((t=>null!==t))}({editor:e,state:c,from:Math.max(n-1,0),to:r.b-1,rule:o,pasteEvent:i,dropEvent:l});if(d&&s.steps.length)return l="undefined"!=typeof DragEvent?new DragEvent("drop"):null,a="undefined"!=typeof ClipboardEvent?new ClipboardEvent("paste"):null,s};return n.map((t=>new r.k_({view(t){const e=e=>{var n;o=(null===(n=t.dom.parentElement)||void 0===n?void 0:n.contains(e.target))?t.dom.parentElement:null};return window.addEventListener("dragstart",e),{destroy(){window.removeEventListener("dragstart",e)}}},props:{handleDOMEvents:{drop:(t,e)=>(s=o===t.dom.parentElement,l=e,!1),paste:(t,e)=>{var n;const r=null===(n=e.clipboardData)||void 0===n?void 0:n.getData("text/html");return a=e,i=!!(null==r?void 0:r.includes("data-pm-slice")),!1}}},appendTransaction:(e,n,r)=>{const o=e[0],l="paste"===o.getMeta("uiEvent")&&!i,d="drop"===o.getMeta("uiEvent")&&!s,u=o.getMeta("applyPasteRules"),h=!!u;if(!l&&!d&&!h)return;if(h){const{from:e,text:n}=u,o=e+n.length,i=(t=>{var e;const n=new ClipboardEvent("paste",{clipboardData:new DataTransfer});return null===(e=n.clipboardData)||void 0===e||e.setData("text/html",t),n})(n);return c({rule:t,state:r,from:e,to:{b:o},pasteEvt:i})}const p=n.doc.content.findDiffStart(r.doc.content),f=n.doc.content.findDiffEnd(r.doc.content);return"number"==typeof p&&f&&p!==f.b?c({rule:t,state:r,from:p,to:f,pasteEvt:a}):void 0}})))}class nt{constructor(t,e){this.splittableMarks=[],this.editor=e,this.extensions=nt.resolve(t),this.schema=U(this.extensions,e),this.setupExtensions()}static resolve(t){const e=nt.sort(nt.flatten(t)),n=function(t){const e=t.filter(((e,n)=>t.indexOf(e)!==n));return[...new Set(e)]}(e.map((t=>t.name)));return n.length&&console.warn(`[tiptap warn]: Duplicate extension names found: [${n.map((t=>`'${t}'`)).join(", ")}]. This can lead to issues.`),e}static flatten(t){return t.map((t=>{const e=D(t,"addExtensions",{name:t.name,options:t.options,storage:t.storage});return e?[t,...this.flatten(e())]:t})).flat(10)}static sort(t){return t.sort(((t,e)=>{const n=D(t,"priority")||100,r=D(e,"priority")||100;return n>r?-1:n<r?1:0}))}get commands(){return this.extensions.reduce(((t,e)=>{const n=D(e,"addCommands",{name:e.name,options:e.options,storage:e.storage,editor:this.editor,type:J(e.name,this.schema)});return n?{...t,...n()}:t}),{})}get plugins(){const{editor:t}=this,e=nt.sort([...this.extensions].reverse()),n=[],r=[],i=e.map((e=>{const i={name:e.name,options:e.options,storage:e.storage,editor:t,type:J(e.name,this.schema)},s=[],a=D(e,"addKeyboardShortcuts",i);let l={};if("mark"===e.type&&e.config.exitable&&(l.ArrowRight=()=>Ft.handleExit({editor:t,mark:e})),a){const e=Object.fromEntries(Object.entries(a()).map((([e,n])=>[e,()=>n({editor:t})])));l={...l,...e}}const c=(0,o.w)(l);s.push(c);const d=D(e,"addInputRules",i);W(e,t.options.enableInputRules)&&d&&n.push(...d());const u=D(e,"addPasteRules",i);W(e,t.options.enablePasteRules)&&u&&r.push(...u());const h=D(e,"addProseMirrorPlugins",i);if(h){const t=h();s.push(...t)}return s})).flat();return[Q({editor:t,rules:n}),...et({editor:t,rules:r}),...i]}get attributes(){return I(this.extensions)}get nodeViews(){const{editor:t}=this,{nodeExtensions:e}=L(this.extensions);return Object.fromEntries(e.filter((t=>!!D(t,"addNodeView"))).map((e=>{const n=this.attributes.filter((t=>t.type===e.name)),r={name:e.name,options:e.options,storage:e.storage,editor:t,type:F(e.name,this.schema)},o=D(e,"addNodeView",r);return o?[e.name,(r,i,s,a)=>{const l=B(r,n);return o()({editor:t,node:r,getPos:s,decorations:a,HTMLAttributes:l,extension:e})}]:[]})))}setupExtensions(){this.extensions.forEach((t=>{var e;this.editor.extensionStorage[t.name]=t.storage;const n={name:t.name,options:t.options,storage:t.storage,editor:this.editor,type:J(t.name,this.schema)};"mark"===t.type&&(null===(e=H(D(t,"keepOnSplit",n)))||void 0===e||e)&&this.splittableMarks.push(t.name);const r=D(t,"onBeforeCreate",n),o=D(t,"onCreate",n),i=D(t,"onUpdate",n),s=D(t,"onSelectionUpdate",n),a=D(t,"onTransaction",n),l=D(t,"onFocus",n),c=D(t,"onBlur",n),d=D(t,"onDestroy",n);r&&this.editor.on("beforeCreate",r),o&&this.editor.on("create",o),i&&this.editor.on("update",i),s&&this.editor.on("selectionUpdate",s),a&&this.editor.on("transaction",a),l&&this.editor.on("focus",l),c&&this.editor.on("blur",c),d&&this.editor.on("destroy",d)}))}}function rt(t){return"Object"===function(t){return Object.prototype.toString.call(t).slice(8,-1)}(t)&&t.constructor===Object&&Object.getPrototypeOf(t)===Object.prototype}function ot(t,e){const n={...t};return rt(t)&&rt(e)&&Object.keys(e).forEach((r=>{rt(e[r])?r in t?n[r]=ot(t[r],e[r]):Object.assign(n,{[r]:e[r]}):Object.assign(n,{[r]:e[r]})})),n}class it{constructor(t={}){this.type="extension",this.name="extension",this.parent=null,this.child=null,this.config={name:this.name,defaultOptions:{}},this.config={...this.config,...t},this.name=this.config.name,t.defaultOptions&&Object.keys(t.defaultOptions).length>0&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${this.name}".`),this.options=this.config.defaultOptions,this.config.addOptions&&(this.options=H(D(this,"addOptions",{name:this.name}))),this.storage=H(D(this,"addStorage",{name:this.name,options:this.options}))||{}}static create(t={}){return new it(t)}configure(t={}){const e=this.extend();return e.parent=this.parent,e.options=ot(this.options,t),e.storage=H(D(e,"addStorage",{name:e.name,options:e.options})),e}extend(t={}){const e=new it({...this.config,...t});return e.parent=this,this.child=e,e.name=t.name?t.name:e.parent.name,t.defaultOptions&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${e.name}".`),e.options=H(D(e,"addOptions",{name:e.name})),e.storage=H(D(e,"addStorage",{name:e.name,options:e.options})),e}}function st(t,e,n={strict:!0}){const r=Object.keys(e);return!r.length||r.every((r=>n.strict?e[r]===t[r]:Z(e[r])?e[r].test(t[r]):e[r]===t[r]))}function at(t,e,n={}){return t.find((t=>t.type===e&&st(t.attrs,n)))}function lt(t,e,n={}){return!!at(t,e,n)}function ct(t,e,n={}){if(!t||!e)return;let r=t.parent.childAfter(t.parentOffset);if(t.parentOffset===r.offset&&0!==r.offset&&(r=t.parent.childBefore(t.parentOffset)),!r.node)return;const o=at([...r.node.marks],e,n);if(!o)return;let i=r.index,s=t.start()+r.offset,a=i+1,l=s+r.node.nodeSize;for(at([...r.node.marks],e,n);i>0&&o.isInSet(t.parent.child(i-1).marks);)i-=1,s-=t.parent.child(i).nodeSize;for(;a<t.parent.childCount&&lt([...t.parent.child(a).marks],e,n);)l+=t.parent.child(a).nodeSize,a+=1;return{from:s,to:l}}function dt(t,e){if("string"==typeof t){if(!e.marks[t])throw Error(`There is no mark type named '${t}'. Maybe you forgot to add the extension?`);return e.marks[t]}return t}function ut(t){return t instanceof r.U3}function ht(t=0,e=0,n=0){return Math.min(Math.max(t,e),n)}function pt(){return["iPad Simulator","iPhone Simulator","iPod Simulator","iPad","iPhone","iPod"].includes(navigator.platform)||navigator.userAgent.includes("Mac")&&"ontouchend"in document}it.create({name:"clipboardTextSerializer",addOptions:()=>({blockSeparator:void 0}),addProseMirrorPlugins(){return[new r.k_({key:new r.hs("clipboardTextSerializer"),props:{clipboardTextSerializer:()=>{const{editor:t}=this,{state:e,schema:n}=t,{doc:r,selection:o}=e,{ranges:i}=o,s=Math.min(...i.map((t=>t.$from.pos))),a=Math.max(...i.map((t=>t.$to.pos))),l=function(t){return Object.fromEntries(Object.entries(t.nodes).filter((([,t])=>t.spec.toText)).map((([t,e])=>[t,e.spec.toText])))}(n);return function(t,e,n){const{from:r,to:o}=e,{blockSeparator:i="\n\n",textSerializers:s={}}=n||{};let a="";return t.nodesBetween(r,o,((t,n,l,c)=>{var d;t.isBlock&&n>r&&(a+=i);const u=null==s?void 0:s[t.type.name];if(u)return l&&(a+=u({node:t,pos:n,parent:l,index:c,range:e})),!1;t.isText&&(a+=null===(d=null==t?void 0:t.text)||void 0===d?void 0:d.slice(Math.max(r,n)-n,o-n))})),a}(r,{from:s,to:a},{...void 0!==this.options.blockSeparator?{blockSeparator:this.options.blockSeparator}:{},textSerializers:l})}}})]}});const ft=t=>{const e=t.childNodes;for(let n=e.length-1;n>=0;n-=1){const r=e[n];3===r.nodeType&&r.nodeValue&&/^(\n\s\s|\n)$/.test(r.nodeValue)?t.removeChild(r):1===r.nodeType&&ft(r)}return t};function mt(t){const e=`<body>${t}</body>`,n=(new window.DOMParser).parseFromString(e,"text/html").body;return ft(n)}function gt(t,e,n){n={slice:!0,parseOptions:{},...n};const r="string"==typeof t;if("object"==typeof t&&null!==t)try{return Array.isArray(t)&&t.length>0?i.FK.fromArray(t.map((t=>e.nodeFromJSON(t)))):e.nodeFromJSON(t)}catch(r){return console.warn("[tiptap warn]: Invalid content.","Passed value:",t,"Error:",r),gt("",e,n)}if(r){const r=i.S4.fromSchema(e);return n.slice?r.parseSlice(mt(t),n.parseOptions).content:r.parse(mt(t),n.parseOptions)}return gt("",e,n)}function yt(){return"undefined"!=typeof navigator&&/Mac/.test(navigator.platform)}function vt(t,e,n={}){const{from:r,to:o,empty:i}=t.selection,s=e?F(e,t.schema):null,a=[];t.doc.nodesBetween(r,o,((t,e)=>{if(t.isText)return;const n=Math.max(r,e),i=Math.min(o,e+t.nodeSize);a.push({node:t,from:n,to:i})}));const l=o-r,c=a.filter((t=>!s||s.name===t.node.type.name)).filter((t=>st(t.node.attrs,n,{strict:!1})));return i?!!c.length:c.reduce(((t,e)=>t+e.to-e.from),0)>=l}function bt(t,e){return e.nodes[t]?"node":e.marks[t]?"mark":null}function wt(t,e){const n="string"==typeof e?[e]:e;return Object.keys(t).reduce(((e,r)=>(n.includes(r)||(e[r]=t[r]),e)),{})}function xt(t,e){const n=dt(e,t.schema),{from:r,to:o,empty:i}=t.selection,s=[];i?(t.storedMarks&&s.push(...t.storedMarks),s.push(...t.selection.$head.marks())):t.doc.nodesBetween(r,o,(t=>{s.push(...t.marks)}));const a=s.find((t=>t.type.name===n.name));return a?{...a.attrs}:{}}function kt(t,e){const n=new s.dL(t);return e.forEach((t=>{t.steps.forEach((t=>{n.step(t)}))})),n}function St(t,e,n){const r=[];return t.nodesBetween(e.from,e.to,((t,e)=>{n(t)&&r.push({node:t,pos:e})})),r}function Mt(t){return e=>function(t,e){for(let n=t.depth;n>0;n-=1){const r=t.node(n);if(e(r))return{pos:n>0?t.before(n):0,start:t.start(n),depth:n,node:r}}}(e.$from,t)}function Ct(t,e){return U(nt.resolve(t),e)}function At(t,e){const n=bt("string"==typeof e?e:e.name,t.schema);return"node"===n?function(t,e){const n=F(e,t.schema),{from:r,to:o}=t.selection,i=[];t.doc.nodesBetween(r,o,(t=>{i.push(t)}));const s=i.reverse().find((t=>t.type.name===n.name));return s?{...s.attrs}:{}}(t,e):"mark"===n?xt(t,e):{}}function Tt(t){const{mapping:e,steps:n}=t,r=[];return e.maps.forEach(((t,o)=>{const i=[];if(t.ranges.length)t.forEach(((t,e)=>{i.push({from:t,to:e})}));else{const{from:t,to:e}=n[o];if(void 0===t||void 0===e)return;i.push({from:t,to:e})}i.forEach((({from:t,to:n})=>{const i=e.slice(o).map(t,-1),s=e.slice(o).map(n),a=e.invert().map(i,-1),l=e.invert().map(s);r.push({oldRange:{from:a,to:l},newRange:{from:i,to:s}})}))})),function(t){const e=function(t,e=JSON.stringify){const n={};return t.filter((t=>{const r=e(t);return!Object.prototype.hasOwnProperty.call(n,r)&&(n[r]=!0)}))}(t);return 1===e.length?e:e.filter(((t,n)=>!e.filter(((t,e)=>e!==n)).some((e=>t.oldRange.from>=e.oldRange.from&&t.oldRange.to<=e.oldRange.to&&t.newRange.from>=e.newRange.from&&t.newRange.to<=e.newRange.to))))}(r)}function Ot(t,e,n){const r=[];return t===e?n.resolve(t).marks().forEach((e=>{const o=ct(n.resolve(t-1),e.type);o&&r.push({mark:e,...o})})):n.nodesBetween(t,e,((t,e)=>{t&&void 0!==(null==t?void 0:t.nodeSize)&&r.push(...t.marks.map((n=>({from:e,to:e+t.nodeSize,mark:n}))))})),r}function Et(t,e,n){return Object.fromEntries(Object.entries(n).filter((([n])=>{const r=t.find((t=>t.type===e&&t.name===n));return!!r&&r.attribute.keepOnSplit})))}function _t(t,e){const{nodeExtensions:n}=L(e),r=n.find((e=>e.name===t));if(!r)return!1;const o=H(D(r,"group",{name:r.name,options:r.options,storage:r.storage}));return"string"==typeof o&&o.split(" ").includes("list")}function Nt(t,e){const n=t.storedMarks||t.selection.$to.parentOffset&&t.selection.$from.marks();if(n){const r=n.filter((t=>null==e?void 0:e.includes(t.type.name)));t.tr.ensureMarks(r)}}const $t=(t,e)=>{const n=Mt((t=>t.type===e))(t.selection);if(!n)return!0;const r=t.doc.resolve(Math.max(0,n.pos-1)).before(n.depth);if(void 0===r)return!0;const o=t.doc.nodeAt(r);return n.node.type!==(null==o?void 0:o.type)||!(0,s.n9)(t.doc,n.pos)||(t.join(n.pos),!0)},Rt=(t,e)=>{const n=Mt((t=>t.type===e))(t.selection);if(!n)return!0;const r=t.doc.resolve(n.start).after(n.depth);if(void 0===r)return!0;const o=t.doc.nodeAt(r);return n.node.type!==(null==o?void 0:o.type)||!(0,s.n9)(t.doc,r)||(t.join(r),!0)};var zt=Object.freeze({__proto__:null,blur:()=>({editor:t,view:e})=>(requestAnimationFrame((()=>{var n;t.isDestroyed||(e.dom.blur(),null===(n=null===window||void 0===window?void 0:window.getSelection())||void 0===n||n.removeAllRanges())})),!0),clearContent:(t=!1)=>({commands:e})=>e.setContent("",t),clearNodes:()=>({state:t,tr:e,dispatch:n})=>{const{selection:r}=e,{ranges:o}=r;return!n||(o.forEach((({$from:n,$to:r})=>{t.doc.nodesBetween(n.pos,r.pos,((t,n)=>{if(t.type.isText)return;const{doc:r,mapping:o}=e,i=r.resolve(o.map(n)),a=r.resolve(o.map(n+t.nodeSize)),l=i.blockRange(a);if(!l)return;const c=(0,s.jP)(l);if(t.type.isTextblock){const{defaultType:t}=i.parent.contentMatchAt(i.index());e.setNodeMarkup(l.start,t)}(c||0===c)&&e.lift(l,c)}))})),!0)},command:t=>e=>t(e),createParagraphNear:()=>({state:t,dispatch:e})=>x(t,e),cut:(t,e)=>({editor:n,tr:o})=>{const{state:i}=n,s=i.doc.slice(t.from,t.to);o.deleteRange(t.from,t.to);const a=o.mapping.map(e);return o.insert(a,s.content),o.setSelection(new r.U3(o.doc.resolve(a-1))),!0},deleteCurrentNode:()=>({tr:t,dispatch:e})=>{const{selection:n}=t,r=n.$anchor.node();if(r.content.size>0)return!1;const o=t.selection.$anchor;for(let n=o.depth;n>0;n-=1)if(o.node(n).type===r.type){if(e){const e=o.before(n),r=o.after(n);t.delete(e,r).scrollIntoView()}return!0}return!1},deleteNode:t=>({tr:e,state:n,dispatch:r})=>{const o=F(t,n.schema),i=e.selection.$anchor;for(let t=i.depth;t>0;t-=1)if(i.node(t).type===o){if(r){const n=i.before(t),r=i.after(t);e.delete(n,r).scrollIntoView()}return!0}return!1},deleteRange:t=>({tr:e,dispatch:n})=>{const{from:r,to:o}=t;return n&&e.delete(r,o),!0},deleteSelection:()=>({state:t,dispatch:e})=>a(t,e),enter:()=>({commands:t})=>t.keyboardShortcut("Enter"),exitCode:()=>({state:t,dispatch:e})=>w(t,e),extendMarkRange:(t,e={})=>({tr:n,state:o,dispatch:i})=>{const s=dt(t,o.schema),{doc:a,selection:l}=n,{$from:c,from:d,to:u}=l;if(i){const t=ct(c,s,e);if(t&&t.from<=d&&t.to>=u){const e=r.U3.create(a,t.from,t.to);n.setSelection(e)}}return!0},first:t=>e=>{const n="function"==typeof t?t(e):t;for(let t=0;t<n.length;t+=1)if(n[t](e))return!0;return!1},focus:(t=null,e={})=>({editor:n,view:o,tr:i,dispatch:s})=>{e={scrollIntoView:!0,...e};const a=()=>{pt()&&o.dom.focus(),requestAnimationFrame((()=>{n.isDestroyed||(o.focus(),(null==e?void 0:e.scrollIntoView)&&n.commands.scrollIntoView())}))};if(o.hasFocus()&&null===t||!1===t)return!0;if(s&&null===t&&!ut(n.state.selection))return a(),!0;const l=function(t,e=null){if(!e)return null;const n=r.LN.atStart(t),o=r.LN.atEnd(t);if("start"===e||!0===e)return n;if("end"===e)return o;const i=n.from,s=o.to;return"all"===e?r.U3.create(t,ht(0,i,s),ht(t.content.size,i,s)):r.U3.create(t,ht(e,i,s),ht(e,i,s))}(i.doc,t)||n.state.selection,c=n.state.selection.eq(l);return s&&(c||i.setSelection(l),c&&i.storedMarks&&i.setStoredMarks(i.storedMarks),a()),!0},forEach:(t,e)=>n=>t.every(((t,r)=>e(t,{...n,index:r}))),insertContent:(t,e)=>({tr:n,commands:r})=>r.insertContentAt({from:n.selection.from,to:n.selection.to},t,e),insertContentAt:(t,e,n)=>({tr:o,dispatch:i,editor:a})=>{if(i){n={parseOptions:{},updateSelection:!0,applyInputRules:!1,applyPasteRules:!1,...n};const i=gt(e,a.schema,{parseOptions:{preserveWhitespace:"full",...n.parseOptions}});if("<>"===i.toString())return!0;let l,{from:c,to:d}="number"==typeof t?{from:t,to:t}:{from:t.from,to:t.to},u=!0,h=!0;if((i.toString().startsWith("<")?i:[i]).forEach((t=>{t.check(),u=!!u&&t.isText&&0===t.marks.length,h=!!h&&t.isBlock})),c===d&&h){const{parent:t}=o.doc.resolve(c);t.isTextblock&&!t.type.spec.code&&!t.childCount&&(c-=1,d+=1)}u?(l=Array.isArray(e)?e.map((t=>t.text||"")).join(""):"object"==typeof e&&e&&e.text?e.text:e,o.insertText(l,c,d)):(l=i,o.replaceWith(c,d,l)),n.updateSelection&&function(t,e,n){const o=t.steps.length-1;if(o<e)return;const i=t.steps[o];if(!(i instanceof s.Ln||i instanceof s.Wg))return;const a=t.mapping.maps[o];let l=0;a.forEach(((t,e,n,r)=>{0===l&&(l=r)})),t.setSelection(r.LN.near(t.doc.resolve(l),-1))}(o,o.steps.length-1),n.applyInputRules&&o.setMeta("applyInputRules",{from:c,text:l}),n.applyPasteRules&&o.setMeta("applyPasteRules",{from:c,text:l})}return!0},joinUp:()=>({state:t,dispatch:e})=>((t,e)=>{let n,o=t.selection,i=o instanceof r.nh;if(i){if(o.node.isTextblock||!(0,s.n9)(t.doc,o.from))return!1;n=o.from}else if(n=(0,s.N0)(t.doc,o.from,-1),null==n)return!1;if(e){let o=t.tr.join(n);i&&o.setSelection(r.nh.create(o.doc,n-t.doc.resolve(n).nodeBefore.nodeSize)),e(o.scrollIntoView())}return!0})(t,e),joinDown:()=>({state:t,dispatch:e})=>((t,e)=>{let n,o=t.selection;if(o instanceof r.nh){if(o.node.isTextblock||!(0,s.n9)(t.doc,o.to))return!1;n=o.to}else if(n=(0,s.N0)(t.doc,o.to,1),null==n)return!1;return e&&e(t.tr.join(n).scrollIntoView()),!0})(t,e),joinBackward:()=>({state:t,dispatch:e})=>c(t,e),joinForward:()=>({state:t,dispatch:e})=>m(t,e),joinItemBackward:()=>({tr:t,state:e,dispatch:n})=>{try{const r=(0,s.N0)(e.doc,e.selection.$from.pos,-1);return null!=r&&(t.join(r,2),n&&n(t),!0)}catch{return!1}},joinItemForward:()=>({state:t,dispatch:e,tr:n})=>{try{const r=(0,s.N0)(t.doc,t.selection.$from.pos,1);return null!=r&&(n.join(r,2),e&&e(n),!0)}catch(t){return!1}},joinTextblockBackward:()=>({state:t,dispatch:e})=>((t,e,n)=>{let r=l(t,void 0);if(!r)return!1;let o=p(r);return!!o&&d(t,o,e)})(t,e),joinTextblockForward:()=>({state:t,dispatch:e})=>((t,e,n)=>{let r=f(t,void 0);if(!r)return!1;let o=y(r);return!!o&&d(t,o,e)})(t,e),keyboardShortcut:t=>({editor:e,view:n,tr:r,dispatch:o})=>{const i=function(t){const e=t.split(/-(?!$)/);let n,r,o,i,s=e[e.length-1];"Space"===s&&(s=" ");for(let t=0;t<e.length-1;t+=1){const s=e[t];if(/^(cmd|meta|m)$/i.test(s))i=!0;else if(/^a(lt)?$/i.test(s))n=!0;else if(/^(c|ctrl|control)$/i.test(s))r=!0;else if(/^s(hift)?$/i.test(s))o=!0;else{if(!/^mod$/i.test(s))throw new Error(`Unrecognized modifier name: ${s}`);pt()||yt()?i=!0:r=!0}}return n&&(s=`Alt-${s}`),r&&(s=`Ctrl-${s}`),i&&(s=`Meta-${s}`),o&&(s=`Shift-${s}`),s}(t).split(/-(?!$)/),s=i.find((t=>!["Alt","Ctrl","Meta","Shift"].includes(t))),a=new KeyboardEvent("keydown",{key:"Space"===s?" ":s,altKey:i.includes("Alt"),ctrlKey:i.includes("Ctrl"),metaKey:i.includes("Meta"),shiftKey:i.includes("Shift"),bubbles:!0,cancelable:!0}),l=e.captureTransaction((()=>{n.someProp("handleKeyDown",(t=>t(n,a)))}));return null==l||l.steps.forEach((t=>{const e=t.map(r.mapping);e&&o&&r.maybeStep(e)})),!0},lift:(t,e={})=>({state:n,dispatch:r})=>!!vt(n,F(t,n.schema),e)&&((t,e)=>{let{$from:n,$to:r}=t.selection,o=n.blockRange(r),i=o&&(0,s.jP)(o);return null!=i&&(e&&e(t.tr.lift(o,i).scrollIntoView()),!0)})(n,r),liftEmptyBlock:()=>({state:t,dispatch:e})=>k(t,e),liftListItem:t=>({state:e,dispatch:n})=>{return(r=F(t,e.schema),function(t,e){let{$from:n,$to:o}=t.selection,a=n.blockRange(o,(t=>t.childCount>0&&t.firstChild.type==r));return!!a&&(!e||(n.node(a.depth-1).type==r?function(t,e,n,r){let o=t.tr,a=r.end,l=r.$to.end(r.depth);a<l&&(o.step(new s.Wg(a-1,l,a,l,new i.Ji(i.FK.from(n.create(null,r.parent.copy())),1,0),1,!0)),r=new i.u$(o.doc.resolve(r.$from.pos),o.doc.resolve(l),r.depth));const c=(0,s.jP)(r);if(null==c)return!1;o.lift(r,c);let d=o.mapping.map(a,-1)-1;return(0,s.n9)(o.doc,d)&&o.join(d),e(o.scrollIntoView()),!0}(t,e,r,a):function(t,e,n){let r=t.tr,o=n.parent;for(let t=n.end,e=n.endIndex-1,i=n.startIndex;e>i;e--)t-=o.child(e).nodeSize,r.delete(t-1,t+1);let a=r.doc.resolve(n.start),l=a.nodeAfter;if(r.mapping.map(n.end)!=n.start+a.nodeAfter.nodeSize)return!1;let c=0==n.startIndex,d=n.endIndex==o.childCount,u=a.node(-1),h=a.index(-1);if(!u.canReplace(h+(c?0:1),h+1,l.content.append(d?i.FK.empty:i.FK.from(o))))return!1;let p=a.pos,f=p+l.nodeSize;return r.step(new s.Wg(p-(c?1:0),f+(d?1:0),p+1,f-1,new i.Ji((c?i.FK.empty:i.FK.from(o.copy(i.FK.empty))).append(d?i.FK.empty:i.FK.from(o.copy(i.FK.empty))),c?0:1,d?0:1),c?0:1)),e(r.scrollIntoView()),!0}(t,e,a)))})(e,n);var r},newlineInCode:()=>({state:t,dispatch:e})=>v(t,e),resetAttributes:(t,e)=>({tr:n,state:r,dispatch:o})=>{let i=null,s=null;const a=bt("string"==typeof t?t:t.name,r.schema);return!!a&&("node"===a&&(i=F(t,r.schema)),"mark"===a&&(s=dt(t,r.schema)),o&&n.selection.ranges.forEach((t=>{r.doc.nodesBetween(t.$from.pos,t.$to.pos,((t,r)=>{i&&i===t.type&&n.setNodeMarkup(r,void 0,wt(t.attrs,e)),s&&t.marks.length&&t.marks.forEach((o=>{s===o.type&&n.addMark(r,r+t.nodeSize,s.create(wt(o.attrs,e)))}))}))})),!0)},scrollIntoView:()=>({tr:t,dispatch:e})=>(e&&t.scrollIntoView(),!0),selectAll:()=>({tr:t,commands:e})=>e.setTextSelection({from:0,to:t.doc.content.size}),selectNodeBackward:()=>({state:t,dispatch:e})=>h(t,e),selectNodeForward:()=>({state:t,dispatch:e})=>g(t,e),selectParentNode:()=>({state:t,dispatch:e})=>((t,e)=>{let n,{$from:o,to:i}=t.selection,s=o.sharedDepth(i);return 0!=s&&(n=o.before(s),e&&e(t.tr.setSelection(r.nh.create(t.doc,n))),!0)})(t,e),selectTextblockEnd:()=>({state:t,dispatch:e})=>T(t,e),selectTextblockStart:()=>({state:t,dispatch:e})=>A(t,e),setContent:(t,e=!1,n={})=>({tr:r,editor:o,dispatch:i})=>{const{doc:s}=r,a=function(t,e,n={}){return gt(t,e,{slice:!1,parseOptions:n})}(t,o.schema,n);return i&&r.replaceWith(0,s.content.size,a).setMeta("preventUpdate",!e),!0},setMark:(t,e={})=>({tr:n,state:r,dispatch:o})=>{const{selection:i}=n,{empty:s,ranges:a}=i,l=dt(t,r.schema);if(o)if(s){const t=xt(r,l);n.addStoredMark(l.create({...t,...e}))}else a.forEach((t=>{const o=t.$from.pos,i=t.$to.pos;r.doc.nodesBetween(o,i,((t,r)=>{const s=Math.max(r,o),a=Math.min(r+t.nodeSize,i);t.marks.find((t=>t.type===l))?t.marks.forEach((t=>{l===t.type&&n.addMark(s,a,l.create({...t.attrs,...e}))})):n.addMark(s,a,l.create(e))}))}));return function(t,e,n){var r;const{selection:o}=e;let i=null;if(ut(o)&&(i=o.$cursor),i){const e=null!==(r=t.storedMarks)&&void 0!==r?r:i.marks();return!!n.isInSet(e)||!e.some((t=>t.type.excludes(n)))}const{ranges:s}=o;return s.some((({$from:e,$to:r})=>{let o=0===e.depth&&t.doc.inlineContent&&t.doc.type.allowsMarkType(n);return t.doc.nodesBetween(e.pos,r.pos,((t,e,r)=>{if(o)return!1;if(t.isInline){const e=!r||r.type.allowsMarkType(n),i=!!n.isInSet(t.marks)||!t.marks.some((t=>t.type.excludes(n)));o=e&&i}return!o})),o}))}(r,n,l)},setMeta:(t,e)=>({tr:n})=>(n.setMeta(t,e),!0),setNode:(t,e={})=>({state:n,dispatch:r,chain:o})=>{const i=F(t,n.schema);return i.isTextblock?o().command((({commands:t})=>!!O(i,e)(n)||t.clearNodes())).command((({state:t})=>O(i,e)(t,r))).run():(console.warn('[tiptap warn]: Currently "setNode()" only supports text block nodes.'),!1)},setNodeSelection:t=>({tr:e,dispatch:n})=>{if(n){const{doc:n}=e,o=ht(t,0,n.content.size),i=r.nh.create(n,o);e.setSelection(i)}return!0},setTextSelection:t=>({tr:e,dispatch:n})=>{if(n){const{doc:n}=e,{from:o,to:i}="number"==typeof t?{from:t,to:t}:t,s=r.U3.atStart(n).from,a=r.U3.atEnd(n).to,l=ht(o,s,a),c=ht(i,s,a),d=r.U3.create(n,l,c);e.setSelection(d)}return!0},sinkListItem:t=>({state:e,dispatch:n})=>{const r=F(t,e.schema);return(o=r,function(t,e){let{$from:n,$to:r}=t.selection,a=n.blockRange(r,(t=>t.childCount>0&&t.firstChild.type==o));if(!a)return!1;let l=a.startIndex;if(0==l)return!1;let c=a.parent,d=c.child(l-1);if(d.type!=o)return!1;if(e){let n=d.lastChild&&d.lastChild.type==c.type,r=i.FK.from(n?o.create():null),l=new i.Ji(i.FK.from(o.create(null,i.FK.from(c.type.create(null,r)))),n?3:1,0),u=a.start,h=a.end;e(t.tr.step(new s.Wg(u-(n?3:1),h,u,h,l,1,!0)).scrollIntoView())}return!0})(e,n);var o},splitBlock:({keepMarks:t=!0}={})=>({tr:e,state:n,dispatch:o,editor:i})=>{const{selection:a,doc:l}=e,{$from:c,$to:d}=a,u=Et(i.extensionManager.attributes,c.node().type.name,c.node().attrs);if(a instanceof r.nh&&a.node.isBlock)return!(!c.parentOffset||!(0,s.zy)(l,c.pos)||(o&&(t&&Nt(n,i.extensionManager.splittableMarks),e.split(c.pos).scrollIntoView()),0));if(!c.parent.isBlock)return!1;if(o){const o=d.parentOffset===d.parent.content.size;a instanceof r.U3&&e.deleteSelection();const l=0===c.depth?void 0:function(t){for(let e=0;e<t.edgeCount;e+=1){const{type:n}=t.edge(e);if(n.isTextblock&&!n.hasRequiredAttrs())return n}return null}(c.node(-1).contentMatchAt(c.indexAfter(-1)));let h=o&&l?[{type:l,attrs:u}]:void 0,p=(0,s.zy)(e.doc,e.mapping.map(c.pos),1,h);if(h||p||!(0,s.zy)(e.doc,e.mapping.map(c.pos),1,l?[{type:l}]:void 0)||(p=!0,h=l?[{type:l,attrs:u}]:void 0),p&&(e.split(e.mapping.map(c.pos),1,h),l&&!o&&!c.parentOffset&&c.parent.type!==l)){const t=e.mapping.map(c.before()),n=e.doc.resolve(t);c.node(-1).canReplaceWith(n.index(),n.index()+1,l)&&e.setNodeMarkup(e.mapping.map(c.before()),l)}t&&Nt(n,i.extensionManager.splittableMarks),e.scrollIntoView()}return!0},splitListItem:t=>({tr:e,state:n,dispatch:o,editor:a})=>{var l;const c=F(t,n.schema),{$from:d,$to:u}=n.selection,h=n.selection.node;if(h&&h.isBlock||d.depth<2||!d.sameParent(u))return!1;const p=d.node(-1);if(p.type!==c)return!1;const f=a.extensionManager.attributes;if(0===d.parent.content.size&&d.node(-1).childCount===d.indexAfter(-1)){if(2===d.depth||d.node(-3).type!==c||d.index(-2)!==d.node(-2).childCount-1)return!1;if(o){let t=i.FK.empty;const n=d.index(-1)?1:d.index(-2)?2:3;for(let e=d.depth-n;e>=d.depth-3;e-=1)t=i.FK.from(d.node(e).copy(t));const o=d.indexAfter(-1)<d.node(-2).childCount?1:d.indexAfter(-2)<d.node(-3).childCount?2:3,s=Et(f,d.node().type.name,d.node().attrs),a=(null===(l=c.contentMatch.defaultType)||void 0===l?void 0:l.createAndFill(s))||void 0;t=t.append(i.FK.from(c.createAndFill(null,a)||void 0));const u=d.before(d.depth-(n-1));e.replace(u,d.after(-o),new i.Ji(t,4-n,0));let h=-1;e.doc.nodesBetween(u,e.doc.content.size,((t,e)=>{if(h>-1)return!1;t.isTextblock&&0===t.content.size&&(h=e+1)})),h>-1&&e.setSelection(r.U3.near(e.doc.resolve(h))),e.scrollIntoView()}return!0}const m=u.pos===d.end()?p.contentMatchAt(0).defaultType:null,g=Et(f,p.type.name,p.attrs),y=Et(f,d.node().type.name,d.node().attrs);e.delete(d.pos,u.pos);const v=m?[{type:c,attrs:g},{type:m,attrs:y}]:[{type:c,attrs:g}];if(!(0,s.zy)(e.doc,d.pos,2))return!1;if(o){const{selection:t,storedMarks:r}=n,{splittableMarks:i}=a.extensionManager,s=r||t.$to.parentOffset&&t.$from.marks();if(e.split(d.pos,2,v).scrollIntoView(),!s||!o)return!0;const l=s.filter((t=>i.includes(t.type.name)));e.ensureMarks(l)}return!0},toggleList:(t,e,n,r={})=>({editor:o,tr:i,state:s,dispatch:a,chain:l,commands:c,can:d})=>{const{extensions:u,splittableMarks:h}=o.extensionManager,p=F(t,s.schema),f=F(e,s.schema),{selection:m,storedMarks:g}=s,{$from:y,$to:v}=m,b=y.blockRange(v),w=g||m.$to.parentOffset&&m.$from.marks();if(!b)return!1;const x=Mt((t=>_t(t.type.name,u)))(m);if(b.depth>=1&&x&&b.depth-x.depth<=1){if(x.node.type===p)return c.liftListItem(f);if(_t(x.node.type.name,u)&&p.validContent(x.node.content)&&a)return l().command((()=>(i.setNodeMarkup(x.pos,p),!0))).command((()=>$t(i,p))).command((()=>Rt(i,p))).run()}return n&&w&&a?l().command((()=>{const t=d().wrapInList(p,r),e=w.filter((t=>h.includes(t.type.name)));return i.ensureMarks(e),!!t||c.clearNodes()})).wrapInList(p,r).command((()=>$t(i,p))).command((()=>Rt(i,p))).run():l().command((()=>!!d().wrapInList(p,r)||c.clearNodes())).wrapInList(p,r).command((()=>$t(i,p))).command((()=>Rt(i,p))).run()},toggleMark:(t,e={},n={})=>({state:r,commands:o})=>{const{extendEmptyMarkRange:i=!1}=n,s=dt(t,r.schema);return function(t,e,n={}){const{empty:r,ranges:o}=t.selection,i=e?dt(e,t.schema):null;if(r)return!!(t.storedMarks||t.selection.$from.marks()).filter((t=>!i||i.name===t.type.name)).find((t=>st(t.attrs,n,{strict:!1})));let s=0;const a=[];if(o.forEach((({$from:e,$to:n})=>{const r=e.pos,o=n.pos;t.doc.nodesBetween(r,o,((t,e)=>{if(!t.isText&&!t.marks.length)return;const n=Math.max(r,e),i=Math.min(o,e+t.nodeSize);s+=i-n,a.push(...t.marks.map((t=>({mark:t,from:n,to:i}))))}))})),0===s)return!1;const l=a.filter((t=>!i||i.name===t.mark.type.name)).filter((t=>st(t.mark.attrs,n,{strict:!1}))).reduce(((t,e)=>t+e.to-e.from),0),c=a.filter((t=>!i||t.mark.type!==i&&t.mark.type.excludes(i))).reduce(((t,e)=>t+e.to-e.from),0);return(l>0?l+c:l)>=s}(r,s,e)?o.unsetMark(s,{extendEmptyMarkRange:i}):o.setMark(s,e)},toggleNode:(t,e,n={})=>({state:r,commands:o})=>{const i=F(t,r.schema),s=F(e,r.schema);return vt(r,i,n)?o.setNode(s):o.setNode(i,n)},toggleWrap:(t,e={})=>({state:n,commands:r})=>{const o=F(t,n.schema);return vt(n,o,e)?r.lift(o):r.wrapIn(o,e)},undoInputRule:()=>({state:t,dispatch:e})=>{const n=t.plugins;for(let r=0;r<n.length;r+=1){const o=n[r];let i;if(o.spec.isInputRules&&(i=o.getState(t))){if(e){const e=t.tr,n=i.transform;for(let t=n.steps.length-1;t>=0;t-=1)e.step(n.steps[t].invert(n.docs[t]));if(i.text){const n=e.doc.resolve(i.from).marks();e.replaceWith(i.from,i.to,t.schema.text(i.text,n))}else e.delete(i.from,i.to)}return!0}}return!1},unsetAllMarks:()=>({tr:t,dispatch:e})=>{const{selection:n}=t,{empty:r,ranges:o}=n;return r||e&&o.forEach((e=>{t.removeMark(e.$from.pos,e.$to.pos)})),!0},unsetMark:(t,e={})=>({tr:n,state:r,dispatch:o})=>{var i;const{extendEmptyMarkRange:s=!1}=e,{selection:a}=n,l=dt(t,r.schema),{$from:c,empty:d,ranges:u}=a;if(!o)return!0;if(d&&s){let{from:t,to:e}=a;const r=null===(i=c.marks().find((t=>t.type===l)))||void 0===i?void 0:i.attrs,o=ct(c,l,r);o&&(t=o.from,e=o.to),n.removeMark(t,e,l)}else u.forEach((t=>{n.removeMark(t.$from.pos,t.$to.pos,l)}));return n.removeStoredMark(l),!0},updateAttributes:(t,e={})=>({tr:n,state:r,dispatch:o})=>{let i=null,s=null;const a=bt("string"==typeof t?t:t.name,r.schema);return!!a&&("node"===a&&(i=F(t,r.schema)),"mark"===a&&(s=dt(t,r.schema)),o&&n.selection.ranges.forEach((t=>{const o=t.$from.pos,a=t.$to.pos;r.doc.nodesBetween(o,a,((t,r)=>{i&&i===t.type&&n.setNodeMarkup(r,void 0,{...t.attrs,...e}),s&&t.marks.length&&t.marks.forEach((i=>{if(s===i.type){const l=Math.max(r,o),c=Math.min(r+t.nodeSize,a);n.addMark(l,c,s.create({...i.attrs,...e}))}}))}))})),!0)},wrapIn:(t,e={})=>({state:n,dispatch:r})=>function(t,e=null){return function(n,r){let{$from:o,$to:i}=n.selection,a=o.blockRange(i),l=a&&(0,s.oM)(a,t,e);return!!l&&(r&&r(n.tr.wrap(a,l).scrollIntoView()),!0)}}(F(t,n.schema),e)(n,r),wrapInList:(t,e={})=>({state:n,dispatch:r})=>function(t,e=null){return function(n,r){let{$from:o,$to:a}=n.selection,l=o.blockRange(a),c=!1,d=l;if(!l)return!1;if(l.depth>=2&&o.node(l.depth-1).type.compatibleContent(t)&&0==l.startIndex){if(0==o.index(l.depth-1))return!1;let t=n.doc.resolve(l.start-2);d=new i.u$(t,t,l.depth),l.endIndex<l.parent.childCount&&(l=new i.u$(o,n.doc.resolve(a.end(l.depth)),l.depth)),c=!0}let u=(0,s.oM)(d,t,e,l);return!!u&&(r&&r(function(t,e,n,r,o){let a=i.FK.empty;for(let t=n.length-1;t>=0;t--)a=i.FK.from(n[t].type.create(n[t].attrs,a));t.step(new s.Wg(e.start-(r?2:0),e.end,e.start,e.end,new i.Ji(a,0,0),n.length,!0));let l=0;for(let t=0;t<n.length;t++)n[t].type==o&&(l=t+1);let c=n.length-l,d=e.start+n.length-(r?2:0),u=e.parent;for(let n=e.startIndex,r=e.endIndex,o=!0;n<r;n++,o=!1)!o&&(0,s.zy)(t.doc,d,c)&&(t.split(d,c),d+=2*c),d+=u.child(n).nodeSize;return t}(n.tr,l,u,c,t).scrollIntoView()),!0)}}(F(t,n.schema),e)(n,r)});function Pt(t){return new G({find:t.find,handler:({state:e,range:n,match:r})=>{const o=H(t.getAttributes,void 0,r);if(!1===o||null===o)return null;const{tr:i}=e,s=r[r.length-1],a=r[0];if(s){const r=a.search(/\S/),l=n.from+a.indexOf(s),c=l+s.length;if(Ot(n.from,n.to,e.doc).filter((e=>e.mark.type.excluded.find((n=>n===t.type&&n!==e.mark.type)))).filter((t=>t.to>l)).length)return null;c<n.to&&i.delete(c,n.to),l>n.from&&i.delete(n.from+r,l);const d=n.from+r+s.length;i.addMark(n.from+r,d,t.type.create(o||{})),i.removeStoredMark(t.type)}}})}function Dt(t){return new G({find:t.find,handler:({state:e,range:n,match:r})=>{const o=H(t.getAttributes,void 0,r)||{},{tr:i}=e,s=n.from;let a=n.to;const l=t.type.create(o);if(r[1]){let t=s+r[0].lastIndexOf(r[1]);t>a?t=a:a=t+r[1].length;const e=r[0][r[0].length-1];i.insertText(e,s+r[0].length-1),i.replaceWith(t,a,l)}else r[0]&&i.insert(s-1,t.type.create(o)).delete(i.mapping.map(s),i.mapping.map(a));i.scrollIntoView()}})}function Lt(t){return new G({find:t.find,handler:({state:e,range:n,match:r})=>{const o=e.doc.resolve(n.from),i=H(t.getAttributes,void 0,r)||{};if(!o.node(-1).canReplaceWith(o.index(-1),o.indexAfter(-1),t.type))return null;e.tr.delete(n.from,n.to).setBlockType(n.from,n.from,t.type,i)}})}function It(t){return new G({find:t.find,handler:({state:e,range:n,match:r,chain:o})=>{const i=H(t.getAttributes,void 0,r)||{},a=e.tr.delete(n.from,n.to),l=a.doc.resolve(n.from).blockRange(),c=l&&(0,s.oM)(l,t.type,i);if(!c)return null;if(a.wrap(l,c),t.keepMarks&&t.editor){const{selection:n,storedMarks:r}=e,{splittableMarks:o}=t.editor.extensionManager,i=r||n.$to.parentOffset&&n.$from.marks();if(i){const t=i.filter((t=>o.includes(t.type.name)));a.ensureMarks(t)}}if(t.keepAttributes){const e="bulletList"===t.type.name||"orderedList"===t.type.name?"listItem":"taskList";o().updateAttributes(e,i).run()}const d=a.doc.resolve(n.from-1).nodeBefore;d&&d.type===t.type&&(0,s.n9)(a.doc,n.from-1)&&(!t.joinPredicate||t.joinPredicate(r,d))&&a.join(n.from-1)}})}it.create({name:"commands",addCommands:()=>({...zt})}),it.create({name:"editable",addProseMirrorPlugins(){return[new r.k_({key:new r.hs("editable"),props:{editable:()=>this.editor.options.editable}})]}}),it.create({name:"focusEvents",addProseMirrorPlugins(){const{editor:t}=this;return[new r.k_({key:new r.hs("focusEvents"),props:{handleDOMEvents:{focus:(e,n)=>{t.isFocused=!0;const r=t.state.tr.setMeta("focus",{event:n}).setMeta("addToHistory",!1);return e.dispatch(r),!1},blur:(e,n)=>{t.isFocused=!1;const r=t.state.tr.setMeta("blur",{event:n}).setMeta("addToHistory",!1);return e.dispatch(r),!1}}}})]}}),it.create({name:"keymap",addKeyboardShortcuts(){const t=()=>this.editor.commands.first((({commands:t})=>[()=>t.undoInputRule(),()=>t.command((({tr:e})=>{const{selection:n,doc:o}=e,{empty:i,$anchor:s}=n,{pos:a,parent:l}=s,c=s.parent.isTextblock&&a>0?e.doc.resolve(a-1):s,d=c.parent.type.spec.isolating,u=s.pos-s.parentOffset,h=d&&1===c.parent.childCount?u===s.pos:r.LN.atStart(o).from===a;return!(!i||!l.type.isTextblock||l.textContent.length||!h||h&&"paragraph"===s.parent.type.name)&&t.clearNodes()})),()=>t.deleteSelection(),()=>t.joinBackward(),()=>t.selectNodeBackward()])),e=()=>this.editor.commands.first((({commands:t})=>[()=>t.deleteSelection(),()=>t.deleteCurrentNode(),()=>t.joinForward(),()=>t.selectNodeForward()])),n={Enter:()=>this.editor.commands.first((({commands:t})=>[()=>t.newlineInCode(),()=>t.createParagraphNear(),()=>t.liftEmptyBlock(),()=>t.splitBlock()])),"Mod-Enter":()=>this.editor.commands.exitCode(),Backspace:t,"Mod-Backspace":t,"Shift-Backspace":t,Delete:e,"Mod-Delete":e,"Mod-a":()=>this.editor.commands.selectAll()},o={...n},i={...n,"Ctrl-h":t,"Alt-Backspace":t,"Ctrl-d":e,"Ctrl-Alt-Backspace":e,"Alt-Delete":e,"Alt-d":e,"Ctrl-a":()=>this.editor.commands.selectTextblockStart(),"Ctrl-e":()=>this.editor.commands.selectTextblockEnd()};return pt()||yt()?i:o},addProseMirrorPlugins(){return[new r.k_({key:new r.hs("clearDocument"),appendTransaction:(t,e,n)=>{if(!t.some((t=>t.docChanged))||e.doc.eq(n.doc))return;const{empty:o,from:i,to:s}=e.selection,a=r.LN.atStart(e.doc).from,l=r.LN.atEnd(e.doc).to;if(o||i!==a||s!==l)return;if(0!==n.doc.textBetween(0,n.doc.content.size," "," ").length)return;const c=n.tr,d=z({state:n,transaction:c}),{commands:u}=new P({editor:this.editor,state:d});return u.clearNodes(),c.steps.length?c:void 0}})]}}),it.create({name:"tabindex",addProseMirrorPlugins(){return[new r.k_({key:new r.hs("tabindex"),props:{attributes:this.editor.isEditable?{tabindex:"0"}:{}}})]}});class Ft{constructor(t={}){this.type="mark",this.name="mark",this.parent=null,this.child=null,this.config={name:this.name,defaultOptions:{}},this.config={...this.config,...t},this.name=this.config.name,t.defaultOptions&&Object.keys(t.defaultOptions).length>0&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${this.name}".`),this.options=this.config.defaultOptions,this.config.addOptions&&(this.options=H(D(this,"addOptions",{name:this.name}))),this.storage=H(D(this,"addStorage",{name:this.name,options:this.options}))||{}}static create(t={}){return new Ft(t)}configure(t={}){const e=this.extend();return e.options=ot(this.options,t),e.storage=H(D(e,"addStorage",{name:e.name,options:e.options})),e}extend(t={}){const e=new Ft({...this.config,...t});return e.parent=this,this.child=e,e.name=t.name?t.name:e.parent.name,t.defaultOptions&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${e.name}".`),e.options=H(D(e,"addOptions",{name:e.name})),e.storage=H(D(e,"addStorage",{name:e.name,options:e.options})),e}static handleExit({editor:t,mark:e}){const{tr:n}=t.state,r=t.state.selection.$from;if(r.pos===r.end()){const o=r.marks();if(!o.find((t=>(null==t?void 0:t.type.name)===e.name)))return!1;const i=o.find((t=>(null==t?void 0:t.type.name)===e.name));return i&&n.removeStoredMark(i),n.insertText(" ",r.pos),t.view.dispatch(n),!0}return!1}}class jt{constructor(t={}){this.type="node",this.name="node",this.parent=null,this.child=null,this.config={name:this.name,defaultOptions:{}},this.config={...this.config,...t},this.name=this.config.name,t.defaultOptions&&Object.keys(t.defaultOptions).length>0&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${this.name}".`),this.options=this.config.defaultOptions,this.config.addOptions&&(this.options=H(D(this,"addOptions",{name:this.name}))),this.storage=H(D(this,"addStorage",{name:this.name,options:this.options}))||{}}static create(t={}){return new jt(t)}configure(t={}){const e=this.extend();return e.options=ot(this.options,t),e.storage=H(D(e,"addStorage",{name:e.name,options:e.options})),e}extend(t={}){const e=new jt({...this.config,...t});return e.parent=this,this.child=e,e.name=t.name?t.name:e.parent.name,t.defaultOptions&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${e.name}".`),e.options=H(D(e,"addOptions",{name:e.name})),e.storage=H(D(e,"addStorage",{name:e.name,options:e.options})),e}}function Bt(t){return new tt({find:t.find,handler:({state:e,range:n,match:r,pasteEvent:o})=>{const i=H(t.getAttributes,void 0,r,o);if(!1===i||null===i)return null;const{tr:s}=e,a=r[r.length-1],l=r[0];let c=n.to;if(a){const r=l.search(/\S/),o=n.from+l.indexOf(a),d=o+a.length;if(Ot(n.from,n.to,e.doc).filter((e=>e.mark.type.excluded.find((n=>n===t.type&&n!==e.mark.type)))).filter((t=>t.to>o)).length)return null;d<n.to&&s.delete(d,n.to),o>n.from&&s.delete(n.from+r,o),c=n.from+r+a.length,s.addMark(n.from+r,c,t.type.create(i||{})),s.removeStoredMark(t.type)}}})}},4717:(t,e,n)=>{"use strict";n.d(e,{A:()=>r}),n(31);const r=n(4198).YY.create({name:"color",addOptions:()=>({types:["textStyle"]}),addGlobalAttributes(){return[{types:this.options.types,attributes:{color:{default:null,parseHTML:t=>{var e;return null===(e=t.style.color)||void 0===e?void 0:e.replace(/['"]+/g,"")},renderHTML:t=>t.color?{style:`color: ${t.color}`}:{}}}}]},addCommands:()=>({setColor:t=>({chain:e})=>e().setMark("textStyle",{color:t}).run(),unsetColor:()=>({chain:t})=>t().setMark("textStyle",{color:null}).removeEmptyTextStyle().run()})})},3004:(t,e,n)=>{"use strict";n.d(e,{Ay:()=>s});var r=n(4198);const o=/(?:^|\s)(==(?!\s+==)((?:[^=]+))==(?!\s+==))$/,i=/(?:^|\s)(==(?!\s+==)((?:[^=]+))==(?!\s+==))/g,s=r.CU.create({name:"highlight",addOptions:()=>({multicolor:!1,HTMLAttributes:{}}),addAttributes(){return this.options.multicolor?{color:{default:null,parseHTML:t=>t.getAttribute("data-color")||t.style.backgroundColor,renderHTML:t=>t.color?{"data-color":t.color,style:`background-color: ${t.color}; color: inherit`}:{}}}:{}},parseHTML:()=>[{tag:"mark"}],renderHTML({HTMLAttributes:t}){return["mark",(0,r.KV)(this.options.HTMLAttributes,t),0]},addCommands(){return{setHighlight:t=>({commands:e})=>e.setMark(this.name,t),toggleHighlight:t=>({commands:e})=>e.toggleMark(this.name,t),unsetHighlight:()=>({commands:t})=>t.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-Shift-h":()=>this.editor.commands.toggleHighlight()}},addInputRules(){return[(0,r.OX)({find:o,type:this.type})]},addPasteRules(){return[(0,r.Zc)({find:i,type:this.type})]}})},4758:(t,e,n)=>{"use strict";n.d(e,{B:()=>A,A:()=>A});var r=n(4198),o=200,i=function(){};i.prototype.append=function(t){return t.length?(t=i.from(t),!this.length&&t||t.length<o&&this.leafAppend(t)||this.length<o&&t.leafPrepend(this)||this.appendInner(t)):this},i.prototype.prepend=function(t){return t.length?i.from(t).append(this):this},i.prototype.appendInner=function(t){return new a(this,t)},i.prototype.slice=function(t,e){return void 0===t&&(t=0),void 0===e&&(e=this.length),t>=e?i.empty:this.sliceInner(Math.max(0,t),Math.min(this.length,e))},i.prototype.get=function(t){if(!(t<0||t>=this.length))return this.getInner(t)},i.prototype.forEach=function(t,e,n){void 0===e&&(e=0),void 0===n&&(n=this.length),e<=n?this.forEachInner(t,e,n,0):this.forEachInvertedInner(t,e,n,0)},i.prototype.map=function(t,e,n){void 0===e&&(e=0),void 0===n&&(n=this.length);var r=[];return this.forEach((function(e,n){return r.push(t(e,n))}),e,n),r},i.from=function(t){return t instanceof i?t:t&&t.length?new s(t):i.empty};var s=function(t){function e(e){t.call(this),this.values=e}t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e;var n={length:{configurable:!0},depth:{configurable:!0}};return e.prototype.flatten=function(){return this.values},e.prototype.sliceInner=function(t,n){return 0==t&&n==this.length?this:new e(this.values.slice(t,n))},e.prototype.getInner=function(t){return this.values[t]},e.prototype.forEachInner=function(t,e,n,r){for(var o=e;o<n;o++)if(!1===t(this.values[o],r+o))return!1},e.prototype.forEachInvertedInner=function(t,e,n,r){for(var o=e-1;o>=n;o--)if(!1===t(this.values[o],r+o))return!1},e.prototype.leafAppend=function(t){if(this.length+t.length<=o)return new e(this.values.concat(t.flatten()))},e.prototype.leafPrepend=function(t){if(this.length+t.length<=o)return new e(t.flatten().concat(this.values))},n.length.get=function(){return this.values.length},n.depth.get=function(){return 0},Object.defineProperties(e.prototype,n),e}(i);i.empty=new s([]);var a=function(t){function e(e,n){t.call(this),this.left=e,this.right=n,this.length=e.length+n.length,this.depth=Math.max(e.depth,n.depth)+1}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype.flatten=function(){return this.left.flatten().concat(this.right.flatten())},e.prototype.getInner=function(t){return t<this.left.length?this.left.get(t):this.right.get(t-this.left.length)},e.prototype.forEachInner=function(t,e,n,r){var o=this.left.length;return!(e<o&&!1===this.left.forEachInner(t,e,Math.min(n,o),r))&&!(n>o&&!1===this.right.forEachInner(t,Math.max(e-o,0),Math.min(this.length,n)-o,r+o))&&void 0},e.prototype.forEachInvertedInner=function(t,e,n,r){var o=this.left.length;return!(e>o&&!1===this.right.forEachInvertedInner(t,e-o,Math.max(n,o)-o,r+o))&&!(n<o&&!1===this.left.forEachInvertedInner(t,Math.min(e,o),n,r))&&void 0},e.prototype.sliceInner=function(t,e){if(0==t&&e==this.length)return this;var n=this.left.length;return e<=n?this.left.slice(t,e):t>=n?this.right.slice(t-n,e-n):this.left.slice(t,n).append(this.right.slice(0,e-n))},e.prototype.leafAppend=function(t){var n=this.right.leafAppend(t);if(n)return new e(this.left,n)},e.prototype.leafPrepend=function(t){var n=this.left.leafPrepend(t);if(n)return new e(n,this.right)},e.prototype.appendInner=function(t){return this.left.depth>=Math.max(this.right.depth,t.depth)+1?new e(this.left,new e(this.right,t)):new e(this,t)},e}(i);const l=i;var c=n(196),d=n(2559);class u{constructor(t,e){this.items=t,this.eventCount=e}popEvent(t,e){if(0==this.eventCount)return null;let n,r,o=this.items.length;for(;;o--)if(this.items.get(o-1).selection){--o;break}e&&(n=this.remapping(o,this.items.length),r=n.maps.length);let i,s,a=t.tr,l=[],c=[];return this.items.forEach(((t,e)=>{if(!t.step)return n||(n=this.remapping(o,e+1),r=n.maps.length),r--,void c.push(t);if(n){c.push(new h(t.map));let e,o=t.step.map(n.slice(r));o&&a.maybeStep(o).doc&&(e=a.mapping.maps[a.mapping.maps.length-1],l.push(new h(e,void 0,void 0,l.length+c.length))),r--,e&&n.appendMap(e,r)}else a.maybeStep(t.step);return t.selection?(i=n?t.selection.map(n.slice(r)):t.selection,s=new u(this.items.slice(0,o).append(c.reverse().concat(l)),this.eventCount-1),!1):void 0}),this.items.length,0),{remaining:s,transform:a,selection:i}}addTransform(t,e,n,r){let o=[],i=this.eventCount,s=this.items,a=!r&&s.length?s.get(s.length-1):null;for(let n=0;n<t.steps.length;n++){let l,c=t.steps[n].invert(t.docs[n]),d=new h(t.mapping.maps[n],c,e);(l=a&&a.merge(d))&&(d=l,n?o.pop():s=s.slice(0,s.length-1)),o.push(d),e&&(i++,e=void 0),r||(a=d)}let l=i-n.depth;return l>f&&(s=function(t,e){let n;return t.forEach(((t,r)=>{if(t.selection&&0==e--)return n=r,!1})),t.slice(n)}(s,l),i-=l),new u(s.append(o),i)}remapping(t,e){let n=new c.X9;return this.items.forEach(((e,r)=>{let o=null!=e.mirrorOffset&&r-e.mirrorOffset>=t?n.maps.length-e.mirrorOffset:void 0;n.appendMap(e.map,o)}),t,e),n}addMaps(t){return 0==this.eventCount?this:new u(this.items.append(t.map((t=>new h(t)))),this.eventCount)}rebased(t,e){if(!this.eventCount)return this;let n=[],r=Math.max(0,this.items.length-e),o=t.mapping,i=t.steps.length,s=this.eventCount;this.items.forEach((t=>{t.selection&&s--}),r);let a=e;this.items.forEach((e=>{let r=o.getMirror(--a);if(null==r)return;i=Math.min(i,r);let l=o.maps[r];if(e.step){let i=t.steps[r].invert(t.docs[r]),c=e.selection&&e.selection.map(o.slice(a+1,r));c&&s++,n.push(new h(l,i,c))}else n.push(new h(l))}),r);let l=[];for(let t=e;t<i;t++)l.push(new h(o.maps[t]));let c=this.items.slice(0,r).append(l).append(n),d=new u(c,s);return d.emptyItemCount()>500&&(d=d.compress(this.items.length-n.length)),d}emptyItemCount(){let t=0;return this.items.forEach((e=>{e.step||t++})),t}compress(t=this.items.length){let e=this.remapping(0,t),n=e.maps.length,r=[],o=0;return this.items.forEach(((i,s)=>{if(s>=t)r.push(i),i.selection&&o++;else if(i.step){let t=i.step.map(e.slice(n)),s=t&&t.getMap();if(n--,s&&e.appendMap(s,n),t){let a=i.selection&&i.selection.map(e.slice(n));a&&o++;let l,c=new h(s.invert(),t,a),d=r.length-1;(l=r.length&&r[d].merge(c))?r[d]=l:r.push(c)}}else i.map&&n--}),this.items.length,0),new u(l.from(r.reverse()),o)}}u.empty=new u(l.empty,0);class h{constructor(t,e,n,r){this.map=t,this.step=e,this.selection=n,this.mirrorOffset=r}merge(t){if(this.step&&t.step&&!t.selection){let e=t.step.merge(this.step);if(e)return new h(e.getMap().invert(),e,this.selection)}}}class p{constructor(t,e,n,r,o){this.done=t,this.undone=e,this.prevRanges=n,this.prevTime=r,this.prevComposition=o}}const f=20;function m(t){let e=[];return t.forEach(((t,n,r,o)=>e.push(r,o))),e}function g(t,e){if(!t)return null;let n=[];for(let r=0;r<t.length;r+=2){let o=e.map(t[r],1),i=e.map(t[r+1],-1);o<=i&&n.push(o,i)}return n}let y=!1,v=null;function b(t){let e=t.plugins;if(v!=e){y=!1,v=e;for(let t=0;t<e.length;t++)if(e[t].spec.historyPreserveItems){y=!0;break}}return y}const w=new d.hs("history"),x=new d.hs("closeHistory");function k(t={}){return t={depth:t.depth||100,newGroupDelay:t.newGroupDelay||500},new d.k_({key:w,state:{init:()=>new p(u.empty,u.empty,null,0,-1),apply:(e,n,r)=>function(t,e,n,r){let o,i=n.getMeta(w);if(i)return i.historyState;n.getMeta(x)&&(t=new p(t.done,t.undone,null,0,-1));let s=n.getMeta("appendedTransaction");if(0==n.steps.length)return t;if(s&&s.getMeta(w))return s.getMeta(w).redo?new p(t.done.addTransform(n,void 0,r,b(e)),t.undone,m(n.mapping.maps[n.steps.length-1]),t.prevTime,t.prevComposition):new p(t.done,t.undone.addTransform(n,void 0,r,b(e)),null,t.prevTime,t.prevComposition);if(!1===n.getMeta("addToHistory")||s&&!1===s.getMeta("addToHistory"))return(o=n.getMeta("rebased"))?new p(t.done.rebased(n,o),t.undone.rebased(n,o),g(t.prevRanges,n.mapping),t.prevTime,t.prevComposition):new p(t.done.addMaps(n.mapping.maps),t.undone.addMaps(n.mapping.maps),g(t.prevRanges,n.mapping),t.prevTime,t.prevComposition);{let o=n.getMeta("composition"),i=0==t.prevTime||!s&&t.prevComposition!=o&&(t.prevTime<(n.time||0)-r.newGroupDelay||!function(t,e){if(!e)return!1;if(!t.docChanged)return!0;let n=!1;return t.mapping.maps[0].forEach(((t,r)=>{for(let o=0;o<e.length;o+=2)t<=e[o+1]&&r>=e[o]&&(n=!0)})),n}(n,t.prevRanges)),a=s?g(t.prevRanges,n.mapping):m(n.mapping.maps[n.steps.length-1]);return new p(t.done.addTransform(n,i?e.selection.getBookmark():void 0,r,b(e)),u.empty,a,n.time,null==o?t.prevComposition:o)}}(n,r,e,t)},config:t,props:{handleDOMEvents:{beforeinput(t,e){let n=e.inputType,r="historyUndo"==n?M:"historyRedo"==n?C:null;return!!r&&(e.preventDefault(),r(t.state,t.dispatch))}}}})}function S(t,e){return(n,r)=>{let o=w.getState(n);if(!o||0==(t?o.undone:o.done).eventCount)return!1;if(r){let i=function(t,e,n){let r=b(e),o=w.get(e).spec.config,i=(n?t.undone:t.done).popEvent(e,r);if(!i)return null;let s=i.selection.resolve(i.transform.doc),a=(n?t.done:t.undone).addTransform(i.transform,e.selection.getBookmark(),o,r),l=new p(n?a:i.remaining,n?i.remaining:a,null,0,-1);return i.transform.setSelection(s).setMeta(w,{redo:n,historyState:l})}(o,n,t);i&&r(e?i.scrollIntoView():i)}return!0}}const M=S(!1,!0),C=S(!0,!0);S(!1,!1),S(!0,!1);const A=r.YY.create({name:"history",addOptions:()=>({depth:100,newGroupDelay:500}),addCommands:()=>({undo:()=>({state:t,dispatch:e})=>M(t,e),redo:()=>({state:t,dispatch:e})=>C(t,e)}),addProseMirrorPlugins(){return[k(this.options)]},addKeyboardShortcuts(){return{"Mod-z":()=>this.editor.commands.undo(),"Shift-Mod-z":()=>this.editor.commands.redo(),"Mod-y":()=>this.editor.commands.redo(),"Mod-я":()=>this.editor.commands.undo(),"Shift-Mod-я":()=>this.editor.commands.redo()}}})},1212:(t,e,n)=>{"use strict";n.d(e,{Ay:()=>de});var r=n(4198);const o="aaa1rp3bb0ott3vie4c1le2ogado5udhabi7c0ademy5centure6ountant0s9o1tor4d0s1ult4e0g1ro2tna4f0l1rica5g0akhan5ency5i0g1rbus3force5tel5kdn3l0ibaba4pay4lfinanz6state5y2sace3tom5m0azon4ericanexpress7family11x2fam3ica3sterdam8nalytics7droid5quan4z2o0l2partments8p0le4q0uarelle8r0ab1mco4chi3my2pa2t0e3s0da2ia2sociates9t0hleta5torney7u0ction5di0ble3o3spost5thor3o0s4w0s2x0a2z0ure5ba0by2idu3namex4d1k2r0celona5laycard4s5efoot5gains6seball5ketball8uhaus5yern5b0c1t1va3cg1n2d1e0ats2uty4er2rlin4st0buy5t2f1g1h0arti5i0ble3d1ke2ng0o3o1z2j1lack0friday9ockbuster8g1omberg7ue3m0s1w2n0pparibas9o0ats3ehringer8fa2m1nd2o0k0ing5sch2tik2on4t1utique6x2r0adesco6idgestone9oadway5ker3ther5ussels7s1t1uild0ers6siness6y1zz3v1w1y1z0h3ca0b1fe2l0l1vinklein9m0era3p2non3petown5ital0one8r0avan4ds2e0er0s4s2sa1e1h1ino4t0ering5holic7ba1n1re3c1d1enter4o1rn3f0a1d2g1h0anel2nel4rity4se2t2eap3intai5ristmas6ome4urch5i0priani6rcle4sco3tadel4i0c2y3k1l0aims4eaning6ick2nic1que6othing5ud3ub0med6m1n1o0ach3des3ffee4llege4ogne5m0mbank4unity6pany2re3uter5sec4ndos3struction8ulting7tact3ractors9oking4l1p2rsica5untry4pon0s4rses6pa2r0edit0card4union9icket5own3s1uise0s6u0isinella9v1w1x1y0mru3ou3z2dad1nce3ta1e1ing3sun4y2clk3ds2e0al0er2s3gree4livery5l1oitte5ta3mocrat6ntal2ist5si0gn4v2hl2iamonds6et2gital5rect0ory7scount3ver5h2y2j1k1m1np2o0cs1tor4g1mains5t1wnload7rive4tv2ubai3nlop4pont4rban5vag2r2z2earth3t2c0o2deka3u0cation8e1g1mail3erck5nergy4gineer0ing9terprises10pson4quipment8r0icsson6ni3s0q1tate5t1u0rovision8s2vents5xchange6pert3osed4ress5traspace10fage2il1rwinds6th3mily4n0s2rm0ers5shion4t3edex3edback6rrari3ero6i0delity5o2lm2nal1nce1ial7re0stone6mdale6sh0ing5t0ness6j1k1lickr3ghts4r2orist4wers5y2m1o0o0d1tball6rd1ex2sale4um3undation8x2r0ee1senius7l1ogans4ntier7tr2ujitsu5n0d2rniture7tbol5yi3ga0l0lery3o1up4me0s3p1rden4y2b0iz3d0n2e0a1nt0ing5orge5f1g0ee3h1i0ft0s3ves2ing5l0ass3e1obal2o4m0ail3bh2o1x2n1odaddy5ld0point6f2o0dyear5g0le4p1t1v2p1q1r0ainger5phics5tis4een3ipe3ocery4up4s1t1u0cci3ge2ide2tars5ru3w1y2hair2mburg5ngout5us3bo2dfc0bank7ealth0care8lp1sinki6re1mes5iphop4samitsu7tachi5v2k0t2m1n1ockey4ldings5iday5medepot5goods5s0ense7nda3rse3spital5t0ing5t0els3mail5use3w2r1sbc3t1u0ghes5yatt3undai7ibm2cbc2e1u2d1e0ee3fm2kano4l1m0amat4db2mo0bilien9n0c1dustries8finiti5o2g1k1stitute6urance4e4t0ernational10uit4vestments10o1piranga7q1r0ish4s0maili5t0anbul7t0au2v3jaguar4va3cb2e0ep2tzt3welry6io2ll2m0p2nj2o0bs1urg4t1y2p0morgan6rs3uegos4niper7kaufen5ddi3e0rryhotels6properties14fh2g1h1i0a1ds2m1ndle4tchen5wi3m1n1oeln3matsu5sher5p0mg2n2r0d1ed3uokgroup8w1y0oto4z2la0caixa5mborghini8er3nd0rover6xess5salle5t0ino3robe5w0yer5b1c1ds2ease3clerc5frak4gal2o2xus4gbt3i0dl2fe0insurance9style7ghting6ke2lly3mited4o2ncoln4k2ve1ing5k1lc1p2oan0s3cker3us3l1ndon4tte1o3ve3pl0financial11r1s1t0d0a3u0ndbeck6xe1ury5v1y2ma0drid4if1son4keup4n0agement7go3p1rket0ing3s4riott5shalls7ttel5ba2c0kinsey7d1e0d0ia3et2lbourne7me1orial6n0u2rckmsd7g1h1iami3crosoft7l1ni1t2t0subishi9k1l0b1s2m0a2n1o0bi0le4da2e1i1m1nash3ey2ster5rmon3tgage6scow4to0rcycles9v0ie4p1q1r1s0d2t0n1r2u0seum3ic4v1w1x1y1z2na0b1goya4me2vy3ba2c1e0c1t0bank4flix4work5ustar5w0s2xt0direct7us4f0l2g0o2hk2i0co2ke1on3nja3ssan1y5l1o0kia3rton4w0ruz3tv4p1r0a1w2tt2u1yc2z2obi1server7ffice5kinawa6layan0group9lo3m0ega4ne1g1l0ine5oo2pen3racle3nge4g0anic5igins6saka4tsuka4t2vh3pa0ge2nasonic7ris2s1tners4s1y3y2ccw3e0t2f0izer5g1h0armacy6d1ilips5one2to0graphy6s4ysio5ics1tet2ures6d1n0g1k2oneer5zza4k1l0ace2y0station9umbing5s3m1n0c2ohl2ker3litie5rn2st3r0america6xi3ess3ime3o0d0uctions8f1gressive8mo2perties3y5tection8u0dential9s1t1ub2w0c2y2qa1pon3uebec3st5racing4dio4e0ad1lestate6tor2y4cipes5d0stone5umbrella9hab3ise0n3t2liance6n0t0als5pair3ort3ublican8st0aurant8view0s5xroth6ich0ardli6oh3l1o1p2o0cks3deo3gers4om3s0vp3u0gby3hr2n2w0e2yukyu6sa0arland6fe0ty4kura4le1on3msclub4ung5ndvik0coromant12ofi4p1rl2s1ve2xo3b0i1s2c0b1haeffler7midt4olarships8ol3ule3warz5ience5ot3d1e0arch3t2cure1ity6ek2lect4ner3rvices6ven3w1x0y3fr2g1h0angrila6rp3ell3ia1ksha5oes2p0ping5uji3w3i0lk2na1gles5te3j1k0i0n2y0pe4l0ing4m0art3ile4n0cf3o0ccer3ial4ftbank4ware6hu2lar2utions7ng1y2y2pa0ce3ort2t3r0l2s1t0ada2ples4r1tebank4farm7c0group6ockholm6rage3e3ream4udio2y3yle4u0cks3pplies3y2ort5rf1gery5zuki5v1watch4iss4x1y0dney4stems6z2tab1ipei4lk2obao4rget4tamotors6r2too4x0i3c0i2d0k2eam2ch0nology8l1masek5nnis4va3f1g1h0d1eater2re6iaa2ckets5enda4ps2res2ol4j0maxx4x2k0maxx5l1m0all4n1o0day3kyo3ols3p1ray3shiba5tal3urs3wn2yota3s3r0ade1ing4ining5vel0ers0insurance16ust3v2t1ube2i1nes3shu4v0s2w1z2ua1bank3s2g1k1nicom3versity8o2ol2ps2s1y1z2va0cations7na1guard7c1e0gas3ntures6risign5mögensberater2ung14sicherung10t2g1i0ajes4deo3g1king4llas4n1p1rgin4sa1ion4va1o3laanderen9n1odka3lvo3te1ing3o2yage5u2wales2mart4ter4ng0gou5tch0es6eather0channel12bcam3er2site5d0ding5ibo2r3f1hoswho6ien2ki2lliamhill9n0dows4e1ners6me2olterskluwer11odside6rk0s2ld3w2s1tc1f3xbox3erox4ihuan4n2xx2yz3yachts4hoo3maxun5ndex5e1odobashi7ga2kohama6u0tube6t1un3za0ppos4ra3ero3ip2m1one3uerich6w2",i="ελ1υ2бг1ел3дети4ею2католик6ом3мкд2он1сква6онлайн5рг3рус2ф2сайт3рб3укр3қаз3հայ3ישראל5קום3ابوظبي5رامكو5لاردن4بحرين5جزائر5سعودية6عليان5مغرب5مارات5یران5بارت2زار4يتك3ھارت5تونس4سودان3رية5شبكة4عراق2ب2مان4فلسطين6قطر3كاثوليك6وم3مصر2ليسيا5وريتانيا7قع4همراه5پاکستان7ڀارت4कॉम3नेट3भारत0म्3ोत5संगठन5বাংলা5ভারত2ৰত4ਭਾਰਤ4ભારત4ଭାରତ4இந்தியா6லங்கை6சிங்கப்பூர்11భారత్5ಭಾರತ4ഭാരതം5ලංකා4คอม3ไทย3ລາວ3გე2みんな3アマゾン4クラウド4グーグル4コム2ストア3セール3ファッション6ポイント4世界2中信1国1國1文网3亚马逊3企业2佛山2信息2健康2八卦2公司1益2台湾1灣2商城1店1标2嘉里0大酒店5在线2大拿2天主教3娱乐2家電2广东2微博2慈善2我爱你3手机2招聘2政务1府2新加坡2闻2时尚2書籍2机构2淡马锡3游戏2澳門2点看2移动2组织机构4网址1店1站1络2联通2谷歌2购物2通販2集团2電訊盈科4飞利浦3食品2餐厅2香格里拉3港2닷넷1컴2삼성2한국2",s=(t,e)=>{for(const n in e)t[n]=e[n];return t},a="numeric",l="ascii",c="alpha",d="asciinumeric",u="alphanumeric",h="domain",p="emoji",f="scheme",m="slashscheme",g="whitespace";function y(t,e){return t in e||(e[t]=[]),e[t]}function v(t,e,n){e[a]&&(e[d]=!0,e[u]=!0),e[l]&&(e[d]=!0,e[c]=!0),e[d]&&(e[u]=!0),e[c]&&(e[u]=!0),e[u]&&(e[h]=!0),e[p]&&(e[h]=!0);for(const r in e){const e=y(r,n);e.indexOf(t)<0&&e.push(t)}}function b(t=null){this.j={},this.jr=[],this.jd=null,this.t=t}b.groups={},b.prototype={accepts(){return!!this.t},go(t){const e=this,n=e.j[t];if(n)return n;for(let n=0;n<e.jr.length;n++){const r=e.jr[n][0],o=e.jr[n][1];if(o&&r.test(t))return o}return e.jd},has(t,e=!1){return e?t in this.j:!!this.go(t)},ta(t,e,n,r){for(let o=0;o<t.length;o++)this.tt(t[o],e,n,r)},tr(t,e,n,r){let o;return r=r||b.groups,e&&e.j?o=e:(o=new b(e),n&&r&&v(e,n,r)),this.jr.push([t,o]),o},ts(t,e,n,r){let o=this;const i=t.length;if(!i)return o;for(let e=0;e<i-1;e++)o=o.tt(t[e]);return o.tt(t[i-1],e,n,r)},tt(t,e,n,r){r=r||b.groups;const o=this;if(e&&e.j)return o.j[t]=e,e;const i=e;let a,l=o.go(t);if(l?(a=new b,s(a.j,l.j),a.jr.push.apply(a.jr,l.jr),a.jd=l.jd,a.t=l.t):a=new b,i){if(r)if(a.t&&"string"==typeof a.t){const t=s(function(t,e){const n={};for(const r in e)e[r].indexOf(t)>=0&&(n[r]=!0);return n}(a.t,r),n);v(i,t,r)}else n&&v(i,n,r);a.t=i}return o.j[t]=a,a}};const w=(t,e,n,r,o)=>t.ta(e,n,r,o),x=(t,e,n,r,o)=>t.tr(e,n,r,o),k=(t,e,n,r,o)=>t.ts(e,n,r,o),S=(t,e,n,r,o)=>t.tt(e,n,r,o),M="WORD",C="UWORD",A="ASCIINUMERICAL",T="ALPHANUMERICAL",O="LOCALHOST",E="TLD",_="UTLD",N="SCHEME",$="SLASH_SCHEME",R="NUM",z="WS",P="NL",D="OPENBRACE",L="CLOSEBRACE",I="OPENBRACKET",F="CLOSEBRACKET",j="OPENPAREN",B="CLOSEPAREN",H="OPENANGLEBRACKET",V="CLOSEANGLEBRACKET",K="FULLWIDTHLEFTPAREN",U="FULLWIDTHRIGHTPAREN",J="LEFTCORNERBRACKET",W="RIGHTCORNERBRACKET",q="LEFTWHITECORNERBRACKET",Z="RIGHTWHITECORNERBRACKET",G="FULLWIDTHLESSTHAN",Y="FULLWIDTHGREATERTHAN",X="AMPERSAND",Q="APOSTROPHE",tt="ASTERISK",et="AT",nt="BACKSLASH",rt="BACKTICK",ot="CARET",it="COLON",st="COMMA",at="DOLLAR",lt="DOT",ct="EQUALS",dt="EXCLAMATION",ut="HYPHEN",ht="PERCENT",pt="PIPE",ft="PLUS",mt="POUND",gt="QUERY",yt="QUOTE",vt="FULLWIDTHMIDDLEDOT",bt="SEMI",wt="SLASH",xt="TILDE",kt="UNDERSCORE",St="EMOJI",Mt="SYM";var Ct=Object.freeze({__proto__:null,ALPHANUMERICAL:T,AMPERSAND:X,APOSTROPHE:Q,ASCIINUMERICAL:A,ASTERISK:tt,AT:et,BACKSLASH:nt,BACKTICK:rt,CARET:ot,CLOSEANGLEBRACKET:V,CLOSEBRACE:L,CLOSEBRACKET:F,CLOSEPAREN:B,COLON:it,COMMA:st,DOLLAR:at,DOT:lt,EMOJI:St,EQUALS:ct,EXCLAMATION:dt,FULLWIDTHGREATERTHAN:Y,FULLWIDTHLEFTPAREN:K,FULLWIDTHLESSTHAN:G,FULLWIDTHMIDDLEDOT:vt,FULLWIDTHRIGHTPAREN:U,HYPHEN:ut,LEFTCORNERBRACKET:J,LEFTWHITECORNERBRACKET:q,LOCALHOST:O,NL:P,NUM:R,OPENANGLEBRACKET:H,OPENBRACE:D,OPENBRACKET:I,OPENPAREN:j,PERCENT:ht,PIPE:pt,PLUS:ft,POUND:mt,QUERY:gt,QUOTE:yt,RIGHTCORNERBRACKET:W,RIGHTWHITECORNERBRACKET:Z,SCHEME:N,SEMI:bt,SLASH:wt,SLASH_SCHEME:$,SYM:Mt,TILDE:xt,TLD:E,UNDERSCORE:kt,UTLD:_,UWORD:C,WORD:M,WS:z});const At=/[a-z]/,Tt=/\p{L}/u,Ot=/\p{Emoji}/u,Et=/\d/,_t=/\s/,Nt="\r",$t="\n",Rt="️",zt="‍",Pt="￼";let Dt=null,Lt=null;function It(t,e){const n=function(t){const e=[],n=t.length;let r=0;for(;r<n;){let o,i=t.charCodeAt(r),s=i<55296||i>56319||r+1===n||(o=t.charCodeAt(r+1))<56320||o>57343?t[r]:t.slice(r,r+2);e.push(s),r+=s.length}return e}(e.replace(/[A-Z]/g,(t=>t.toLowerCase()))),r=n.length,o=[];let i=0,s=0;for(;s<r;){let a=t,l=null,c=0,d=null,u=-1,h=-1;for(;s<r&&(l=a.go(n[s]));)a=l,a.accepts()?(u=0,h=0,d=a):u>=0&&(u+=n[s].length,h++),c+=n[s].length,i+=n[s].length,s++;i-=u,s-=h,c-=u,o.push({t:d.t,v:e.slice(i-c,i),s:i-c,e:i})}return o}function Ft(t,e,n,r,o){let i;const s=e.length;for(let n=0;n<s-1;n++){const s=e[n];t.j[s]?i=t.j[s]:(i=new b(r),i.jr=o.slice(),t.j[s]=i),t=i}return i=new b(n),i.jr=o.slice(),t.j[e[s-1]]=i,i}function jt(t){const e=[],n=[];let r=0;for(;r<t.length;){let o=0;for(;"0123456789".indexOf(t[r+o])>=0;)o++;if(o>0){e.push(n.join(""));for(let e=parseInt(t.substring(r,r+o),10);e>0;e--)n.pop();r+=o}else n.push(t[r]),r++}return e}const Bt={defaultProtocol:"http",events:null,format:Vt,formatHref:Vt,nl2br:!1,tagName:"a",target:null,rel:null,validate:!0,truncate:1/0,className:null,attributes:null,ignoreTags:[],render:null};function Ht(t,e=null){let n=s({},Bt);t&&(n=s(n,t instanceof Ht?t.o:t));const r=n.ignoreTags,o=[];for(let t=0;t<r.length;t++)o.push(r[t].toUpperCase());this.o=n,e&&(this.defaultRender=e),this.ignoreTags=o}function Vt(t){return t}function Kt(t,e){this.t="token",this.v=t,this.tk=e}function Ut(t,e){class n extends Kt{constructor(e,n){super(e,n),this.t=t}}for(const t in e)n.prototype[t]=e[t];return n.t=t,n}Ht.prototype={o:Bt,ignoreTags:[],defaultRender:t=>t,check(t){return this.get("validate",t.toString(),t)},get(t,e,n){const r=null!=e;let o=this.o[t];return o?("object"==typeof o?(o=n.t in o?o[n.t]:Bt[t],"function"==typeof o&&r&&(o=o(e,n))):"function"==typeof o&&r&&(o=o(e,n.t,n)),o):o},getObj(t,e,n){let r=this.o[t];return"function"==typeof r&&null!=e&&(r=r(e,n.t,n)),r},render(t){const e=t.render(this);return(this.get("render",null,t)||this.defaultRender)(e,t.t,t)}},Kt.prototype={isLink:!1,toString(){return this.v},toHref(t){return this.toString()},toFormattedString(t){const e=this.toString(),n=t.get("truncate",e,this),r=t.get("format",e,this);return n&&r.length>n?r.substring(0,n)+"…":r},toFormattedHref(t){return t.get("formatHref",this.toHref(t.get("defaultProtocol")),this)},startIndex(){return this.tk[0].s},endIndex(){return this.tk[this.tk.length-1].e},toObject(t=Bt.defaultProtocol){return{type:this.t,value:this.toString(),isLink:this.isLink,href:this.toHref(t),start:this.startIndex(),end:this.endIndex()}},toFormattedObject(t){return{type:this.t,value:this.toFormattedString(t),isLink:this.isLink,href:this.toFormattedHref(t),start:this.startIndex(),end:this.endIndex()}},validate(t){return t.get("validate",this.toString(),this)},render(t){const e=this,n=this.toHref(t.get("defaultProtocol")),r=t.get("formatHref",n,this),o=t.get("tagName",n,e),i=this.toFormattedString(t),a={},l=t.get("className",n,e),c=t.get("target",n,e),d=t.get("rel",n,e),u=t.getObj("attributes",n,e),h=t.getObj("events",n,e);return a.href=r,l&&(a.class=l),c&&(a.target=c),d&&(a.rel=d),u&&s(a,u),{tagName:o,attributes:a,content:i,eventListeners:h}}};const Jt=Ut("email",{isLink:!0,toHref(){return"mailto:"+this.toString()}}),Wt=Ut("text"),qt=Ut("nl"),Zt=Ut("url",{isLink:!0,toHref(t=Bt.defaultProtocol){return this.hasProtocol()?this.v:`${t}://${this.v}`},hasProtocol(){const t=this.tk;return t.length>=2&&t[0].t!==O&&t[1].t===it}}),Gt=t=>new b(t);function Yt(t,e,n){const r=n[0].s,o=n[n.length-1].e;return new t(e.slice(r,o),n)}const Xt="undefined"!=typeof console&&console&&console.warn||(()=>{}),Qt={scanner:null,parser:null,tokenQueue:[],pluginQueue:[],customSchemes:[],initialized:!1};function te(t,e=!1){if(Qt.initialized&&Xt(`linkifyjs: already initialized - will not register custom scheme "${t}" until manual call of linkify.init(). Register all schemes and plugins before invoking linkify the first time.`),!/^[0-9a-z]+(-[0-9a-z]+)*$/.test(t))throw new Error('linkifyjs: incorrect scheme format.\n1. Must only contain digits, lowercase ASCII letters or "-"\n2. Cannot start or end with "-"\n3. "-" cannot repeat');Qt.customSchemes.push([t,e])}function ee(t){return Qt.initialized||function(){Qt.scanner=function(t=[]){const e={};b.groups=e;const n=new b;null==Dt&&(Dt=jt(o)),null==Lt&&(Lt=jt(i)),S(n,"'",Q),S(n,"{",D),S(n,"}",L),S(n,"[",I),S(n,"]",F),S(n,"(",j),S(n,")",B),S(n,"<",H),S(n,">",V),S(n,"（",K),S(n,"）",U),S(n,"「",J),S(n,"」",W),S(n,"『",q),S(n,"』",Z),S(n,"＜",G),S(n,"＞",Y),S(n,"&",X),S(n,"*",tt),S(n,"@",et),S(n,"`",rt),S(n,"^",ot),S(n,":",it),S(n,",",st),S(n,"$",at),S(n,".",lt),S(n,"=",ct),S(n,"!",dt),S(n,"-",ut),S(n,"%",ht),S(n,"|",pt),S(n,"+",ft),S(n,"#",mt),S(n,"?",gt),S(n,'"',yt),S(n,"/",wt),S(n,";",bt),S(n,"~",xt),S(n,"_",kt),S(n,"\\",nt),S(n,"・",vt);const r=x(n,Et,R,{[a]:!0});x(r,Et,r);const y=x(r,At,A,{[d]:!0}),w=x(r,Tt,T,{[u]:!0}),It=x(n,At,M,{[l]:!0});x(It,Et,y),x(It,At,It),x(y,Et,y),x(y,At,y);const Bt=x(n,Tt,C,{[c]:!0});x(Bt,At),x(Bt,Et,w),x(Bt,Tt,Bt),x(w,Et,w),x(w,At),x(w,Tt,w);const Ht=S(n,$t,P,{[g]:!0}),Vt=S(n,Nt,z,{[g]:!0}),Kt=x(n,_t,z,{[g]:!0});S(n,Pt,Kt),S(Vt,$t,Ht),S(Vt,Pt,Kt),x(Vt,_t,Kt),S(Kt,Nt),S(Kt,$t),x(Kt,_t,Kt),S(Kt,Pt,Kt);const Ut=x(n,Ot,St,{[p]:!0});S(Ut,"#"),x(Ut,Ot,Ut),S(Ut,Rt,Ut);const Jt=S(Ut,zt);S(Jt,"#"),x(Jt,Ot,Ut);const Wt=[[At,It],[Et,y]],qt=[[At,null],[Tt,Bt],[Et,w]];for(let t=0;t<Dt.length;t++)Ft(n,Dt[t],E,M,Wt);for(let t=0;t<Lt.length;t++)Ft(n,Lt[t],_,C,qt);v(E,{tld:!0,ascii:!0},e),v(_,{utld:!0,alpha:!0},e),Ft(n,"file",N,M,Wt),Ft(n,"mailto",N,M,Wt),Ft(n,"http",$,M,Wt),Ft(n,"https",$,M,Wt),Ft(n,"ftp",$,M,Wt),Ft(n,"ftps",$,M,Wt),v(N,{scheme:!0,ascii:!0},e),v($,{slashscheme:!0,ascii:!0},e),t=t.sort(((t,e)=>t[0]>e[0]?1:-1));for(let e=0;e<t.length;e++){const r=t[e][0],o=t[e][1]?{[f]:!0}:{[m]:!0};r.indexOf("-")>=0?o[h]=!0:At.test(r)?Et.test(r)?o[d]=!0:o[l]=!0:o[a]=!0,k(n,r,r,o)}return k(n,"localhost",O,{ascii:!0}),n.jd=new b(Mt),{start:n,tokens:s({groups:e},Ct)}}(Qt.customSchemes);for(let t=0;t<Qt.tokenQueue.length;t++)Qt.tokenQueue[t][1]({scanner:Qt.scanner});Qt.parser=function({groups:t}){const e=t.domain.concat([X,tt,et,nt,rt,ot,at,ct,ut,R,ht,pt,ft,mt,wt,Mt,xt,kt]),n=[Q,it,st,lt,dt,ht,gt,yt,bt,H,V,D,L,F,I,j,B,K,U,J,W,q,Z,G,Y],r=[X,Q,tt,nt,rt,ot,at,ct,ut,D,L,ht,pt,ft,mt,gt,wt,Mt,xt,kt],o=Gt(),i=S(o,xt);w(i,r,i),w(i,t.domain,i);const s=Gt(),a=Gt(),l=Gt();w(o,t.domain,s),w(o,t.scheme,a),w(o,t.slashscheme,l),w(s,r,i),w(s,t.domain,s);const c=S(s,et);S(i,et,c),S(a,et,c),S(l,et,c);const d=S(i,lt);w(d,r,i),w(d,t.domain,i);const u=Gt();w(c,t.domain,u),w(u,t.domain,u);const h=S(u,lt);w(h,t.domain,u);const p=Gt(Jt);w(h,t.tld,p),w(h,t.utld,p),S(c,O,p);const f=S(u,ut);S(f,ut,f),w(f,t.domain,u),w(p,t.domain,u),S(p,lt,h),S(p,ut,f);const m=S(p,it);w(m,t.numeric,Jt);const g=S(s,ut),y=S(s,lt);S(g,ut,g),w(g,t.domain,s),w(y,r,i),w(y,t.domain,s);const v=Gt(Zt);w(y,t.tld,v),w(y,t.utld,v),w(v,t.domain,s),w(v,r,i),S(v,lt,y),S(v,ut,g),S(v,et,c);const b=S(v,it),x=Gt(Zt);w(b,t.numeric,x);const k=Gt(Zt),M=Gt();w(k,e,k),w(k,n,M),w(M,e,k),w(M,n,M),S(v,wt,k),S(x,wt,k);const C=S(a,it),A=S(l,it),T=S(A,wt),E=S(T,wt);w(a,t.domain,s),S(a,lt,y),S(a,ut,g),w(l,t.domain,s),S(l,lt,y),S(l,ut,g),w(C,t.domain,k),S(C,wt,k),S(C,gt,k),w(E,t.domain,k),w(E,e,k),S(E,wt,k);const _=[[D,L],[I,F],[j,B],[H,V],[K,U],[J,W],[q,Z],[G,Y]];for(let t=0;t<_.length;t++){const[r,o]=_[t],i=S(k,r);S(M,r,i),S(i,o,k);const s=Gt(Zt);w(i,e,s);const a=Gt();w(i,n),w(s,e,s),w(s,n,a),w(a,e,s),w(a,n,a),S(s,o,k),S(a,o,k)}return S(o,O,v),S(o,P,qt),{start:o,tokens:Ct}}(Qt.scanner.tokens);for(let t=0;t<Qt.pluginQueue.length;t++)Qt.pluginQueue[t][1]({scanner:Qt.scanner,parser:Qt.parser});Qt.initialized=!0}(),function(t,e,n){let r=n.length,o=0,i=[],s=[];for(;o<r;){let a=t,l=null,c=null,d=0,u=null,h=-1;for(;o<r&&!(l=a.go(n[o].t));)s.push(n[o++]);for(;o<r&&(c=l||a.go(n[o].t));)l=null,a=c,a.accepts()?(h=0,u=a):h>=0&&h++,o++,d++;if(h<0)o-=d,o<r&&(s.push(n[o]),o++);else{s.length>0&&(i.push(Yt(Wt,e,s)),s=[]),o-=h,d-=h;const t=u.t,r=n.slice(o-d,o);i.push(Yt(t,e,r))}}return s.length>0&&i.push(Yt(Wt,e,s)),i}(Qt.parser.start,t,It(Qt.scanner.start,t))}function ne(t,e=null,n=null){if(e&&"object"==typeof e){if(n)throw Error(`linkifyjs: Invalid link type ${e}; must be a string`);n=e,e=null}const r=new Ht(n),o=ee(t),i=[];for(let t=0;t<o.length;t++){const n=o[t];!n.isLink||e&&n.t!==e||!r.check(n)||i.push(n.toFormattedObject(r))}return i}ee.scan=It;var re=n(2559),oe="[\0-   ᠎ -\u2029 　]",ie=new RegExp(oe),se=new RegExp(`${oe}$`),ae=new RegExp(oe,"g");function le(t,e){const n=["http","https","ftp","ftps","mailto","tel","callto","sms","cid","xmpp"];return e&&e.forEach((t=>{const e="string"==typeof t?t:t.scheme;e&&n.push(e)})),!t||t.replace(ae,"").match(new RegExp(`^(?:(?:${n.join("|")}):|[^a-z]|[a-z0-9+.-]+(?:[^a-z+.-:]|$))`,"i"))}var ce=r.CU.create({name:"link",priority:1e3,keepOnSplit:!1,exitable:!0,onCreate(){this.options.validate&&!this.options.shouldAutoLink&&(this.options.shouldAutoLink=this.options.validate,console.warn("The `validate` option is deprecated. Rename to the `shouldAutoLink` option instead.")),this.options.protocols.forEach((t=>{"string"!=typeof t?te(t.scheme,t.optionalSlashes):te(t)}))},onDestroy(){b.groups={},Qt.scanner=null,Qt.parser=null,Qt.tokenQueue=[],Qt.pluginQueue=[],Qt.customSchemes=[],Qt.initialized=!1},inclusive(){return this.options.autolink},addOptions:()=>({openOnClick:!0,enableClickSelection:!1,linkOnPaste:!0,autolink:!0,protocols:[],defaultProtocol:"http",HTMLAttributes:{target:"_blank",rel:"noopener noreferrer nofollow",class:null},isAllowedUri:(t,e)=>!!le(t,e.protocols),validate:t=>!!t,shouldAutoLink:t=>!!t}),addAttributes(){return{href:{default:null,parseHTML:t=>t.getAttribute("href")},target:{default:this.options.HTMLAttributes.target},rel:{default:this.options.HTMLAttributes.rel},class:{default:this.options.HTMLAttributes.class}}},parseHTML(){return[{tag:"a[href]",getAttrs:t=>{const e=t.getAttribute("href");return!(!e||!this.options.isAllowedUri(e,{defaultValidate:t=>!!le(t,this.options.protocols),protocols:this.options.protocols,defaultProtocol:this.options.defaultProtocol}))&&null}}]},renderHTML({HTMLAttributes:t}){return this.options.isAllowedUri(t.href,{defaultValidate:t=>!!le(t,this.options.protocols),protocols:this.options.protocols,defaultProtocol:this.options.defaultProtocol})?["a",(0,r.KV)(this.options.HTMLAttributes,t),0]:["a",(0,r.KV)(this.options.HTMLAttributes,{...t,href:""}),0]},addCommands(){return{setLink:t=>({chain:e})=>{const{href:n}=t;return!!this.options.isAllowedUri(n,{defaultValidate:t=>!!le(t,this.options.protocols),protocols:this.options.protocols,defaultProtocol:this.options.defaultProtocol})&&e().setMark(this.name,t).setMeta("preventAutolink",!0).run()},toggleLink:t=>({chain:e})=>{const{href:n}=t||{};return!(n&&!this.options.isAllowedUri(n,{defaultValidate:t=>!!le(t,this.options.protocols),protocols:this.options.protocols,defaultProtocol:this.options.defaultProtocol}))&&e().toggleMark(this.name,t,{extendEmptyMarkRange:!0}).setMeta("preventAutolink",!0).run()},unsetLink:()=>({chain:t})=>t().unsetMark(this.name,{extendEmptyMarkRange:!0}).setMeta("preventAutolink",!0).run()}},addPasteRules(){return[(0,r.Zc)({find:t=>{const e=[];if(t){const{protocols:n,defaultProtocol:r}=this.options,o=ne(t).filter((t=>t.isLink&&this.options.isAllowedUri(t.value,{defaultValidate:t=>!!le(t,n),protocols:n,defaultProtocol:r})));o.length&&o.forEach((t=>e.push({text:t.value,data:{href:t.href},index:t.start})))}return e},type:this.type,getAttributes:t=>{var e;return{href:null==(e=t.data)?void 0:e.href}}})]},addProseMirrorPlugins(){const t=[],{protocols:e,defaultProtocol:n}=this.options;return this.options.autolink&&t.push((o={type:this.type,defaultProtocol:this.options.defaultProtocol,validate:t=>this.options.isAllowedUri(t,{defaultValidate:t=>!!le(t,e),protocols:e,defaultProtocol:n}),shouldAutoLink:this.options.shouldAutoLink},new re.k_({key:new re.hs("autolink"),appendTransaction:(t,e,n)=>{const i=t.some((t=>t.docChanged))&&!e.doc.eq(n.doc),s=t.some((t=>t.getMeta("preventAutolink")));if(!i||s)return;const{tr:a}=n,l=(0,r.T7)(e.doc,[...t]);return(0,r.FF)(l).forEach((({newRange:t})=>{const e=(0,r.Nx)(n.doc,t,(t=>t.isTextblock));let i,s;if(e.length>1)i=e[0],s=n.doc.textBetween(i.pos,i.pos+i.node.nodeSize,void 0," ");else if(e.length){const r=n.doc.textBetween(t.from,t.to," "," ");if(!se.test(r))return;i=e[0],s=n.doc.textBetween(i.pos,t.to,void 0," ")}if(i&&s){const t=s.split(ie).filter(Boolean);if(t.length<=0)return!1;const e=t[t.length-1],c=i.pos+s.lastIndexOf(e);if(!e)return!1;const d=ee(e).map((t=>t.toObject(o.defaultProtocol)));if(!(1===(l=d).length?l[0].isLink:3===l.length&&l[1].isLink&&["()","[]"].includes(l[0].value+l[2].value)))return!1;d.filter((t=>t.isLink)).map((t=>({...t,from:c+t.start+1,to:c+t.end+1}))).filter((t=>!n.schema.marks.code||!n.doc.rangeHasMark(t.from,t.to,n.schema.marks.code))).filter((t=>o.validate(t.value))).filter((t=>o.shouldAutoLink(t.value))).forEach((t=>{(0,r.hO)(t.from,t.to,n.doc).some((t=>t.mark.type===o.type))||a.addMark(t.from,t.to,o.type.create({href:t.href}))}))}var l})),a.steps.length?a:void 0}}))),!0===this.options.openOnClick&&t.push(function(t){return new re.k_({key:new re.hs("handleClickLink"),props:{handleClick:(e,n,o)=>{var i,s;if(0!==o.button)return!1;if(!e.editable)return!1;let a=null;if(o.target instanceof HTMLAnchorElement)a=o.target;else{let t=o.target;const e=[];for(;"DIV"!==t.nodeName;)e.push(t),t=t.parentNode;a=e.find((t=>"A"===t.nodeName))}if(!a)return!1;const l=(0,r.gu)(e.state,t.type.name),c=null!=(i=null==a?void 0:a.href)?i:l.href,d=null!=(s=null==a?void 0:a.target)?s:l.target;return t.enableClickSelection&&t.editor.commands.extendMarkRange(t.type.name),!(!a||!c||(window.open(c,d),0))}}})}({type:this.type,editor:this.editor,enableClickSelection:this.options.enableClickSelection})),this.options.linkOnPaste&&t.push(function(t){return new re.k_({key:new re.hs("handlePasteLink"),props:{handlePaste:(e,n,r)=>{const{state:o}=e,{selection:i}=o,{empty:s}=i;if(s)return!1;let a="";r.content.forEach((t=>{a+=t.textContent}));const l=ne(a,{defaultProtocol:t.defaultProtocol}).find((t=>t.isLink&&t.value===a));return!(!a||!l)&&t.editor.commands.setMark(t.type,{href:l.href})}}})}({editor:this.editor,defaultProtocol:this.options.defaultProtocol,type:this.type})),t;var o}}),de=ce},2359:(t,e,n)=>{"use strict";n.d(e,{A:()=>r});const r=n(4198).YY.create({name:"textAlign",addOptions:()=>({types:[],alignments:["left","center","right","justify"],defaultAlignment:"left"}),addGlobalAttributes(){return[{types:this.options.types,attributes:{textAlign:{default:this.options.defaultAlignment,parseHTML:t=>t.style.textAlign||this.options.defaultAlignment,renderHTML:t=>t.textAlign===this.options.defaultAlignment?{}:{style:`text-align: ${t.textAlign}`}}}}]},addCommands(){return{setTextAlign:t=>({commands:e})=>!!this.options.alignments.includes(t)&&this.options.types.map((n=>e.updateAttributes(n,{textAlign:t}))).every((t=>t)),unsetTextAlign:()=>({commands:t})=>this.options.types.map((e=>t.resetAttributes(e,"textAlign"))).every((t=>t))}},addKeyboardShortcuts(){return{"Mod-Shift-l":()=>this.editor.commands.setTextAlign("left"),"Mod-Shift-e":()=>this.editor.commands.setTextAlign("center"),"Mod-Shift-r":()=>this.editor.commands.setTextAlign("right"),"Mod-Shift-j":()=>this.editor.commands.setTextAlign("justify")}}})},31:(t,e,n)=>{"use strict";n.d(e,{A:()=>o});var r=n(4198);const o=r.CU.create({name:"textStyle",addOptions:()=>({HTMLAttributes:{}}),parseHTML:()=>[{tag:"span",getAttrs:t=>!!t.hasAttribute("style")&&{}}],renderHTML({HTMLAttributes:t}){return["span",(0,r.KV)(this.options.HTMLAttributes,t),0]},addCommands(){return{removeEmptyTextStyle:()=>({state:t,commands:e})=>{const n=(0,r.z6)(t,this.type);return!!Object.entries(n).some((([,t])=>!!t))||e.unsetMark(this.name)}}}})},2740:(t,e,n)=>{"use strict";n.d(e,{A:()=>o});var r=n(4198);const o=r.CU.create({name:"underline",addOptions:()=>({HTMLAttributes:{}}),parseHTML:()=>[{tag:"u"},{style:"text-decoration",consuming:!1,getAttrs:t=>!!t.includes("underline")&&{}}],renderHTML({HTMLAttributes:t}){return["u",(0,r.KV)(this.options.HTMLAttributes,t),0]},addCommands(){return{setUnderline:()=>({commands:t})=>t.setMark(this.name),toggleUnderline:()=>({commands:t})=>t.toggleMark(this.name),unsetUnderline:()=>({commands:t})=>t.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-u":()=>this.editor.commands.toggleUnderline(),"Mod-U":()=>this.editor.commands.toggleUnderline()}}})},9395:(t,e,n)=>{"use strict";n.d(e,{A:()=>rt});var r=n(4198);const o=/^\s*>\s$/,i=r.bP.create({name:"blockquote",addOptions:()=>({HTMLAttributes:{}}),content:"block+",group:"block",defining:!0,parseHTML:()=>[{tag:"blockquote"}],renderHTML({HTMLAttributes:t}){return["blockquote",(0,r.KV)(this.options.HTMLAttributes,t),0]},addCommands(){return{setBlockquote:()=>({commands:t})=>t.wrapIn(this.name),toggleBlockquote:()=>({commands:t})=>t.toggleWrap(this.name),unsetBlockquote:()=>({commands:t})=>t.lift(this.name)}},addKeyboardShortcuts(){return{"Mod-Shift-b":()=>this.editor.commands.toggleBlockquote()}},addInputRules(){return[(0,r.tG)({find:o,type:this.type})]}}),s=/(?:^|\s)(\*\*(?!\s+\*\*)((?:[^*]+))\*\*(?!\s+\*\*))$/,a=/(?:^|\s)(\*\*(?!\s+\*\*)((?:[^*]+))\*\*(?!\s+\*\*))/g,l=/(?:^|\s)(__(?!\s+__)((?:[^_]+))__(?!\s+__))$/,c=/(?:^|\s)(__(?!\s+__)((?:[^_]+))__(?!\s+__))/g,d=r.CU.create({name:"bold",addOptions:()=>({HTMLAttributes:{}}),parseHTML:()=>[{tag:"strong"},{tag:"b",getAttrs:t=>"normal"!==t.style.fontWeight&&null},{style:"font-weight",getAttrs:t=>/^(bold(er)?|[5-9]\d{2,})$/.test(t)&&null}],renderHTML({HTMLAttributes:t}){return["strong",(0,r.KV)(this.options.HTMLAttributes,t),0]},addCommands(){return{setBold:()=>({commands:t})=>t.setMark(this.name),toggleBold:()=>({commands:t})=>t.toggleMark(this.name),unsetBold:()=>({commands:t})=>t.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-b":()=>this.editor.commands.toggleBold(),"Mod-B":()=>this.editor.commands.toggleBold()}},addInputRules(){return[(0,r.OX)({find:s,type:this.type}),(0,r.OX)({find:l,type:this.type})]},addPasteRules(){return[(0,r.Zc)({find:a,type:this.type}),(0,r.Zc)({find:c,type:this.type})]}}),u=r.bP.create({name:"listItem",addOptions:()=>({HTMLAttributes:{},bulletListTypeName:"bulletList",orderedListTypeName:"orderedList"}),content:"paragraph block*",defining:!0,parseHTML:()=>[{tag:"li"}],renderHTML({HTMLAttributes:t}){return["li",(0,r.KV)(this.options.HTMLAttributes,t),0]},addKeyboardShortcuts(){return{Enter:()=>this.editor.commands.splitListItem(this.name),Tab:()=>this.editor.commands.sinkListItem(this.name),"Shift-Tab":()=>this.editor.commands.liftListItem(this.name)}}}),h=r.CU.create({name:"textStyle",addOptions:()=>({HTMLAttributes:{}}),parseHTML:()=>[{tag:"span",getAttrs:t=>!!t.hasAttribute("style")&&{}}],renderHTML({HTMLAttributes:t}){return["span",(0,r.KV)(this.options.HTMLAttributes,t),0]},addCommands(){return{removeEmptyTextStyle:()=>({state:t,commands:e})=>{const n=(0,r.z6)(t,this.type);return!!Object.entries(n).some((([,t])=>!!t))||e.unsetMark(this.name)}}}}),p=/^\s*([-+*])\s$/,f=r.bP.create({name:"bulletList",addOptions:()=>({itemTypeName:"listItem",HTMLAttributes:{},keepMarks:!1,keepAttributes:!1}),group:"block list",content(){return`${this.options.itemTypeName}+`},parseHTML:()=>[{tag:"ul"}],renderHTML({HTMLAttributes:t}){return["ul",(0,r.KV)(this.options.HTMLAttributes,t),0]},addCommands(){return{toggleBulletList:()=>({commands:t,chain:e})=>this.options.keepAttributes?e().toggleList(this.name,this.options.itemTypeName,this.options.keepMarks).updateAttributes(u.name,this.editor.getAttributes(h.name)).run():t.toggleList(this.name,this.options.itemTypeName,this.options.keepMarks)}},addKeyboardShortcuts(){return{"Mod-Shift-8":()=>this.editor.commands.toggleBulletList()}},addInputRules(){let t=(0,r.tG)({find:p,type:this.type});return(this.options.keepMarks||this.options.keepAttributes)&&(t=(0,r.tG)({find:p,type:this.type,keepMarks:this.options.keepMarks,keepAttributes:this.options.keepAttributes,getAttributes:()=>this.editor.getAttributes(h.name),editor:this.editor})),[t]}}),m=/(?:^|\s)(`(?!\s+`)((?:[^`]+))`(?!\s+`))$/,g=/(?:^|\s)(`(?!\s+`)((?:[^`]+))`(?!\s+`))/g,y=r.CU.create({name:"code",addOptions:()=>({HTMLAttributes:{}}),excludes:"_",code:!0,exitable:!0,parseHTML:()=>[{tag:"code"}],renderHTML({HTMLAttributes:t}){return["code",(0,r.KV)(this.options.HTMLAttributes,t),0]},addCommands(){return{setCode:()=>({commands:t})=>t.setMark(this.name),toggleCode:()=>({commands:t})=>t.toggleMark(this.name),unsetCode:()=>({commands:t})=>t.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-e":()=>this.editor.commands.toggleCode()}},addInputRules(){return[(0,r.OX)({find:m,type:this.type})]},addPasteRules(){return[(0,r.Zc)({find:g,type:this.type})]}});var v=n(2559);const b=/^```([a-z]+)?[\s\n]$/,w=/^~~~([a-z]+)?[\s\n]$/,x=r.bP.create({name:"codeBlock",addOptions:()=>({languageClassPrefix:"language-",exitOnTripleEnter:!0,exitOnArrowDown:!0,HTMLAttributes:{}}),content:"text*",marks:"",group:"block",code:!0,defining:!0,addAttributes(){return{language:{default:null,parseHTML:t=>{var e;const{languageClassPrefix:n}=this.options;return[...(null===(e=t.firstElementChild)||void 0===e?void 0:e.classList)||[]].filter((t=>t.startsWith(n))).map((t=>t.replace(n,"")))[0]||null},rendered:!1}}},parseHTML:()=>[{tag:"pre",preserveWhitespace:"full"}],renderHTML({node:t,HTMLAttributes:e}){return["pre",(0,r.KV)(this.options.HTMLAttributes,e),["code",{class:t.attrs.language?this.options.languageClassPrefix+t.attrs.language:null},0]]},addCommands(){return{setCodeBlock:t=>({commands:e})=>e.setNode(this.name,t),toggleCodeBlock:t=>({commands:e})=>e.toggleNode(this.name,"paragraph",t)}},addKeyboardShortcuts(){return{"Mod-Alt-c":()=>this.editor.commands.toggleCodeBlock(),Backspace:()=>{const{empty:t,$anchor:e}=this.editor.state.selection,n=1===e.pos;return!(!t||e.parent.type.name!==this.name)&&!(!n&&e.parent.textContent.length)&&this.editor.commands.clearNodes()},Enter:({editor:t})=>{if(!this.options.exitOnTripleEnter)return!1;const{state:e}=t,{selection:n}=e,{$from:r,empty:o}=n;if(!o||r.parent.type!==this.type)return!1;const i=r.parentOffset===r.parent.nodeSize-2,s=r.parent.textContent.endsWith("\n\n");return!(!i||!s)&&t.chain().command((({tr:t})=>(t.delete(r.pos-2,r.pos),!0))).exitCode().run()},ArrowDown:({editor:t})=>{if(!this.options.exitOnArrowDown)return!1;const{state:e}=t,{selection:n,doc:r}=e,{$from:o,empty:i}=n;if(!i||o.parent.type!==this.type)return!1;if(o.parentOffset!==o.parent.nodeSize-2)return!1;const s=o.after();return void 0!==s&&(!r.nodeAt(s)&&t.commands.exitCode())}}},addInputRules(){return[(0,r.JJ)({find:b,type:this.type,getAttributes:t=>({language:t[1]})}),(0,r.JJ)({find:w,type:this.type,getAttributes:t=>({language:t[1]})})]},addProseMirrorPlugins(){return[new v.k_({key:new v.hs("codeBlockVSCodeHandler"),props:{handlePaste:(t,e)=>{if(!e.clipboardData)return!1;if(this.editor.isActive(this.type.name))return!1;const n=e.clipboardData.getData("text/plain"),r=e.clipboardData.getData("vscode-editor-data"),o=r?JSON.parse(r):void 0,i=null==o?void 0:o.mode;if(!n||!i)return!1;const{tr:s}=t.state;return t.state.selection.from===t.state.doc.nodeSize-(1+2*t.state.selection.$to.depth)?s.insert(t.state.selection.from-1,this.type.create({language:i})):s.replaceSelectionWith(this.type.create({language:i})),s.setSelection(v.U3.near(s.doc.resolve(Math.max(0,s.selection.from-2)))),s.insertText(n.replace(/\r\n?/g,"\n")),s.setMeta("paste",!0),t.dispatch(s),!0}}})]}}),k=r.bP.create({name:"doc",topNode:!0,content:"block+"});var S=n(196);function M(t={}){return new v.k_({view:e=>new C(e,t)})}class C{constructor(t,e){var n;this.editorView=t,this.cursorPos=null,this.element=null,this.timeout=-1,this.width=null!==(n=e.width)&&void 0!==n?n:1,this.color=!1===e.color?void 0:e.color||"black",this.class=e.class,this.handlers=["dragover","dragend","drop","dragleave"].map((e=>{let n=t=>{this[e](t)};return t.dom.addEventListener(e,n),{name:e,handler:n}}))}destroy(){this.handlers.forEach((({name:t,handler:e})=>this.editorView.dom.removeEventListener(t,e)))}update(t,e){null!=this.cursorPos&&e.doc!=t.state.doc&&(this.cursorPos>t.state.doc.content.size?this.setCursor(null):this.updateOverlay())}setCursor(t){t!=this.cursorPos&&(this.cursorPos=t,null==t?(this.element.parentNode.removeChild(this.element),this.element=null):this.updateOverlay())}updateOverlay(){let t,e=this.editorView.state.doc.resolve(this.cursorPos),n=!e.parent.inlineContent;if(n){let n=e.nodeBefore,r=e.nodeAfter;if(n||r){let e=this.editorView.nodeDOM(this.cursorPos-(n?n.nodeSize:0));if(e){let o=e.getBoundingClientRect(),i=n?o.bottom:o.top;n&&r&&(i=(i+this.editorView.nodeDOM(this.cursorPos).getBoundingClientRect().top)/2),t={left:o.left,right:o.right,top:i-this.width/2,bottom:i+this.width/2}}}}if(!t){let e=this.editorView.coordsAtPos(this.cursorPos);t={left:e.left-this.width/2,right:e.left+this.width/2,top:e.top,bottom:e.bottom}}let r,o,i=this.editorView.dom.offsetParent;if(this.element||(this.element=i.appendChild(document.createElement("div")),this.class&&(this.element.className=this.class),this.element.style.cssText="position: absolute; z-index: 50; pointer-events: none;",this.color&&(this.element.style.backgroundColor=this.color)),this.element.classList.toggle("prosemirror-dropcursor-block",n),this.element.classList.toggle("prosemirror-dropcursor-inline",!n),!i||i==document.body&&"static"==getComputedStyle(i).position)r=-pageXOffset,o=-pageYOffset;else{let t=i.getBoundingClientRect();r=t.left-i.scrollLeft,o=t.top-i.scrollTop}this.element.style.left=t.left-r+"px",this.element.style.top=t.top-o+"px",this.element.style.width=t.right-t.left+"px",this.element.style.height=t.bottom-t.top+"px"}scheduleRemoval(t){clearTimeout(this.timeout),this.timeout=setTimeout((()=>this.setCursor(null)),t)}dragover(t){if(!this.editorView.editable)return;let e=this.editorView.posAtCoords({left:t.clientX,top:t.clientY}),n=e&&e.inside>=0&&this.editorView.state.doc.nodeAt(e.inside),r=n&&n.type.spec.disableDropCursor,o="function"==typeof r?r(this.editorView,e,t):r;if(e&&!o){let t=e.pos;if(this.editorView.dragging&&this.editorView.dragging.slice){let e=(0,S.Um)(this.editorView.state.doc,t,this.editorView.dragging.slice);null!=e&&(t=e)}this.setCursor(t),this.scheduleRemoval(5e3)}}dragend(){this.scheduleRemoval(20)}drop(){this.scheduleRemoval(20)}dragleave(t){t.target!=this.editorView.dom&&this.editorView.dom.contains(t.relatedTarget)||this.setCursor(null)}}const A=r.YY.create({name:"dropCursor",addOptions:()=>({color:"currentColor",width:1,class:void 0}),addProseMirrorPlugins(){return[M(this.options)]}});var T=n(1804),O=n(9679),E=n(5873);class _ extends v.LN{constructor(t){super(t,t)}map(t,e){let n=t.resolve(e.map(this.head));return _.valid(n)?new _(n):v.LN.near(n)}content(){return O.Ji.empty}eq(t){return t instanceof _&&t.head==this.head}toJSON(){return{type:"gapcursor",pos:this.head}}static fromJSON(t,e){if("number"!=typeof e.pos)throw new RangeError("Invalid input for GapCursor.fromJSON");return new _(t.resolve(e.pos))}getBookmark(){return new N(this.anchor)}static valid(t){let e=t.parent;if(e.isTextblock||!function(t){for(let e=t.depth;e>=0;e--){let n=t.index(e),r=t.node(e);if(0!=n)for(let t=r.child(n-1);;t=t.lastChild){if(0==t.childCount&&!t.inlineContent||t.isAtom||t.type.spec.isolating)return!0;if(t.inlineContent)return!1}else if(r.type.spec.isolating)return!0}return!0}(t)||!function(t){for(let e=t.depth;e>=0;e--){let n=t.indexAfter(e),r=t.node(e);if(n!=r.childCount)for(let t=r.child(n);;t=t.firstChild){if(0==t.childCount&&!t.inlineContent||t.isAtom||t.type.spec.isolating)return!0;if(t.inlineContent)return!1}else if(r.type.spec.isolating)return!0}return!0}(t))return!1;let n=e.type.spec.allowGapCursor;if(null!=n)return n;let r=e.contentMatchAt(t.index()).defaultType;return r&&r.isTextblock}static findGapCursorFrom(t,e,n=!1){t:for(;;){if(!n&&_.valid(t))return t;let r=t.pos,o=null;for(let n=t.depth;;n--){let i=t.node(n);if(e>0?t.indexAfter(n)<i.childCount:t.index(n)>0){o=i.child(e>0?t.indexAfter(n):t.index(n)-1);break}if(0==n)return null;r+=e;let s=t.doc.resolve(r);if(_.valid(s))return s}for(;;){let i=e>0?o.firstChild:o.lastChild;if(!i){if(o.isAtom&&!o.isText&&!v.nh.isSelectable(o)){t=t.doc.resolve(r+o.nodeSize*e),n=!1;continue t}break}o=i,r+=e;let s=t.doc.resolve(r);if(_.valid(s))return s}return null}}}_.prototype.visible=!1,_.findFrom=_.findGapCursorFrom,v.LN.jsonID("gapcursor",_);class N{constructor(t){this.pos=t}map(t){return new N(t.map(this.pos))}resolve(t){let e=t.resolve(this.pos);return _.valid(e)?new _(e):v.LN.near(e)}}const $=(0,T.K)({ArrowLeft:R("horiz",-1),ArrowRight:R("horiz",1),ArrowUp:R("vert",-1),ArrowDown:R("vert",1)});function R(t,e){const n="vert"==t?e>0?"down":"up":e>0?"right":"left";return function(t,r,o){let i=t.selection,s=e>0?i.$to:i.$from,a=i.empty;if(i instanceof v.U3){if(!o.endOfTextblock(n)||0==s.depth)return!1;a=!1,s=t.doc.resolve(e>0?s.after():s.before())}let l=_.findGapCursorFrom(s,e,a);return!!l&&(r&&r(t.tr.setSelection(new _(l))),!0)}}function z(t,e,n){if(!t||!t.editable)return!1;let r=t.state.doc.resolve(e);if(!_.valid(r))return!1;let o=t.posAtCoords({left:n.clientX,top:n.clientY});return!(o&&o.inside>-1&&v.nh.isSelectable(t.state.doc.nodeAt(o.inside))||(t.dispatch(t.state.tr.setSelection(new _(r))),0))}function P(t,e){if("insertCompositionText"!=e.inputType||!(t.state.selection instanceof _))return!1;let{$from:n}=t.state.selection,r=n.parent.contentMatchAt(n.index()).findWrapping(t.state.schema.nodes.text);if(!r)return!1;let o=O.FK.empty;for(let t=r.length-1;t>=0;t--)o=O.FK.from(r[t].createAndFill(null,o));let i=t.state.tr.replace(n.pos,n.pos,new O.Ji(o,0,0));return i.setSelection(v.U3.near(i.doc.resolve(n.pos+1))),t.dispatch(i),!1}function D(t){if(!(t.selection instanceof _))return null;let e=document.createElement("div");return e.className="ProseMirror-gapcursor",E.zF.create(t.doc,[E.NZ.widget(t.selection.head,e,{key:"gapcursor"})])}const L=r.YY.create({name:"gapCursor",addProseMirrorPlugins:()=>[new v.k_({props:{decorations:D,createSelectionBetween:(t,e,n)=>e.pos==n.pos&&_.valid(n)?new _(n):null,handleClick:z,handleKeyDown:$,handleDOMEvents:{beforeinput:P}}})],extendNodeSchema(t){var e;const n={name:t.name,options:t.options,storage:t.storage};return{allowGapCursor:null!==(e=(0,r.gk)((0,r.iI)(t,"allowGapCursor",n)))&&void 0!==e?e:null}}}),I=r.bP.create({name:"hardBreak",addOptions:()=>({keepMarks:!0,HTMLAttributes:{}}),inline:!0,group:"inline",selectable:!1,parseHTML:()=>[{tag:"br"}],renderHTML({HTMLAttributes:t}){return["br",(0,r.KV)(this.options.HTMLAttributes,t)]},renderText:()=>"\n",addCommands(){return{setHardBreak:()=>({commands:t,chain:e,state:n,editor:r})=>t.first([()=>t.exitCode(),()=>t.command((()=>{const{selection:t,storedMarks:o}=n;if(t.$from.parent.type.spec.isolating)return!1;const{keepMarks:i}=this.options,{splittableMarks:s}=r.extensionManager,a=o||t.$to.parentOffset&&t.$from.marks();return e().insertContent({type:this.name}).command((({tr:t,dispatch:e})=>{if(e&&a&&i){const e=a.filter((t=>s.includes(t.type.name)));t.ensureMarks(e)}return!0})).run()}))])}},addKeyboardShortcuts(){return{"Mod-Enter":()=>this.editor.commands.setHardBreak(),"Shift-Enter":()=>this.editor.commands.setHardBreak()}}}),F=r.bP.create({name:"heading",addOptions:()=>({levels:[1,2,3,4,5,6],HTMLAttributes:{}}),content:"inline*",group:"block",defining:!0,addAttributes:()=>({level:{default:1,rendered:!1}}),parseHTML(){return this.options.levels.map((t=>({tag:`h${t}`,attrs:{level:t}})))},renderHTML({node:t,HTMLAttributes:e}){return[`h${this.options.levels.includes(t.attrs.level)?t.attrs.level:this.options.levels[0]}`,(0,r.KV)(this.options.HTMLAttributes,e),0]},addCommands(){return{setHeading:t=>({commands:e})=>!!this.options.levels.includes(t.level)&&e.setNode(this.name,t),toggleHeading:t=>({commands:e})=>!!this.options.levels.includes(t.level)&&e.toggleNode(this.name,"paragraph",t)}},addKeyboardShortcuts(){return this.options.levels.reduce(((t,e)=>({...t,[`Mod-Alt-${e}`]:()=>this.editor.commands.toggleHeading({level:e})})),{})},addInputRules(){return this.options.levels.map((t=>(0,r.JJ)({find:new RegExp(`^(#{1,${t}})\\s$`),type:this.type,getAttributes:{level:t}})))}});var j=n(4758);const B=r.bP.create({name:"horizontalRule",addOptions:()=>({HTMLAttributes:{}}),group:"block",parseHTML:()=>[{tag:"hr"}],renderHTML({HTMLAttributes:t}){return["hr",(0,r.KV)(this.options.HTMLAttributes,t)]},addCommands(){return{setHorizontalRule:()=>({chain:t,state:e})=>{const{$to:n}=e.selection,r=t();return 0===n.parentOffset?r.insertContentAt(Math.max(n.pos-2,0),{type:this.name}):r.insertContent({type:this.name}),r.command((({tr:t,dispatch:e})=>{var n;if(e){const{$to:e}=t.selection,r=e.end();if(e.nodeAfter)e.nodeAfter.isTextblock?t.setSelection(v.U3.create(t.doc,e.pos+1)):e.nodeAfter.isBlock?t.setSelection(v.nh.create(t.doc,e.pos)):t.setSelection(v.U3.create(t.doc,e.pos));else{const o=null===(n=e.parent.type.contentMatch.defaultType)||void 0===n?void 0:n.create();o&&(t.insert(r,o),t.setSelection(v.U3.create(t.doc,r+1)))}t.scrollIntoView()}return!0})).run()}}},addInputRules(){return[(0,r.jT)({find:/^(?:---|—-|___\s|\*\*\*\s)$/,type:this.type})]}}),H=/(?:^|\s)(\*(?!\s+\*)((?:[^*]+))\*(?!\s+\*))$/,V=/(?:^|\s)(\*(?!\s+\*)((?:[^*]+))\*(?!\s+\*))/g,K=/(?:^|\s)(_(?!\s+_)((?:[^_]+))_(?!\s+_))$/,U=/(?:^|\s)(_(?!\s+_)((?:[^_]+))_(?!\s+_))/g,J=r.CU.create({name:"italic",addOptions:()=>({HTMLAttributes:{}}),parseHTML:()=>[{tag:"em"},{tag:"i",getAttrs:t=>"normal"!==t.style.fontStyle&&null},{style:"font-style=italic"}],renderHTML({HTMLAttributes:t}){return["em",(0,r.KV)(this.options.HTMLAttributes,t),0]},addCommands(){return{setItalic:()=>({commands:t})=>t.setMark(this.name),toggleItalic:()=>({commands:t})=>t.toggleMark(this.name),unsetItalic:()=>({commands:t})=>t.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-i":()=>this.editor.commands.toggleItalic(),"Mod-I":()=>this.editor.commands.toggleItalic()}},addInputRules(){return[(0,r.OX)({find:H,type:this.type}),(0,r.OX)({find:K,type:this.type})]},addPasteRules(){return[(0,r.Zc)({find:V,type:this.type}),(0,r.Zc)({find:U,type:this.type})]}}),W=r.bP.create({name:"listItem",addOptions:()=>({HTMLAttributes:{},bulletListTypeName:"bulletList",orderedListTypeName:"orderedList"}),content:"paragraph block*",defining:!0,parseHTML:()=>[{tag:"li"}],renderHTML({HTMLAttributes:t}){return["li",(0,r.KV)(this.options.HTMLAttributes,t),0]},addKeyboardShortcuts(){return{Enter:()=>this.editor.commands.splitListItem(this.name),Tab:()=>this.editor.commands.sinkListItem(this.name),"Shift-Tab":()=>this.editor.commands.liftListItem(this.name)}}}),q=r.bP.create({name:"listItem",addOptions:()=>({HTMLAttributes:{},bulletListTypeName:"bulletList",orderedListTypeName:"orderedList"}),content:"paragraph block*",defining:!0,parseHTML:()=>[{tag:"li"}],renderHTML({HTMLAttributes:t}){return["li",(0,r.KV)(this.options.HTMLAttributes,t),0]},addKeyboardShortcuts(){return{Enter:()=>this.editor.commands.splitListItem(this.name),Tab:()=>this.editor.commands.sinkListItem(this.name),"Shift-Tab":()=>this.editor.commands.liftListItem(this.name)}}}),Z=r.CU.create({name:"textStyle",addOptions:()=>({HTMLAttributes:{}}),parseHTML:()=>[{tag:"span",getAttrs:t=>!!t.hasAttribute("style")&&{}}],renderHTML({HTMLAttributes:t}){return["span",(0,r.KV)(this.options.HTMLAttributes,t),0]},addCommands(){return{removeEmptyTextStyle:()=>({state:t,commands:e})=>{const n=(0,r.z6)(t,this.type);return!!Object.entries(n).some((([,t])=>!!t))||e.unsetMark(this.name)}}}}),G=/^(\d+)\.\s$/,Y=r.bP.create({name:"orderedList",addOptions:()=>({itemTypeName:"listItem",HTMLAttributes:{},keepMarks:!1,keepAttributes:!1}),group:"block list",content(){return`${this.options.itemTypeName}+`},addAttributes:()=>({start:{default:1,parseHTML:t=>t.hasAttribute("start")?parseInt(t.getAttribute("start")||"",10):1}}),parseHTML:()=>[{tag:"ol"}],renderHTML({HTMLAttributes:t}){const{start:e,...n}=t;return 1===e?["ol",(0,r.KV)(this.options.HTMLAttributes,n),0]:["ol",(0,r.KV)(this.options.HTMLAttributes,t),0]},addCommands(){return{toggleOrderedList:()=>({commands:t,chain:e})=>this.options.keepAttributes?e().toggleList(this.name,this.options.itemTypeName,this.options.keepMarks).updateAttributes(q.name,this.editor.getAttributes(Z.name)).run():t.toggleList(this.name,this.options.itemTypeName,this.options.keepMarks)}},addKeyboardShortcuts(){return{"Mod-Shift-7":()=>this.editor.commands.toggleOrderedList()}},addInputRules(){let t=(0,r.tG)({find:G,type:this.type,getAttributes:t=>({start:+t[1]}),joinPredicate:(t,e)=>e.childCount+e.attrs.start===+t[1]});return(this.options.keepMarks||this.options.keepAttributes)&&(t=(0,r.tG)({find:G,type:this.type,keepMarks:this.options.keepMarks,keepAttributes:this.options.keepAttributes,getAttributes:t=>({start:+t[1],...this.editor.getAttributes(Z.name)}),joinPredicate:(t,e)=>e.childCount+e.attrs.start===+t[1],editor:this.editor})),[t]}}),X=r.bP.create({name:"paragraph",priority:1e3,addOptions:()=>({HTMLAttributes:{}}),group:"block",content:"inline*",parseHTML:()=>[{tag:"p"}],renderHTML({HTMLAttributes:t}){return["p",(0,r.KV)(this.options.HTMLAttributes,t),0]},addCommands(){return{setParagraph:()=>({commands:t})=>t.setNode(this.name)}},addKeyboardShortcuts(){return{"Mod-Alt-0":()=>this.editor.commands.setParagraph()}}}),Q=/(?:^|\s)(~~(?!\s+~~)((?:[^~]+))~~(?!\s+~~))$/,tt=/(?:^|\s)(~~(?!\s+~~)((?:[^~]+))~~(?!\s+~~))/g,et=r.CU.create({name:"strike",addOptions:()=>({HTMLAttributes:{}}),parseHTML:()=>[{tag:"s"},{tag:"del"},{tag:"strike"},{style:"text-decoration",consuming:!1,getAttrs:t=>!!t.includes("line-through")&&{}}],renderHTML({HTMLAttributes:t}){return["s",(0,r.KV)(this.options.HTMLAttributes,t),0]},addCommands(){return{setStrike:()=>({commands:t})=>t.setMark(this.name),toggleStrike:()=>({commands:t})=>t.toggleMark(this.name),unsetStrike:()=>({commands:t})=>t.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-Shift-s":()=>this.editor.commands.toggleStrike()}},addInputRules(){return[(0,r.OX)({find:Q,type:this.type})]},addPasteRules(){return[(0,r.Zc)({find:tt,type:this.type})]}}),nt=r.bP.create({name:"text",group:"inline"}),rt=r.YY.create({name:"starterKit",addExtensions(){var t,e,n,r,o,s,a,l,c,u,h,p,m,g,v,b,w,S;const M=[];return!1!==this.options.blockquote&&M.push(i.configure(null===(t=this.options)||void 0===t?void 0:t.blockquote)),!1!==this.options.bold&&M.push(d.configure(null===(e=this.options)||void 0===e?void 0:e.bold)),!1!==this.options.bulletList&&M.push(f.configure(null===(n=this.options)||void 0===n?void 0:n.bulletList)),!1!==this.options.code&&M.push(y.configure(null===(r=this.options)||void 0===r?void 0:r.code)),!1!==this.options.codeBlock&&M.push(x.configure(null===(o=this.options)||void 0===o?void 0:o.codeBlock)),!1!==this.options.document&&M.push(k.configure(null===(s=this.options)||void 0===s?void 0:s.document)),!1!==this.options.dropcursor&&M.push(A.configure(null===(a=this.options)||void 0===a?void 0:a.dropcursor)),!1!==this.options.gapcursor&&M.push(L.configure(null===(l=this.options)||void 0===l?void 0:l.gapcursor)),!1!==this.options.hardBreak&&M.push(I.configure(null===(c=this.options)||void 0===c?void 0:c.hardBreak)),!1!==this.options.heading&&M.push(F.configure(null===(u=this.options)||void 0===u?void 0:u.heading)),!1!==this.options.history&&M.push(j.B.configure(null===(h=this.options)||void 0===h?void 0:h.history)),!1!==this.options.horizontalRule&&M.push(B.configure(null===(p=this.options)||void 0===p?void 0:p.horizontalRule)),!1!==this.options.italic&&M.push(J.configure(null===(m=this.options)||void 0===m?void 0:m.italic)),!1!==this.options.listItem&&M.push(W.configure(null===(g=this.options)||void 0===g?void 0:g.listItem)),!1!==this.options.orderedList&&M.push(Y.configure(null===(v=this.options)||void 0===v?void 0:v.orderedList)),!1!==this.options.paragraph&&M.push(X.configure(null===(b=this.options)||void 0===b?void 0:b.paragraph)),!1!==this.options.strike&&M.push(et.configure(null===(w=this.options)||void 0===w?void 0:w.strike)),!1!==this.options.text&&M.push(nt.configure(null===(S=this.options)||void 0===S?void 0:S.text)),M}})},1318:(t,e,n)=>{"use strict";n.d(e,{OA:()=>r,WL:()=>i,u$:()=>o});const r={ATTRIBUTE:1,CHILD:2,PROPERTY:3,BOOLEAN_ATTRIBUTE:4,EVENT:5,ELEMENT:6},o=t=>(...e)=>({_$litDirective$:t,values:e});class i{constructor(t){}get _$AU(){return this._$AM._$AU}_$AT(t,e,n){this._$Ct=t,this._$AM=e,this._$Ci=n}_$AS(t,e){return this.update(t,e)}update(t,e){return this.render(...e)}}},7994:(t,e,n)=>{"use strict";n.d(e,{XX:()=>H,c0:()=>C,ge:()=>j,qy:()=>M,s6:()=>A});const r=globalThis,o=r.trustedTypes,i=o?o.createPolicy("lit-html",{createHTML:t=>t}):void 0,s="$lit$",a=`lit$${Math.random().toFixed(9).slice(2)}$`,l="?"+a,c=`<${l}>`,d=document,u=()=>d.createComment(""),h=t=>null===t||"object"!=typeof t&&"function"!=typeof t,p=Array.isArray,f=t=>p(t)||"function"==typeof t?.[Symbol.iterator],m="[ \t\n\f\r]",g=/<(?:(!--|\/[^a-zA-Z])|(\/?[a-zA-Z][^>\s]*)|(\/?$))/g,y=/-->/g,v=/>/g,b=RegExp(`>|${m}(?:([^\\s"'>=/]+)(${m}*=${m}*(?:[^ \t\n\f\r"'\`<>=]|("|')|))|$)`,"g"),w=/'/g,x=/"/g,k=/^(?:script|style|textarea|title)$/i,S=t=>(e,...n)=>({_$litType$:t,strings:e,values:n}),M=S(1),C=(S(2),Symbol.for("lit-noChange")),A=Symbol.for("lit-nothing"),T=new WeakMap,O=d.createTreeWalker(d,129);function E(t,e){if(!Array.isArray(t)||!t.hasOwnProperty("raw"))throw Error("invalid template strings array");return void 0!==i?i.createHTML(e):e}const _=(t,e)=>{const n=t.length-1,r=[];let o,i=2===e?"<svg>":"",l=g;for(let e=0;e<n;e++){const n=t[e];let d,u,h=-1,p=0;for(;p<n.length&&(l.lastIndex=p,u=l.exec(n),null!==u);)p=l.lastIndex,l===g?"!--"===u[1]?l=y:void 0!==u[1]?l=v:void 0!==u[2]?(k.test(u[2])&&(o=RegExp("</"+u[2],"g")),l=b):void 0!==u[3]&&(l=b):l===b?">"===u[0]?(l=o??g,h=-1):void 0===u[1]?h=-2:(h=l.lastIndex-u[2].length,d=u[1],l=void 0===u[3]?b:'"'===u[3]?x:w):l===x||l===w?l=b:l===y||l===v?l=g:(l=b,o=void 0);const f=l===b&&t[e+1].startsWith("/>")?" ":"";i+=l===g?n+c:h>=0?(r.push(d),n.slice(0,h)+s+n.slice(h)+a+f):n+a+(-2===h?e:f)}return[E(t,i+(t[n]||"<?>")+(2===e?"</svg>":"")),r]};class N{constructor({strings:t,_$litType$:e},n){let r;this.parts=[];let i=0,c=0;const d=t.length-1,h=this.parts,[p,f]=_(t,e);if(this.el=N.createElement(p,n),O.currentNode=this.el.content,2===e){const t=this.el.content.firstChild;t.replaceWith(...t.childNodes)}for(;null!==(r=O.nextNode())&&h.length<d;){if(1===r.nodeType){if(r.hasAttributes())for(const t of r.getAttributeNames())if(t.endsWith(s)){const e=f[c++],n=r.getAttribute(t).split(a),o=/([.?@])?(.*)/.exec(e);h.push({type:1,index:i,name:o[2],strings:n,ctor:"."===o[1]?D:"?"===o[1]?L:"@"===o[1]?I:P}),r.removeAttribute(t)}else t.startsWith(a)&&(h.push({type:6,index:i}),r.removeAttribute(t));if(k.test(r.tagName)){const t=r.textContent.split(a),e=t.length-1;if(e>0){r.textContent=o?o.emptyScript:"";for(let n=0;n<e;n++)r.append(t[n],u()),O.nextNode(),h.push({type:2,index:++i});r.append(t[e],u())}}}else if(8===r.nodeType)if(r.data===l)h.push({type:2,index:i});else{let t=-1;for(;-1!==(t=r.data.indexOf(a,t+1));)h.push({type:7,index:i}),t+=a.length-1}i++}}static createElement(t,e){const n=d.createElement("template");return n.innerHTML=t,n}}function $(t,e,n=t,r){if(e===C)return e;let o=void 0!==r?n._$Co?.[r]:n._$Cl;const i=h(e)?void 0:e._$litDirective$;return o?.constructor!==i&&(o?._$AO?.(!1),void 0===i?o=void 0:(o=new i(t),o._$AT(t,n,r)),void 0!==r?(n._$Co??=[])[r]=o:n._$Cl=o),void 0!==o&&(e=$(t,o._$AS(t,e.values),o,r)),e}class R{constructor(t,e){this._$AV=[],this._$AN=void 0,this._$AD=t,this._$AM=e}get parentNode(){return this._$AM.parentNode}get _$AU(){return this._$AM._$AU}u(t){const{el:{content:e},parts:n}=this._$AD,r=(t?.creationScope??d).importNode(e,!0);O.currentNode=r;let o=O.nextNode(),i=0,s=0,a=n[0];for(;void 0!==a;){if(i===a.index){let e;2===a.type?e=new z(o,o.nextSibling,this,t):1===a.type?e=new a.ctor(o,a.name,a.strings,this,t):6===a.type&&(e=new F(o,this,t)),this._$AV.push(e),a=n[++s]}i!==a?.index&&(o=O.nextNode(),i++)}return O.currentNode=d,r}p(t){let e=0;for(const n of this._$AV)void 0!==n&&(void 0!==n.strings?(n._$AI(t,n,e),e+=n.strings.length-2):n._$AI(t[e])),e++}}class z{get _$AU(){return this._$AM?._$AU??this._$Cv}constructor(t,e,n,r){this.type=2,this._$AH=A,this._$AN=void 0,this._$AA=t,this._$AB=e,this._$AM=n,this.options=r,this._$Cv=r?.isConnected??!0}get parentNode(){let t=this._$AA.parentNode;const e=this._$AM;return void 0!==e&&11===t?.nodeType&&(t=e.parentNode),t}get startNode(){return this._$AA}get endNode(){return this._$AB}_$AI(t,e=this){t=$(this,t,e),h(t)?t===A||null==t||""===t?(this._$AH!==A&&this._$AR(),this._$AH=A):t!==this._$AH&&t!==C&&this._(t):void 0!==t._$litType$?this.$(t):void 0!==t.nodeType?this.T(t):f(t)?this.k(t):this._(t)}S(t){return this._$AA.parentNode.insertBefore(t,this._$AB)}T(t){this._$AH!==t&&(this._$AR(),this._$AH=this.S(t))}_(t){this._$AH!==A&&h(this._$AH)?this._$AA.nextSibling.data=t:this.T(d.createTextNode(t)),this._$AH=t}$(t){const{values:e,_$litType$:n}=t,r="number"==typeof n?this._$AC(t):(void 0===n.el&&(n.el=N.createElement(E(n.h,n.h[0]),this.options)),n);if(this._$AH?._$AD===r)this._$AH.p(e);else{const t=new R(r,this),n=t.u(this.options);t.p(e),this.T(n),this._$AH=t}}_$AC(t){let e=T.get(t.strings);return void 0===e&&T.set(t.strings,e=new N(t)),e}k(t){p(this._$AH)||(this._$AH=[],this._$AR());const e=this._$AH;let n,r=0;for(const o of t)r===e.length?e.push(n=new z(this.S(u()),this.S(u()),this,this.options)):n=e[r],n._$AI(o),r++;r<e.length&&(this._$AR(n&&n._$AB.nextSibling,r),e.length=r)}_$AR(t=this._$AA.nextSibling,e){for(this._$AP?.(!1,!0,e);t&&t!==this._$AB;){const e=t.nextSibling;t.remove(),t=e}}setConnected(t){void 0===this._$AM&&(this._$Cv=t,this._$AP?.(t))}}class P{get tagName(){return this.element.tagName}get _$AU(){return this._$AM._$AU}constructor(t,e,n,r,o){this.type=1,this._$AH=A,this._$AN=void 0,this.element=t,this.name=e,this._$AM=r,this.options=o,n.length>2||""!==n[0]||""!==n[1]?(this._$AH=Array(n.length-1).fill(new String),this.strings=n):this._$AH=A}_$AI(t,e=this,n,r){const o=this.strings;let i=!1;if(void 0===o)t=$(this,t,e,0),i=!h(t)||t!==this._$AH&&t!==C,i&&(this._$AH=t);else{const r=t;let s,a;for(t=o[0],s=0;s<o.length-1;s++)a=$(this,r[n+s],e,s),a===C&&(a=this._$AH[s]),i||=!h(a)||a!==this._$AH[s],a===A?t=A:t!==A&&(t+=(a??"")+o[s+1]),this._$AH[s]=a}i&&!r&&this.j(t)}j(t){t===A?this.element.removeAttribute(this.name):this.element.setAttribute(this.name,t??"")}}class D extends P{constructor(){super(...arguments),this.type=3}j(t){this.element[this.name]=t===A?void 0:t}}class L extends P{constructor(){super(...arguments),this.type=4}j(t){this.element.toggleAttribute(this.name,!!t&&t!==A)}}class I extends P{constructor(t,e,n,r,o){super(t,e,n,r,o),this.type=5}_$AI(t,e=this){if((t=$(this,t,e,0)??A)===C)return;const n=this._$AH,r=t===A&&n!==A||t.capture!==n.capture||t.once!==n.once||t.passive!==n.passive,o=t!==A&&(n===A||r);r&&this.element.removeEventListener(this.name,this,n),o&&this.element.addEventListener(this.name,this,t),this._$AH=t}handleEvent(t){"function"==typeof this._$AH?this._$AH.call(this.options?.host??this.element,t):this._$AH.handleEvent(t)}}class F{constructor(t,e,n){this.element=t,this.type=6,this._$AN=void 0,this._$AM=e,this.options=n}get _$AU(){return this._$AM._$AU}_$AI(t){$(this,t)}}const j={P:s,A:a,C:l,M:1,L:_,R,D:f,V:$,I:z,H:P,N:L,U:I,B:D,F},B=r.litHtmlPolyfillSupport;B?.(N,z),(r.litHtmlVersions??=[]).push("3.1.4");const H=(t,e,n)=>{const r=n?.renderBefore??e;let o=r._$litPart$;if(void 0===o){const t=n?.renderBefore??null;r._$litPart$=o=new z(e.insertBefore(u(),t),t,void 0,n??{})}return o._$AI(t),o}},9701:(t,e,n)=>{"use strict";n.d(e,{EM:()=>r,MZ:()=>a,P:()=>d,KN:()=>u,wk:()=>l});const r=t=>(e,n)=>{void 0!==n?n.addInitializer((()=>{customElements.define(t,e)})):customElements.define(t,e)};var o=n(7601);const i={attribute:!0,type:String,converter:o.W3,reflect:!1,hasChanged:o.Ec},s=(t=i,e,n)=>{const{kind:r,metadata:o}=n;let s=globalThis.litPropertyMetadata.get(o);if(void 0===s&&globalThis.litPropertyMetadata.set(o,s=new Map),s.set(n.name,t),"accessor"===r){const{name:r}=n;return{set(n){const o=e.get.call(this);e.set.call(this,n),this.requestUpdate(r,o,t)},init(e){return void 0!==e&&this.P(r,void 0,t),e}}}if("setter"===r){const{name:r}=n;return function(n){const o=this[r];e.call(this,n),this.requestUpdate(r,o,t)}}throw Error("Unsupported decorator location: "+r)};function a(t){return(e,n)=>"object"==typeof n?s(t,e,n):((t,e,n)=>{const r=e.hasOwnProperty(n);return e.constructor.createProperty(n,r?{...t,wrapped:!0}:t),r?Object.getOwnPropertyDescriptor(e,n):void 0})(t,e,n)}function l(t){return a({...t,state:!0,attribute:!1})}const c=(t,e,n)=>(n.configurable=!0,n.enumerable=!0,Reflect.decorate&&"object"!=typeof e&&Object.defineProperty(t,e,n),n);function d(t,e){return(n,r,o)=>{const i=e=>e.renderRoot?.querySelector(t)??null;if(e){const{get:t,set:e}="object"==typeof r?n:o??(()=>{const t=Symbol();return{get(){return this[t]},set(e){this[t]=e}}})();return c(n,r,{get(){let n=t.call(this);return void 0===n&&(n=i(this),(null!==n||this.hasUpdated)&&e.call(this,n)),n}})}return c(n,r,{get(){return i(this)}})}}function u(t){return(e,n)=>{const{slot:r,selector:o}=t??{},i="slot"+(r?`[name=${r}]`:":not([name])");return c(e,n,{get(){const e=this.renderRoot?.querySelector(i),n=e?.assignedElements(t)??[];return void 0===o?n:n.filter((t=>t.matches(o)))}})}}},6678:(t,e,n)=>{"use strict";n.d(e,{H:()=>i});var r=n(7994),o=n(1318);const i=(0,o.u$)(class extends o.WL{constructor(t){if(super(t),t.type!==o.OA.ATTRIBUTE||"class"!==t.name||t.strings?.length>2)throw Error("`classMap()` can only be used in the `class` attribute and must be the only part in the attribute.")}render(t){return" "+Object.keys(t).filter((e=>t[e])).join(" ")+" "}update(t,[e]){if(void 0===this.st){this.st=new Set,void 0!==t.strings&&(this.nt=new Set(t.strings.join(" ").split(/\s/).filter((t=>""!==t))));for(const t in e)e[t]&&!this.nt?.has(t)&&this.st.add(t);return this.render(e)}const n=t.element.classList;for(const t of this.st)t in e||(n.remove(t),this.st.delete(t));for(const t in e){const r=!!e[t];r===this.st.has(t)||this.nt?.has(t)||(r?(n.add(t),this.st.add(t)):(n.remove(t),this.st.delete(t)))}return r.c0}})},6394:(t,e,n)=>{"use strict";n.d(e,{_:()=>p,K:()=>g});var r=n(7994);const{I:o}=r.ge;var i=n(1318);const s=(t,e)=>{const n=t._$AN;if(void 0===n)return!1;for(const t of n)t._$AO?.(e,!1),s(t,e);return!0},a=t=>{let e,n;do{if(void 0===(e=t._$AM))break;n=e._$AN,n.delete(t),t=e}while(0===n?.size)},l=t=>{for(let e;e=t._$AM;t=e){let n=e._$AN;if(void 0===n)e._$AN=n=new Set;else if(n.has(t))break;n.add(t),u(e)}};function c(t){void 0!==this._$AN?(a(this),this._$AM=t,l(this)):this._$AM=t}function d(t,e=!1,n=0){const r=this._$AH,o=this._$AN;if(void 0!==o&&0!==o.size)if(e)if(Array.isArray(r))for(let t=n;t<r.length;t++)s(r[t],!1),a(r[t]);else null!=r&&(s(r,!1),a(r));else s(this,t)}const u=t=>{t.type==i.OA.CHILD&&(t._$AP??=d,t._$AQ??=c)};class h extends i.WL{constructor(){super(...arguments),this._$AN=void 0}_$AT(t,e,n){super._$AT(t,e,n),l(this),this.isConnected=t._$AU}_$AO(t,e=!0){t!==this.isConnected&&(this.isConnected=t,t?this.reconnected?.():this.disconnected?.()),e&&(s(this,t),a(this))}setValue(t){if((t=>void 0===this._$Ct.strings)())this._$Ct._$AI(t,this);else{const e=[...this._$Ct._$AH];e[this._$Ci]=t,this._$Ct._$AI(e,this,0)}}disconnected(){}reconnected(){}}const p=()=>new f;class f{}const m=new WeakMap,g=(0,i.u$)(class extends h{render(t){return r.s6}update(t,[e]){const n=e!==this.Y;return n&&void 0!==this.Y&&this.rt(void 0),(n||this.lt!==this.ct)&&(this.Y=e,this.ht=t.options?.host,this.rt(this.ct=t.element)),r.s6}rt(t){if(this.isConnected||(t=void 0),"function"==typeof this.Y){const e=this.ht??globalThis;let n=m.get(e);void 0===n&&(n=new WeakMap,m.set(e,n)),void 0!==n.get(this.Y)&&this.Y.call(this.ht,void 0),n.set(this.Y,t),void 0!==t&&this.Y.call(this.ht,t)}else this.Y.value=t}get lt(){return"function"==typeof this.Y?m.get(this.ht??globalThis)?.get(this.Y):this.Y?.value}disconnected(){this.lt===this.ct&&this.rt(void 0)}reconnected(){this.rt(this.ct)}})},1514:(t,e,n)=>{"use strict";n.d(e,{W:()=>a});var r=n(7994),o=n(1318);const i="important",s=" !"+i,a=(0,o.u$)(class extends o.WL{constructor(t){if(super(t),t.type!==o.OA.ATTRIBUTE||"style"!==t.name||t.strings?.length>2)throw Error("The `styleMap` directive must be used in the `style` attribute and must be the only part in the attribute.")}render(t){return Object.keys(t).reduce(((e,n)=>{const r=t[n];return null==r?e:e+`${n=n.includes("-")?n:n.replace(/(?:^(webkit|moz|ms|o)|)(?=[A-Z])/g,"-$&").toLowerCase()}:${r};`}),"")}update(t,[e]){const{style:n}=t.element;if(void 0===this.ft)return this.ft=new Set(Object.keys(e)),this.render(e);for(const t of this.ft)null==e[t]&&(this.ft.delete(t),t.includes("-")?n.removeProperty(t):n[t]=null);for(const t in e){const r=e[t];if(null!=r){this.ft.add(t);const e="string"==typeof r&&r.endsWith(s);t.includes("-")||e?n.setProperty(t,e?r.slice(0,-11):r,e?i:""):n[t]=r}}return r.c0}})},1959:(t,e,n)=>{"use strict";n.d(e,{_:()=>s});var r=n(7994),o=n(1318);class i extends o.WL{constructor(t){if(super(t),this.it=r.s6,t.type!==o.OA.CHILD)throw Error(this.constructor.directiveName+"() can only be used in child bindings")}render(t){if(t===r.s6||null==t)return this._t=void 0,this.it=t;if(t===r.c0)return t;if("string"!=typeof t)throw Error(this.constructor.directiveName+"() called with a non-string value");if(t===this.it)return this._t;this.it=t;const e=[t];return e.raw=e,this._t={_$litType$:this.constructor.resultType,strings:e,values:[]}}}i.directiveName="unsafeHTML",i.resultType=1;const s=(0,o.u$)(i)},2835:(t,e,n)=>{"use strict";n.d(e,{WF:()=>i,AH:()=>r.AH,qy:()=>o.qy,s6:()=>o.s6});var r=n(7601),o=n(7994);class i extends r.mN{constructor(){super(...arguments),this.renderOptions={host:this},this._$Do=void 0}createRenderRoot(){const t=super.createRenderRoot();return this.renderOptions.renderBefore??=t.firstChild,t}update(t){const e=this.render();this.hasUpdated||(this.renderOptions.isConnected=this.isConnected),super.update(t),this._$Do=(0,o.XX)(e,this.renderRoot,this.renderOptions)}connectedCallback(){super.connectedCallback(),this._$Do?.setConnected(!0)}disconnectedCallback(){super.disconnectedCallback(),this._$Do?.setConnected(!1)}render(){return o.c0}}i._$litElement$=!0,i.finalized=!0,globalThis.litElementHydrateSupport?.({LitElement:i});const s=globalThis.litElementPolyfillSupport;s?.({LitElement:i}),(globalThis.litElementVersions??=[]).push("4.0.6")},1804:(t,e,n)=>{"use strict";n.d(e,{K:()=>f,w:()=>p});for(var r={8:"Backspace",9:"Tab",10:"Enter",12:"NumLock",13:"Enter",16:"Shift",17:"Control",18:"Alt",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",44:"PrintScreen",45:"Insert",46:"Delete",59:";",61:"=",91:"Meta",92:"Meta",106:"*",107:"+",108:",",109:"-",110:".",111:"/",144:"NumLock",145:"ScrollLock",160:"Shift",161:"Shift",162:"Control",163:"Control",164:"Alt",165:"Alt",173:"-",186:";",187:"=",188:",",189:"-",190:".",191:"/",192:"`",219:"[",220:"\\",221:"]",222:"'"},o={48:")",49:"!",50:"@",51:"#",52:"$",53:"%",54:"^",55:"&",56:"*",57:"(",59:":",61:"+",173:"_",186:":",187:"+",188:"<",189:"_",190:">",191:"?",192:"~",219:"{",220:"|",221:"}",222:'"'},i="undefined"!=typeof navigator&&/Mac/.test(navigator.platform),s="undefined"!=typeof navigator&&/MSIE \d|Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(navigator.userAgent),a=0;a<10;a++)r[48+a]=r[96+a]=String(a);for(a=1;a<=24;a++)r[a+111]="F"+a;for(a=65;a<=90;a++)r[a]=String.fromCharCode(a+32),o[a]=String.fromCharCode(a);for(var l in r)o.hasOwnProperty(l)||(o[l]=r[l]);var c=n(2559);const d="undefined"!=typeof navigator&&/Mac|iP(hone|[oa]d)/.test(navigator.platform);function u(t){let e,n,r,o,i=t.split(/-(?!$)/),s=i[i.length-1];"Space"==s&&(s=" ");for(let t=0;t<i.length-1;t++){let s=i[t];if(/^(cmd|meta|m)$/i.test(s))o=!0;else if(/^a(lt)?$/i.test(s))e=!0;else if(/^(c|ctrl|control)$/i.test(s))n=!0;else if(/^s(hift)?$/i.test(s))r=!0;else{if(!/^mod$/i.test(s))throw new Error("Unrecognized modifier name: "+s);d?o=!0:n=!0}}return e&&(s="Alt-"+s),n&&(s="Ctrl-"+s),o&&(s="Meta-"+s),r&&(s="Shift-"+s),s}function h(t,e,n=!0){return e.altKey&&(t="Alt-"+t),e.ctrlKey&&(t="Ctrl-"+t),e.metaKey&&(t="Meta-"+t),n&&e.shiftKey&&(t="Shift-"+t),t}function p(t){return new c.k_({props:{handleKeyDown:f(t)}})}function f(t){let e=function(t){let e=Object.create(null);for(let n in t)e[u(n)]=t[n];return e}(t);return function(t,n){let a,l=function(t){var e=!(i&&t.metaKey&&t.shiftKey&&!t.ctrlKey&&!t.altKey||s&&t.shiftKey&&t.key&&1==t.key.length||"Unidentified"==t.key)&&t.key||(t.shiftKey?o:r)[t.keyCode]||t.key||"Unidentified";return"Esc"==e&&(e="Escape"),"Del"==e&&(e="Delete"),"Left"==e&&(e="ArrowLeft"),"Up"==e&&(e="ArrowUp"),"Right"==e&&(e="ArrowRight"),"Down"==e&&(e="ArrowDown"),e}(n),c=e[h(l,n)];if(c&&c(t.state,t.dispatch,t))return!0;if(1==l.length&&" "!=l){if(n.shiftKey){let r=e[h(l,n,!1)];if(r&&r(t.state,t.dispatch,t))return!0}if((n.shiftKey||n.altKey||n.metaKey||l.charCodeAt(0)>127)&&(a=r[n.keyCode])&&a!=l){let r=e[h(a,n)];if(r&&r(t.state,t.dispatch,t))return!0}}return!1}}},9679:(t,e,n)=>{"use strict";function r(t){this.content=t}n.d(e,{S4:()=>Y,ZF:()=>at,FK:()=>a,CU:()=>u,sX:()=>q,bP:()=>N,u$:()=>E,vI:()=>h,Sj:()=>Z,Ji:()=>p}),r.prototype={constructor:r,find:function(t){for(var e=0;e<this.content.length;e+=2)if(this.content[e]===t)return e;return-1},get:function(t){var e=this.find(t);return-1==e?void 0:this.content[e+1]},update:function(t,e,n){var o=n&&n!=t?this.remove(n):this,i=o.find(t),s=o.content.slice();return-1==i?s.push(n||t,e):(s[i+1]=e,n&&(s[i]=n)),new r(s)},remove:function(t){var e=this.find(t);if(-1==e)return this;var n=this.content.slice();return n.splice(e,2),new r(n)},addToStart:function(t,e){return new r([t,e].concat(this.remove(t).content))},addToEnd:function(t,e){var n=this.remove(t).content.slice();return n.push(t,e),new r(n)},addBefore:function(t,e,n){var o=this.remove(e),i=o.content.slice(),s=o.find(t);return i.splice(-1==s?i.length:s,0,e,n),new r(i)},forEach:function(t){for(var e=0;e<this.content.length;e+=2)t(this.content[e],this.content[e+1])},prepend:function(t){return(t=r.from(t)).size?new r(t.content.concat(this.subtract(t).content)):this},append:function(t){return(t=r.from(t)).size?new r(this.subtract(t).content.concat(t.content)):this},subtract:function(t){var e=this;t=r.from(t);for(var n=0;n<t.content.length;n+=2)e=e.remove(t.content[n]);return e},toObject:function(){var t={};return this.forEach((function(e,n){t[e]=n})),t},get size(){return this.content.length>>1}},r.from=function(t){if(t instanceof r)return t;var e=[];if(t)for(var n in t)e.push(n,t[n]);return new r(e)};const o=r;function i(t,e,n){for(let r=0;;r++){if(r==t.childCount||r==e.childCount)return t.childCount==e.childCount?null:n;let o=t.child(r),s=e.child(r);if(o!=s){if(!o.sameMarkup(s))return n;if(o.isText&&o.text!=s.text){for(let t=0;o.text[t]==s.text[t];t++)n++;return n}if(o.content.size||s.content.size){let t=i(o.content,s.content,n+1);if(null!=t)return t}n+=o.nodeSize}else n+=o.nodeSize}}function s(t,e,n,r){for(let o=t.childCount,i=e.childCount;;){if(0==o||0==i)return o==i?null:{a:n,b:r};let a=t.child(--o),l=e.child(--i),c=a.nodeSize;if(a!=l){if(!a.sameMarkup(l))return{a:n,b:r};if(a.isText&&a.text!=l.text){let t=0,e=Math.min(a.text.length,l.text.length);for(;t<e&&a.text[a.text.length-t-1]==l.text[l.text.length-t-1];)t++,n--,r--;return{a:n,b:r}}if(a.content.size||l.content.size){let t=s(a.content,l.content,n-1,r-1);if(t)return t}n-=c,r-=c}else n-=c,r-=c}}class a{constructor(t,e){if(this.content=t,this.size=e||0,null==e)for(let e=0;e<t.length;e++)this.size+=t[e].nodeSize}nodesBetween(t,e,n,r=0,o){for(let i=0,s=0;s<e;i++){let a=this.content[i],l=s+a.nodeSize;if(l>t&&!1!==n(a,r+s,o||null,i)&&a.content.size){let o=s+1;a.nodesBetween(Math.max(0,t-o),Math.min(a.content.size,e-o),n,r+o)}s=l}}descendants(t){this.nodesBetween(0,this.size,t)}textBetween(t,e,n,r){let o="",i=!0;return this.nodesBetween(t,e,((s,a)=>{let l=s.isText?s.text.slice(Math.max(t,a)-a,e-a):s.isLeaf?r?"function"==typeof r?r(s):r:s.type.spec.leafText?s.type.spec.leafText(s):"":"";s.isBlock&&(s.isLeaf&&l||s.isTextblock)&&n&&(i?i=!1:o+=n),o+=l}),0),o}append(t){if(!t.size)return this;if(!this.size)return t;let e=this.lastChild,n=t.firstChild,r=this.content.slice(),o=0;for(e.isText&&e.sameMarkup(n)&&(r[r.length-1]=e.withText(e.text+n.text),o=1);o<t.content.length;o++)r.push(t.content[o]);return new a(r,this.size+t.size)}cut(t,e=this.size){if(0==t&&e==this.size)return this;let n=[],r=0;if(e>t)for(let o=0,i=0;i<e;o++){let s=this.content[o],a=i+s.nodeSize;a>t&&((i<t||a>e)&&(s=s.isText?s.cut(Math.max(0,t-i),Math.min(s.text.length,e-i)):s.cut(Math.max(0,t-i-1),Math.min(s.content.size,e-i-1))),n.push(s),r+=s.nodeSize),i=a}return new a(n,r)}cutByIndex(t,e){return t==e?a.empty:0==t&&e==this.content.length?this:new a(this.content.slice(t,e))}replaceChild(t,e){let n=this.content[t];if(n==e)return this;let r=this.content.slice(),o=this.size+e.nodeSize-n.nodeSize;return r[t]=e,new a(r,o)}addToStart(t){return new a([t].concat(this.content),this.size+t.nodeSize)}addToEnd(t){return new a(this.content.concat(t),this.size+t.nodeSize)}eq(t){if(this.content.length!=t.content.length)return!1;for(let e=0;e<this.content.length;e++)if(!this.content[e].eq(t.content[e]))return!1;return!0}get firstChild(){return this.content.length?this.content[0]:null}get lastChild(){return this.content.length?this.content[this.content.length-1]:null}get childCount(){return this.content.length}child(t){let e=this.content[t];if(!e)throw new RangeError("Index "+t+" out of range for "+this);return e}maybeChild(t){return this.content[t]||null}forEach(t){for(let e=0,n=0;e<this.content.length;e++){let r=this.content[e];t(r,n,e),n+=r.nodeSize}}findDiffStart(t,e=0){return i(this,t,e)}findDiffEnd(t,e=this.size,n=t.size){return s(this,t,e,n)}findIndex(t,e=-1){if(0==t)return c(0,t);if(t==this.size)return c(this.content.length,t);if(t>this.size||t<0)throw new RangeError(`Position ${t} outside of fragment (${this})`);for(let n=0,r=0;;n++){let o=r+this.child(n).nodeSize;if(o>=t)return o==t||e>0?c(n+1,o):c(n,r);r=o}}toString(){return"<"+this.toStringInner()+">"}toStringInner(){return this.content.join(", ")}toJSON(){return this.content.length?this.content.map((t=>t.toJSON())):null}static fromJSON(t,e){if(!e)return a.empty;if(!Array.isArray(e))throw new RangeError("Invalid input for Fragment.fromJSON");return new a(e.map(t.nodeFromJSON))}static fromArray(t){if(!t.length)return a.empty;let e,n=0;for(let r=0;r<t.length;r++){let o=t[r];n+=o.nodeSize,r&&o.isText&&t[r-1].sameMarkup(o)?(e||(e=t.slice(0,r)),e[e.length-1]=o.withText(e[e.length-1].text+o.text)):e&&e.push(o)}return new a(e||t,n)}static from(t){if(!t)return a.empty;if(t instanceof a)return t;if(Array.isArray(t))return this.fromArray(t);if(t.attrs)return new a([t],t.nodeSize);throw new RangeError("Can not convert "+t+" to a Fragment"+(t.nodesBetween?" (looks like multiple versions of prosemirror-model were loaded)":""))}}a.empty=new a([],0);const l={index:0,offset:0};function c(t,e){return l.index=t,l.offset=e,l}function d(t,e){if(t===e)return!0;if(!t||"object"!=typeof t||!e||"object"!=typeof e)return!1;let n=Array.isArray(t);if(Array.isArray(e)!=n)return!1;if(n){if(t.length!=e.length)return!1;for(let n=0;n<t.length;n++)if(!d(t[n],e[n]))return!1}else{for(let n in t)if(!(n in e)||!d(t[n],e[n]))return!1;for(let n in e)if(!(n in t))return!1}return!0}class u{constructor(t,e){this.type=t,this.attrs=e}addToSet(t){let e,n=!1;for(let r=0;r<t.length;r++){let o=t[r];if(this.eq(o))return t;if(this.type.excludes(o.type))e||(e=t.slice(0,r));else{if(o.type.excludes(this.type))return t;!n&&o.type.rank>this.type.rank&&(e||(e=t.slice(0,r)),e.push(this),n=!0),e&&e.push(o)}}return e||(e=t.slice()),n||e.push(this),e}removeFromSet(t){for(let e=0;e<t.length;e++)if(this.eq(t[e]))return t.slice(0,e).concat(t.slice(e+1));return t}isInSet(t){for(let e=0;e<t.length;e++)if(this.eq(t[e]))return!0;return!1}eq(t){return this==t||this.type==t.type&&d(this.attrs,t.attrs)}toJSON(){let t={type:this.type.name};for(let e in this.attrs){t.attrs=this.attrs;break}return t}static fromJSON(t,e){if(!e)throw new RangeError("Invalid input for Mark.fromJSON");let n=t.marks[e.type];if(!n)throw new RangeError(`There is no mark type ${e.type} in this schema`);return n.create(e.attrs)}static sameSet(t,e){if(t==e)return!0;if(t.length!=e.length)return!1;for(let n=0;n<t.length;n++)if(!t[n].eq(e[n]))return!1;return!0}static setFrom(t){if(!t||Array.isArray(t)&&0==t.length)return u.none;if(t instanceof u)return[t];let e=t.slice();return e.sort(((t,e)=>t.type.rank-e.type.rank)),e}}u.none=[];class h extends Error{}class p{constructor(t,e,n){this.content=t,this.openStart=e,this.openEnd=n}get size(){return this.content.size-this.openStart-this.openEnd}insertAt(t,e){let n=m(this.content,t+this.openStart,e);return n&&new p(n,this.openStart,this.openEnd)}removeBetween(t,e){return new p(f(this.content,t+this.openStart,e+this.openStart),this.openStart,this.openEnd)}eq(t){return this.content.eq(t.content)&&this.openStart==t.openStart&&this.openEnd==t.openEnd}toString(){return this.content+"("+this.openStart+","+this.openEnd+")"}toJSON(){if(!this.content.size)return null;let t={content:this.content.toJSON()};return this.openStart>0&&(t.openStart=this.openStart),this.openEnd>0&&(t.openEnd=this.openEnd),t}static fromJSON(t,e){if(!e)return p.empty;let n=e.openStart||0,r=e.openEnd||0;if("number"!=typeof n||"number"!=typeof r)throw new RangeError("Invalid input for Slice.fromJSON");return new p(a.fromJSON(t,e.content),n,r)}static maxOpen(t,e=!0){let n=0,r=0;for(let r=t.firstChild;r&&!r.isLeaf&&(e||!r.type.spec.isolating);r=r.firstChild)n++;for(let n=t.lastChild;n&&!n.isLeaf&&(e||!n.type.spec.isolating);n=n.lastChild)r++;return new p(t,n,r)}}function f(t,e,n){let{index:r,offset:o}=t.findIndex(e),i=t.maybeChild(r),{index:s,offset:a}=t.findIndex(n);if(o==e||i.isText){if(a!=n&&!t.child(s).isText)throw new RangeError("Removing non-flat range");return t.cut(0,e).append(t.cut(n))}if(r!=s)throw new RangeError("Removing non-flat range");return t.replaceChild(r,i.copy(f(i.content,e-o-1,n-o-1)))}function m(t,e,n,r){let{index:o,offset:i}=t.findIndex(e),s=t.maybeChild(o);if(i==e||s.isText)return r&&!r.canReplace(o,o,n)?null:t.cut(0,e).append(n).append(t.cut(e));let a=m(s.content,e-i-1,n);return a&&t.replaceChild(o,s.copy(a))}function g(t,e,n){if(n.openStart>t.depth)throw new h("Inserted content deeper than insertion position");if(t.depth-n.openStart!=e.depth-n.openEnd)throw new h("Inconsistent open depths");return y(t,e,n,0)}function y(t,e,n,r){let o=t.index(r),i=t.node(r);if(o==e.index(r)&&r<t.depth-n.openStart){let s=y(t,e,n,r+1);return i.copy(i.content.replaceChild(o,s))}if(n.content.size){if(n.openStart||n.openEnd||t.depth!=r||e.depth!=r){let{start:o,end:s}=function(t,e){let n=e.depth-t.openStart,r=e.node(n).copy(t.content);for(let t=n-1;t>=0;t--)r=e.node(t).copy(a.from(r));return{start:r.resolveNoCache(t.openStart+n),end:r.resolveNoCache(r.content.size-t.openEnd-n)}}(n,t);return k(i,S(t,o,s,e,r))}{let r=t.parent,o=r.content;return k(r,o.cut(0,t.parentOffset).append(n.content).append(o.cut(e.parentOffset)))}}return k(i,M(t,e,r))}function v(t,e){if(!e.type.compatibleContent(t.type))throw new h("Cannot join "+e.type.name+" onto "+t.type.name)}function b(t,e,n){let r=t.node(n);return v(r,e.node(n)),r}function w(t,e){let n=e.length-1;n>=0&&t.isText&&t.sameMarkup(e[n])?e[n]=t.withText(e[n].text+t.text):e.push(t)}function x(t,e,n,r){let o=(e||t).node(n),i=0,s=e?e.index(n):o.childCount;t&&(i=t.index(n),t.depth>n?i++:t.textOffset&&(w(t.nodeAfter,r),i++));for(let t=i;t<s;t++)w(o.child(t),r);e&&e.depth==n&&e.textOffset&&w(e.nodeBefore,r)}function k(t,e){return t.type.checkContent(e),t.copy(e)}function S(t,e,n,r,o){let i=t.depth>o&&b(t,e,o+1),s=r.depth>o&&b(n,r,o+1),l=[];return x(null,t,o,l),i&&s&&e.index(o)==n.index(o)?(v(i,s),w(k(i,S(t,e,n,r,o+1)),l)):(i&&w(k(i,M(t,e,o+1)),l),x(e,n,o,l),s&&w(k(s,M(n,r,o+1)),l)),x(r,null,o,l),new a(l)}function M(t,e,n){let r=[];return x(null,t,n,r),t.depth>n&&w(k(b(t,e,n+1),M(t,e,n+1)),r),x(e,null,n,r),new a(r)}p.empty=new p(a.empty,0,0);class C{constructor(t,e,n){this.pos=t,this.path=e,this.parentOffset=n,this.depth=e.length/3-1}resolveDepth(t){return null==t?this.depth:t<0?this.depth+t:t}get parent(){return this.node(this.depth)}get doc(){return this.node(0)}node(t){return this.path[3*this.resolveDepth(t)]}index(t){return this.path[3*this.resolveDepth(t)+1]}indexAfter(t){return t=this.resolveDepth(t),this.index(t)+(t!=this.depth||this.textOffset?1:0)}start(t){return 0==(t=this.resolveDepth(t))?0:this.path[3*t-1]+1}end(t){return t=this.resolveDepth(t),this.start(t)+this.node(t).content.size}before(t){if(!(t=this.resolveDepth(t)))throw new RangeError("There is no position before the top-level node");return t==this.depth+1?this.pos:this.path[3*t-1]}after(t){if(!(t=this.resolveDepth(t)))throw new RangeError("There is no position after the top-level node");return t==this.depth+1?this.pos:this.path[3*t-1]+this.path[3*t].nodeSize}get textOffset(){return this.pos-this.path[this.path.length-1]}get nodeAfter(){let t=this.parent,e=this.index(this.depth);if(e==t.childCount)return null;let n=this.pos-this.path[this.path.length-1],r=t.child(e);return n?t.child(e).cut(n):r}get nodeBefore(){let t=this.index(this.depth),e=this.pos-this.path[this.path.length-1];return e?this.parent.child(t).cut(0,e):0==t?null:this.parent.child(t-1)}posAtIndex(t,e){e=this.resolveDepth(e);let n=this.path[3*e],r=0==e?0:this.path[3*e-1]+1;for(let e=0;e<t;e++)r+=n.child(e).nodeSize;return r}marks(){let t=this.parent,e=this.index();if(0==t.content.size)return u.none;if(this.textOffset)return t.child(e).marks;let n=t.maybeChild(e-1),r=t.maybeChild(e);if(!n){let t=n;n=r,r=t}let o=n.marks;for(var i=0;i<o.length;i++)!1!==o[i].type.spec.inclusive||r&&o[i].isInSet(r.marks)||(o=o[i--].removeFromSet(o));return o}marksAcross(t){let e=this.parent.maybeChild(this.index());if(!e||!e.isInline)return null;let n=e.marks,r=t.parent.maybeChild(t.index());for(var o=0;o<n.length;o++)!1!==n[o].type.spec.inclusive||r&&n[o].isInSet(r.marks)||(n=n[o--].removeFromSet(n));return n}sharedDepth(t){for(let e=this.depth;e>0;e--)if(this.start(e)<=t&&this.end(e)>=t)return e;return 0}blockRange(t=this,e){if(t.pos<this.pos)return t.blockRange(this);for(let n=this.depth-(this.parent.inlineContent||this.pos==t.pos?1:0);n>=0;n--)if(t.pos<=this.end(n)&&(!e||e(this.node(n))))return new E(this,t,n);return null}sameParent(t){return this.pos-this.parentOffset==t.pos-t.parentOffset}max(t){return t.pos>this.pos?t:this}min(t){return t.pos<this.pos?t:this}toString(){let t="";for(let e=1;e<=this.depth;e++)t+=(t?"/":"")+this.node(e).type.name+"_"+this.index(e-1);return t+":"+this.parentOffset}static resolve(t,e){if(!(e>=0&&e<=t.content.size))throw new RangeError("Position "+e+" out of range");let n=[],r=0,o=e;for(let e=t;;){let{index:t,offset:i}=e.content.findIndex(o),s=o-i;if(n.push(e,t,r+i),!s)break;if(e=e.child(t),e.isText)break;o=s-1,r+=i+1}return new C(e,n,o)}static resolveCached(t,e){for(let n=0;n<A.length;n++){let r=A[n];if(r.pos==e&&r.doc==t)return r}let n=A[T]=C.resolve(t,e);return T=(T+1)%O,n}}let A=[],T=0,O=12;class E{constructor(t,e,n){this.$from=t,this.$to=e,this.depth=n}get start(){return this.$from.before(this.depth+1)}get end(){return this.$to.after(this.depth+1)}get parent(){return this.$from.node(this.depth)}get startIndex(){return this.$from.index(this.depth)}get endIndex(){return this.$to.indexAfter(this.depth)}}const _=Object.create(null);class N{constructor(t,e,n,r=u.none){this.type=t,this.attrs=e,this.marks=r,this.content=n||a.empty}get nodeSize(){return this.isLeaf?1:2+this.content.size}get childCount(){return this.content.childCount}child(t){return this.content.child(t)}maybeChild(t){return this.content.maybeChild(t)}forEach(t){this.content.forEach(t)}nodesBetween(t,e,n,r=0){this.content.nodesBetween(t,e,n,r,this)}descendants(t){this.nodesBetween(0,this.content.size,t)}get textContent(){return this.isLeaf&&this.type.spec.leafText?this.type.spec.leafText(this):this.textBetween(0,this.content.size,"")}textBetween(t,e,n,r){return this.content.textBetween(t,e,n,r)}get firstChild(){return this.content.firstChild}get lastChild(){return this.content.lastChild}eq(t){return this==t||this.sameMarkup(t)&&this.content.eq(t.content)}sameMarkup(t){return this.hasMarkup(t.type,t.attrs,t.marks)}hasMarkup(t,e,n){return this.type==t&&d(this.attrs,e||t.defaultAttrs||_)&&u.sameSet(this.marks,n||u.none)}copy(t=null){return t==this.content?this:new N(this.type,this.attrs,t,this.marks)}mark(t){return t==this.marks?this:new N(this.type,this.attrs,this.content,t)}cut(t,e=this.content.size){return 0==t&&e==this.content.size?this:this.copy(this.content.cut(t,e))}slice(t,e=this.content.size,n=!1){if(t==e)return p.empty;let r=this.resolve(t),o=this.resolve(e),i=n?0:r.sharedDepth(e),s=r.start(i),a=r.node(i).content.cut(r.pos-s,o.pos-s);return new p(a,r.depth-i,o.depth-i)}replace(t,e,n){return g(this.resolve(t),this.resolve(e),n)}nodeAt(t){for(let e=this;;){let{index:n,offset:r}=e.content.findIndex(t);if(e=e.maybeChild(n),!e)return null;if(r==t||e.isText)return e;t-=r+1}}childAfter(t){let{index:e,offset:n}=this.content.findIndex(t);return{node:this.content.maybeChild(e),index:e,offset:n}}childBefore(t){if(0==t)return{node:null,index:0,offset:0};let{index:e,offset:n}=this.content.findIndex(t);if(n<t)return{node:this.content.child(e),index:e,offset:n};let r=this.content.child(e-1);return{node:r,index:e-1,offset:n-r.nodeSize}}resolve(t){return C.resolveCached(this,t)}resolveNoCache(t){return C.resolve(this,t)}rangeHasMark(t,e,n){let r=!1;return e>t&&this.nodesBetween(t,e,(t=>(n.isInSet(t.marks)&&(r=!0),!r))),r}get isBlock(){return this.type.isBlock}get isTextblock(){return this.type.isTextblock}get inlineContent(){return this.type.inlineContent}get isInline(){return this.type.isInline}get isText(){return this.type.isText}get isLeaf(){return this.type.isLeaf}get isAtom(){return this.type.isAtom}toString(){if(this.type.spec.toDebugString)return this.type.spec.toDebugString(this);let t=this.type.name;return this.content.size&&(t+="("+this.content.toStringInner()+")"),R(this.marks,t)}contentMatchAt(t){let e=this.type.contentMatch.matchFragment(this.content,0,t);if(!e)throw new Error("Called contentMatchAt on a node with invalid content");return e}canReplace(t,e,n=a.empty,r=0,o=n.childCount){let i=this.contentMatchAt(t).matchFragment(n,r,o),s=i&&i.matchFragment(this.content,e);if(!s||!s.validEnd)return!1;for(let t=r;t<o;t++)if(!this.type.allowsMarks(n.child(t).marks))return!1;return!0}canReplaceWith(t,e,n,r){if(r&&!this.type.allowsMarks(r))return!1;let o=this.contentMatchAt(t).matchType(n),i=o&&o.matchFragment(this.content,e);return!!i&&i.validEnd}canAppend(t){return t.content.size?this.canReplace(this.childCount,this.childCount,t.content):this.type.compatibleContent(t.type)}check(){this.type.checkContent(this.content);let t=u.none;for(let e=0;e<this.marks.length;e++)t=this.marks[e].addToSet(t);if(!u.sameSet(t,this.marks))throw new RangeError(`Invalid collection of marks for node ${this.type.name}: ${this.marks.map((t=>t.type.name))}`);this.content.forEach((t=>t.check()))}toJSON(){let t={type:this.type.name};for(let e in this.attrs){t.attrs=this.attrs;break}return this.content.size&&(t.content=this.content.toJSON()),this.marks.length&&(t.marks=this.marks.map((t=>t.toJSON()))),t}static fromJSON(t,e){if(!e)throw new RangeError("Invalid input for Node.fromJSON");let n=null;if(e.marks){if(!Array.isArray(e.marks))throw new RangeError("Invalid mark data for Node.fromJSON");n=e.marks.map(t.markFromJSON)}if("text"==e.type){if("string"!=typeof e.text)throw new RangeError("Invalid text node in JSON");return t.text(e.text,n)}let r=a.fromJSON(t,e.content);return t.nodeType(e.type).create(e.attrs,r,n)}}N.prototype.text=void 0;class $ extends N{constructor(t,e,n,r){if(super(t,e,null,r),!n)throw new RangeError("Empty text nodes are not allowed");this.text=n}toString(){return this.type.spec.toDebugString?this.type.spec.toDebugString(this):R(this.marks,JSON.stringify(this.text))}get textContent(){return this.text}textBetween(t,e){return this.text.slice(t,e)}get nodeSize(){return this.text.length}mark(t){return t==this.marks?this:new $(this.type,this.attrs,this.text,t)}withText(t){return t==this.text?this:new $(this.type,this.attrs,t,this.marks)}cut(t=0,e=this.text.length){return 0==t&&e==this.text.length?this:this.withText(this.text.slice(t,e))}eq(t){return this.sameMarkup(t)&&this.text==t.text}toJSON(){let t=super.toJSON();return t.text=this.text,t}}function R(t,e){for(let n=t.length-1;n>=0;n--)e=t[n].type.name+"("+e+")";return e}class z{constructor(t){this.validEnd=t,this.next=[],this.wrapCache=[]}static parse(t,e){let n=new P(t,e);if(null==n.next)return z.empty;let r=D(n);n.next&&n.err("Unexpected trailing text");let o=function(t){let e=Object.create(null);return function n(r){let o=[];r.forEach((e=>{t[e].forEach((({term:e,to:n})=>{if(!e)return;let r;for(let t=0;t<o.length;t++)o[t][0]==e&&(r=o[t][1]);H(t,n).forEach((t=>{r||o.push([e,r=[]]),-1==r.indexOf(t)&&r.push(t)}))}))}));let i=e[r.join(",")]=new z(r.indexOf(t.length-1)>-1);for(let t=0;t<o.length;t++){let r=o[t][1].sort(B);i.next.push({type:o[t][0],next:e[r.join(",")]||n(r)})}return i}(H(t,0))}(function(t){let e=[[]];return o(function t(e,i){if("choice"==e.type)return e.exprs.reduce(((e,n)=>e.concat(t(n,i))),[]);if("seq"!=e.type){if("star"==e.type){let s=n();return r(i,s),o(t(e.expr,s),s),[r(s)]}if("plus"==e.type){let s=n();return o(t(e.expr,i),s),o(t(e.expr,s),s),[r(s)]}if("opt"==e.type)return[r(i)].concat(t(e.expr,i));if("range"==e.type){let s=i;for(let r=0;r<e.min;r++){let r=n();o(t(e.expr,s),r),s=r}if(-1==e.max)o(t(e.expr,s),s);else for(let i=e.min;i<e.max;i++){let i=n();r(s,i),o(t(e.expr,s),i),s=i}return[r(s)]}if("name"==e.type)return[r(i,void 0,e.value)];throw new Error("Unknown expr type")}for(let r=0;;r++){let s=t(e.exprs[r],i);if(r==e.exprs.length-1)return s;o(s,i=n())}}(t,0),n()),e;function n(){return e.push([])-1}function r(t,n,r){let o={term:r,to:n};return e[t].push(o),o}function o(t,e){t.forEach((t=>t.to=e))}}(r));return function(t,e){for(let n=0,r=[t];n<r.length;n++){let t=r[n],o=!t.validEnd,i=[];for(let e=0;e<t.next.length;e++){let{type:n,next:s}=t.next[e];i.push(n.name),!o||n.isText||n.hasRequiredAttrs()||(o=!1),-1==r.indexOf(s)&&r.push(s)}o&&e.err("Only non-generatable nodes ("+i.join(", ")+") in a required position (see https://prosemirror.net/docs/guide/#generatable)")}}(o,n),o}matchType(t){for(let e=0;e<this.next.length;e++)if(this.next[e].type==t)return this.next[e].next;return null}matchFragment(t,e=0,n=t.childCount){let r=this;for(let o=e;r&&o<n;o++)r=r.matchType(t.child(o).type);return r}get inlineContent(){return 0!=this.next.length&&this.next[0].type.isInline}get defaultType(){for(let t=0;t<this.next.length;t++){let{type:e}=this.next[t];if(!e.isText&&!e.hasRequiredAttrs())return e}return null}compatible(t){for(let e=0;e<this.next.length;e++)for(let n=0;n<t.next.length;n++)if(this.next[e].type==t.next[n].type)return!0;return!1}fillBefore(t,e=!1,n=0){let r=[this];return function o(i,s){let l=i.matchFragment(t,n);if(l&&(!e||l.validEnd))return a.from(s.map((t=>t.createAndFill())));for(let t=0;t<i.next.length;t++){let{type:e,next:n}=i.next[t];if(!e.isText&&!e.hasRequiredAttrs()&&-1==r.indexOf(n)){r.push(n);let t=o(n,s.concat(e));if(t)return t}}return null}(this,[])}findWrapping(t){for(let e=0;e<this.wrapCache.length;e+=2)if(this.wrapCache[e]==t)return this.wrapCache[e+1];let e=this.computeWrapping(t);return this.wrapCache.push(t,e),e}computeWrapping(t){let e=Object.create(null),n=[{match:this,type:null,via:null}];for(;n.length;){let r=n.shift(),o=r.match;if(o.matchType(t)){let t=[];for(let e=r;e.type;e=e.via)t.push(e.type);return t.reverse()}for(let t=0;t<o.next.length;t++){let{type:i,next:s}=o.next[t];i.isLeaf||i.hasRequiredAttrs()||i.name in e||r.type&&!s.validEnd||(n.push({match:i.contentMatch,type:i,via:r}),e[i.name]=!0)}}return null}get edgeCount(){return this.next.length}edge(t){if(t>=this.next.length)throw new RangeError(`There's no ${t}th edge in this content match`);return this.next[t]}toString(){let t=[];return function e(n){t.push(n);for(let r=0;r<n.next.length;r++)-1==t.indexOf(n.next[r].next)&&e(n.next[r].next)}(this),t.map(((e,n)=>{let r=n+(e.validEnd?"*":" ")+" ";for(let n=0;n<e.next.length;n++)r+=(n?", ":"")+e.next[n].type.name+"->"+t.indexOf(e.next[n].next);return r})).join("\n")}}z.empty=new z(!0);class P{constructor(t,e){this.string=t,this.nodeTypes=e,this.inline=null,this.pos=0,this.tokens=t.split(/\s*(?=\b|\W|$)/),""==this.tokens[this.tokens.length-1]&&this.tokens.pop(),""==this.tokens[0]&&this.tokens.shift()}get next(){return this.tokens[this.pos]}eat(t){return this.next==t&&(this.pos++||!0)}err(t){throw new SyntaxError(t+" (in content expression '"+this.string+"')")}}function D(t){let e=[];do{e.push(L(t))}while(t.eat("|"));return 1==e.length?e[0]:{type:"choice",exprs:e}}function L(t){let e=[];do{e.push(I(t))}while(t.next&&")"!=t.next&&"|"!=t.next);return 1==e.length?e[0]:{type:"seq",exprs:e}}function I(t){let e=function(t){if(t.eat("(")){let e=D(t);return t.eat(")")||t.err("Missing closing paren"),e}if(!/\W/.test(t.next)){let e=function(t,e){let n=t.nodeTypes,r=n[e];if(r)return[r];let o=[];for(let t in n){let r=n[t];r.groups.indexOf(e)>-1&&o.push(r)}return 0==o.length&&t.err("No node type or group '"+e+"' found"),o}(t,t.next).map((e=>(null==t.inline?t.inline=e.isInline:t.inline!=e.isInline&&t.err("Mixing inline and block content"),{type:"name",value:e})));return t.pos++,1==e.length?e[0]:{type:"choice",exprs:e}}t.err("Unexpected token '"+t.next+"'")}(t);for(;;)if(t.eat("+"))e={type:"plus",expr:e};else if(t.eat("*"))e={type:"star",expr:e};else if(t.eat("?"))e={type:"opt",expr:e};else{if(!t.eat("{"))break;e=j(t,e)}return e}function F(t){/\D/.test(t.next)&&t.err("Expected number, got '"+t.next+"'");let e=Number(t.next);return t.pos++,e}function j(t,e){let n=F(t),r=n;return t.eat(",")&&(r="}"!=t.next?F(t):-1),t.eat("}")||t.err("Unclosed braced range"),{type:"range",min:n,max:r,expr:e}}function B(t,e){return e-t}function H(t,e){let n=[];return function e(r){let o=t[r];if(1==o.length&&!o[0].term)return e(o[0].to);n.push(r);for(let t=0;t<o.length;t++){let{term:r,to:i}=o[t];r||-1!=n.indexOf(i)||e(i)}}(e),n.sort(B)}function V(t){let e=Object.create(null);for(let n in t){let r=t[n];if(!r.hasDefault)return null;e[n]=r.default}return e}function K(t,e){let n=Object.create(null);for(let r in t){let o=e&&e[r];if(void 0===o){let e=t[r];if(!e.hasDefault)throw new RangeError("No value supplied for attribute "+r);o=e.default}n[r]=o}return n}function U(t){let e=Object.create(null);if(t)for(let n in t)e[n]=new W(t[n]);return e}class J{constructor(t,e,n){this.name=t,this.schema=e,this.spec=n,this.markSet=null,this.groups=n.group?n.group.split(" "):[],this.attrs=U(n.attrs),this.defaultAttrs=V(this.attrs),this.contentMatch=null,this.inlineContent=null,this.isBlock=!(n.inline||"text"==t),this.isText="text"==t}get isInline(){return!this.isBlock}get isTextblock(){return this.isBlock&&this.inlineContent}get isLeaf(){return this.contentMatch==z.empty}get isAtom(){return this.isLeaf||!!this.spec.atom}get whitespace(){return this.spec.whitespace||(this.spec.code?"pre":"normal")}hasRequiredAttrs(){for(let t in this.attrs)if(this.attrs[t].isRequired)return!0;return!1}compatibleContent(t){return this==t||this.contentMatch.compatible(t.contentMatch)}computeAttrs(t){return!t&&this.defaultAttrs?this.defaultAttrs:K(this.attrs,t)}create(t=null,e,n){if(this.isText)throw new Error("NodeType.create can't construct text nodes");return new N(this,this.computeAttrs(t),a.from(e),u.setFrom(n))}createChecked(t=null,e,n){return e=a.from(e),this.checkContent(e),new N(this,this.computeAttrs(t),e,u.setFrom(n))}createAndFill(t=null,e,n){if(t=this.computeAttrs(t),(e=a.from(e)).size){let t=this.contentMatch.fillBefore(e);if(!t)return null;e=t.append(e)}let r=this.contentMatch.matchFragment(e),o=r&&r.fillBefore(a.empty,!0);return o?new N(this,t,e.append(o),u.setFrom(n)):null}validContent(t){let e=this.contentMatch.matchFragment(t);if(!e||!e.validEnd)return!1;for(let e=0;e<t.childCount;e++)if(!this.allowsMarks(t.child(e).marks))return!1;return!0}checkContent(t){if(!this.validContent(t))throw new RangeError(`Invalid content for node ${this.name}: ${t.toString().slice(0,50)}`)}allowsMarkType(t){return null==this.markSet||this.markSet.indexOf(t)>-1}allowsMarks(t){if(null==this.markSet)return!0;for(let e=0;e<t.length;e++)if(!this.allowsMarkType(t[e].type))return!1;return!0}allowedMarks(t){if(null==this.markSet)return t;let e;for(let n=0;n<t.length;n++)this.allowsMarkType(t[n].type)?e&&e.push(t[n]):e||(e=t.slice(0,n));return e?e.length?e:u.none:t}static compile(t,e){let n=Object.create(null);t.forEach(((t,r)=>n[t]=new J(t,e,r)));let r=e.spec.topNode||"doc";if(!n[r])throw new RangeError("Schema is missing its top node type ('"+r+"')");if(!n.text)throw new RangeError("Every schema needs a 'text' type");for(let t in n.text.attrs)throw new RangeError("The text node type should not have attributes");return n}}class W{constructor(t){this.hasDefault=Object.prototype.hasOwnProperty.call(t,"default"),this.default=t.default}get isRequired(){return!this.hasDefault}}class q{constructor(t,e,n,r){this.name=t,this.rank=e,this.schema=n,this.spec=r,this.attrs=U(r.attrs),this.excluded=null;let o=V(this.attrs);this.instance=o?new u(this,o):null}create(t=null){return!t&&this.instance?this.instance:new u(this,K(this.attrs,t))}static compile(t,e){let n=Object.create(null),r=0;return t.forEach(((t,o)=>n[t]=new q(t,r++,e,o))),n}removeFromSet(t){for(var e=0;e<t.length;e++)t[e].type==this&&(t=t.slice(0,e).concat(t.slice(e+1)),e--);return t}isInSet(t){for(let e=0;e<t.length;e++)if(t[e].type==this)return t[e]}excludes(t){return this.excluded.indexOf(t)>-1}}class Z{constructor(t){this.linebreakReplacement=null,this.cached=Object.create(null);let e=this.spec={};for(let n in t)e[n]=t[n];e.nodes=o.from(t.nodes),e.marks=o.from(t.marks||{}),this.nodes=J.compile(this.spec.nodes,this),this.marks=q.compile(this.spec.marks,this);let n=Object.create(null);for(let t in this.nodes){if(t in this.marks)throw new RangeError(t+" can not be both a node and a mark");let e=this.nodes[t],r=e.spec.content||"",o=e.spec.marks;if(e.contentMatch=n[r]||(n[r]=z.parse(r,this.nodes)),e.inlineContent=e.contentMatch.inlineContent,e.spec.linebreakReplacement){if(this.linebreakReplacement)throw new RangeError("Multiple linebreak nodes defined");if(!e.isInline||!e.isLeaf)throw new RangeError("Linebreak replacement nodes must be inline leaf nodes");this.linebreakReplacement=e}e.markSet="_"==o?null:o?G(this,o.split(" ")):""!=o&&e.inlineContent?null:[]}for(let t in this.marks){let e=this.marks[t],n=e.spec.excludes;e.excluded=null==n?[e]:""==n?[]:G(this,n.split(" "))}this.nodeFromJSON=this.nodeFromJSON.bind(this),this.markFromJSON=this.markFromJSON.bind(this),this.topNodeType=this.nodes[this.spec.topNode||"doc"],this.cached.wrappings=Object.create(null)}node(t,e=null,n,r){if("string"==typeof t)t=this.nodeType(t);else{if(!(t instanceof J))throw new RangeError("Invalid node type: "+t);if(t.schema!=this)throw new RangeError("Node type from different schema used ("+t.name+")")}return t.createChecked(e,n,r)}text(t,e){let n=this.nodes.text;return new $(n,n.defaultAttrs,t,u.setFrom(e))}mark(t,e){return"string"==typeof t&&(t=this.marks[t]),t.create(e)}nodeFromJSON(t){return N.fromJSON(this,t)}markFromJSON(t){return u.fromJSON(this,t)}nodeType(t){let e=this.nodes[t];if(!e)throw new RangeError("Unknown node type: "+t);return e}}function G(t,e){let n=[];for(let r=0;r<e.length;r++){let o=e[r],i=t.marks[o],s=i;if(i)n.push(i);else for(let e in t.marks){let r=t.marks[e];("_"==o||r.spec.group&&r.spec.group.split(" ").indexOf(o)>-1)&&n.push(s=r)}if(!s)throw new SyntaxError("Unknown mark type: '"+e[r]+"'")}return n}class Y{constructor(t,e){this.schema=t,this.rules=e,this.tags=[],this.styles=[],e.forEach((t=>{!function(t){return null!=t.tag}(t)?function(t){return null!=t.style}(t)&&this.styles.push(t):this.tags.push(t)})),this.normalizeLists=!this.tags.some((e=>{if(!/^(ul|ol)\b/.test(e.tag)||!e.node)return!1;let n=t.nodes[e.node];return n.contentMatch.matchType(n)}))}parse(t,e={}){let n=new rt(this,e,!1);return n.addAll(t,e.from,e.to),n.finish()}parseSlice(t,e={}){let n=new rt(this,e,!0);return n.addAll(t,e.from,e.to),p.maxOpen(n.finish())}matchTag(t,e,n){for(let r=n?this.tags.indexOf(n)+1:0;r<this.tags.length;r++){let n=this.tags[r];if(ot(t,n.tag)&&(void 0===n.namespace||t.namespaceURI==n.namespace)&&(!n.context||e.matchesContext(n.context))){if(n.getAttrs){let e=n.getAttrs(t);if(!1===e)continue;n.attrs=e||void 0}return n}}}matchStyle(t,e,n,r){for(let o=r?this.styles.indexOf(r)+1:0;o<this.styles.length;o++){let r=this.styles[o],i=r.style;if(!(0!=i.indexOf(t)||r.context&&!n.matchesContext(r.context)||i.length>t.length&&(61!=i.charCodeAt(t.length)||i.slice(t.length+1)!=e))){if(r.getAttrs){let t=r.getAttrs(e);if(!1===t)continue;r.attrs=t||void 0}return r}}}static schemaRules(t){let e=[];function n(t){let n=null==t.priority?50:t.priority,r=0;for(;r<e.length;r++){let t=e[r];if((null==t.priority?50:t.priority)<n)break}e.splice(r,0,t)}for(let e in t.marks){let r=t.marks[e].spec.parseDOM;r&&r.forEach((t=>{n(t=it(t)),t.mark||t.ignore||t.clearMark||(t.mark=e)}))}for(let e in t.nodes){let r=t.nodes[e].spec.parseDOM;r&&r.forEach((t=>{n(t=it(t)),t.node||t.ignore||t.mark||(t.node=e)}))}return e}static fromSchema(t){return t.cached.domParser||(t.cached.domParser=new Y(t,Y.schemaRules(t)))}}const X={address:!0,article:!0,aside:!0,blockquote:!0,canvas:!0,dd:!0,div:!0,dl:!0,fieldset:!0,figcaption:!0,figure:!0,footer:!0,form:!0,h1:!0,h2:!0,h3:!0,h4:!0,h5:!0,h6:!0,header:!0,hgroup:!0,hr:!0,li:!0,noscript:!0,ol:!0,output:!0,p:!0,pre:!0,section:!0,table:!0,tfoot:!0,ul:!0},Q={head:!0,noscript:!0,object:!0,script:!0,style:!0,title:!0},tt={ol:!0,ul:!0};function et(t,e,n){return null!=e?(e?1:0)|("full"===e?2:0):t&&"pre"==t.whitespace?3:-5&n}class nt{constructor(t,e,n,r,o,i,s){this.type=t,this.attrs=e,this.marks=n,this.pendingMarks=r,this.solid=o,this.options=s,this.content=[],this.activeMarks=u.none,this.stashMarks=[],this.match=i||(4&s?null:t.contentMatch)}findWrapping(t){if(!this.match){if(!this.type)return[];let e=this.type.contentMatch.fillBefore(a.from(t));if(!e){let e,n=this.type.contentMatch;return(e=n.findWrapping(t.type))?(this.match=n,e):null}this.match=this.type.contentMatch.matchFragment(e)}return this.match.findWrapping(t.type)}finish(t){if(!(1&this.options)){let t,e=this.content[this.content.length-1];if(e&&e.isText&&(t=/[ \t\r\n\u000c]+$/.exec(e.text))){let n=e;e.text.length==t[0].length?this.content.pop():this.content[this.content.length-1]=n.withText(n.text.slice(0,n.text.length-t[0].length))}}let e=a.from(this.content);return!t&&this.match&&(e=e.append(this.match.fillBefore(a.empty,!0))),this.type?this.type.create(this.attrs,e,this.marks):e}popFromStashMark(t){for(let e=this.stashMarks.length-1;e>=0;e--)if(t.eq(this.stashMarks[e]))return this.stashMarks.splice(e,1)[0]}applyPending(t){for(let e=0,n=this.pendingMarks;e<n.length;e++){let r=n[e];(this.type?this.type.allowsMarkType(r.type):st(r.type,t))&&!r.isInSet(this.activeMarks)&&(this.activeMarks=r.addToSet(this.activeMarks),this.pendingMarks=r.removeFromSet(this.pendingMarks))}}inlineContext(t){return this.type?this.type.inlineContent:this.content.length?this.content[0].isInline:t.parentNode&&!X.hasOwnProperty(t.parentNode.nodeName.toLowerCase())}}class rt{constructor(t,e,n){this.parser=t,this.options=e,this.isOpen=n,this.open=0;let r,o=e.topNode,i=et(null,e.preserveWhitespace,0)|(n?4:0);r=o?new nt(o.type,o.attrs,u.none,u.none,!0,e.topMatch||o.type.contentMatch,i):new nt(n?null:t.schema.topNodeType,null,u.none,u.none,!0,null,i),this.nodes=[r],this.find=e.findPositions,this.needsBlock=!1}get top(){return this.nodes[this.open]}addDOM(t){3==t.nodeType?this.addTextNode(t):1==t.nodeType&&this.addElement(t)}withStyleRules(t,e){let n=t.style;if(!n||!n.length)return e();let r=this.readStyles(t.style);if(!r)return;let[o,i]=r,s=this.top;for(let t=0;t<i.length;t++)this.removePendingMark(i[t],s);for(let t=0;t<o.length;t++)this.addPendingMark(o[t]);e();for(let t=0;t<o.length;t++)this.removePendingMark(o[t],s);for(let t=0;t<i.length;t++)this.addPendingMark(i[t])}addTextNode(t){let e=t.nodeValue,n=this.top;if(2&n.options||n.inlineContext(t)||/[^ \t\r\n\u000c]/.test(e)){if(1&n.options)e=2&n.options?e.replace(/\r\n?/g,"\n"):e.replace(/\r?\n|\r/g," ");else if(e=e.replace(/[ \t\r\n\u000c]+/g," "),/^[ \t\r\n\u000c]/.test(e)&&this.open==this.nodes.length-1){let r=n.content[n.content.length-1],o=t.previousSibling;(!r||o&&"BR"==o.nodeName||r.isText&&/[ \t\r\n\u000c]$/.test(r.text))&&(e=e.slice(1))}e&&this.insertNode(this.parser.schema.text(e)),this.findInText(t)}else this.findInside(t)}addElement(t,e){let n,r=t.nodeName.toLowerCase();tt.hasOwnProperty(r)&&this.parser.normalizeLists&&function(t){for(let e=t.firstChild,n=null;e;e=e.nextSibling){let t=1==e.nodeType?e.nodeName.toLowerCase():null;t&&tt.hasOwnProperty(t)&&n?(n.appendChild(e),e=n):"li"==t?n=e:t&&(n=null)}}(t);let o=this.options.ruleFromNode&&this.options.ruleFromNode(t)||(n=this.parser.matchTag(t,this,e));if(o?o.ignore:Q.hasOwnProperty(r))this.findInside(t),this.ignoreFallback(t);else if(!o||o.skip||o.closeParent){o&&o.closeParent?this.open=Math.max(0,this.open-1):o&&o.skip.nodeType&&(t=o.skip);let e,n=this.top,i=this.needsBlock;if(X.hasOwnProperty(r))n.content.length&&n.content[0].isInline&&this.open&&(this.open--,n=this.top),e=!0,n.type||(this.needsBlock=!0);else if(!t.firstChild)return void this.leafFallback(t);o&&o.skip?this.addAll(t):this.withStyleRules(t,(()=>this.addAll(t))),e&&this.sync(n),this.needsBlock=i}else this.withStyleRules(t,(()=>{this.addElementByRule(t,o,!1===o.consuming?n:void 0)}))}leafFallback(t){"BR"==t.nodeName&&this.top.type&&this.top.type.inlineContent&&this.addTextNode(t.ownerDocument.createTextNode("\n"))}ignoreFallback(t){"BR"!=t.nodeName||this.top.type&&this.top.type.inlineContent||this.findPlace(this.parser.schema.text("-"))}readStyles(t){let e=u.none,n=u.none;for(let r=0,o=t.length;r<o;r++){let o=t.item(r);for(let r;;){let i=this.parser.matchStyle(o,t.getPropertyValue(o),this,r);if(!i)break;if(i.ignore)return null;if(i.clearMark?this.top.pendingMarks.concat(this.top.activeMarks).forEach((t=>{i.clearMark(t)&&(n=t.addToSet(n))})):e=this.parser.schema.marks[i.mark].create(i.attrs).addToSet(e),!1!==i.consuming)break;r=i}}return[e,n]}addElementByRule(t,e,n){let r,o,i;e.node?(o=this.parser.schema.nodes[e.node],o.isLeaf?this.insertNode(o.create(e.attrs))||this.leafFallback(t):r=this.enter(o,e.attrs||null,e.preserveWhitespace)):(i=this.parser.schema.marks[e.mark].create(e.attrs),this.addPendingMark(i));let s=this.top;if(o&&o.isLeaf)this.findInside(t);else if(n)this.addElement(t,n);else if(e.getContent)this.findInside(t),e.getContent(t,this.parser.schema).forEach((t=>this.insertNode(t)));else{let n=t;"string"==typeof e.contentElement?n=t.querySelector(e.contentElement):"function"==typeof e.contentElement?n=e.contentElement(t):e.contentElement&&(n=e.contentElement),this.findAround(t,n,!0),this.addAll(n)}r&&this.sync(s)&&this.open--,i&&this.removePendingMark(i,s)}addAll(t,e,n){let r=e||0;for(let o=e?t.childNodes[e]:t.firstChild,i=null==n?null:t.childNodes[n];o!=i;o=o.nextSibling,++r)this.findAtPoint(t,r),this.addDOM(o);this.findAtPoint(t,r)}findPlace(t){let e,n;for(let r=this.open;r>=0;r--){let o=this.nodes[r],i=o.findWrapping(t);if(i&&(!e||e.length>i.length)&&(e=i,n=o,!i.length))break;if(o.solid)break}if(!e)return!1;this.sync(n);for(let t=0;t<e.length;t++)this.enterInner(e[t],null,!1);return!0}insertNode(t){if(t.isInline&&this.needsBlock&&!this.top.type){let t=this.textblockFromContext();t&&this.enterInner(t)}if(this.findPlace(t)){this.closeExtra();let e=this.top;e.applyPending(t.type),e.match&&(e.match=e.match.matchType(t.type));let n=e.activeMarks;for(let r=0;r<t.marks.length;r++)e.type&&!e.type.allowsMarkType(t.marks[r].type)||(n=t.marks[r].addToSet(n));return e.content.push(t.mark(n)),!0}return!1}enter(t,e,n){let r=this.findPlace(t.create(e));return r&&this.enterInner(t,e,!0,n),r}enterInner(t,e=null,n=!1,r){this.closeExtra();let o=this.top;o.applyPending(t),o.match=o.match&&o.match.matchType(t);let i=et(t,r,o.options);4&o.options&&0==o.content.length&&(i|=4),this.nodes.push(new nt(t,e,o.activeMarks,o.pendingMarks,n,null,i)),this.open++}closeExtra(t=!1){let e=this.nodes.length-1;if(e>this.open){for(;e>this.open;e--)this.nodes[e-1].content.push(this.nodes[e].finish(t));this.nodes.length=this.open+1}}finish(){return this.open=0,this.closeExtra(this.isOpen),this.nodes[0].finish(this.isOpen||this.options.topOpen)}sync(t){for(let e=this.open;e>=0;e--)if(this.nodes[e]==t)return this.open=e,!0;return!1}get currentPos(){this.closeExtra();let t=0;for(let e=this.open;e>=0;e--){let n=this.nodes[e].content;for(let e=n.length-1;e>=0;e--)t+=n[e].nodeSize;e&&t++}return t}findAtPoint(t,e){if(this.find)for(let n=0;n<this.find.length;n++)this.find[n].node==t&&this.find[n].offset==e&&(this.find[n].pos=this.currentPos)}findInside(t){if(this.find)for(let e=0;e<this.find.length;e++)null==this.find[e].pos&&1==t.nodeType&&t.contains(this.find[e].node)&&(this.find[e].pos=this.currentPos)}findAround(t,e,n){if(t!=e&&this.find)for(let r=0;r<this.find.length;r++)null==this.find[r].pos&&1==t.nodeType&&t.contains(this.find[r].node)&&e.compareDocumentPosition(this.find[r].node)&(n?2:4)&&(this.find[r].pos=this.currentPos)}findInText(t){if(this.find)for(let e=0;e<this.find.length;e++)this.find[e].node==t&&(this.find[e].pos=this.currentPos-(t.nodeValue.length-this.find[e].offset))}matchesContext(t){if(t.indexOf("|")>-1)return t.split(/\s*\|\s*/).some(this.matchesContext,this);let e=t.split("/"),n=this.options.context,r=!(this.isOpen||n&&n.parent.type!=this.nodes[0].type),o=-(n?n.depth+1:0)+(r?0:1),i=(t,s)=>{for(;t>=0;t--){let a=e[t];if(""==a){if(t==e.length-1||0==t)continue;for(;s>=o;s--)if(i(t-1,s))return!0;return!1}{let t=s>0||0==s&&r?this.nodes[s].type:n&&s>=o?n.node(s-o).type:null;if(!t||t.name!=a&&-1==t.groups.indexOf(a))return!1;s--}}return!0};return i(e.length-1,this.open)}textblockFromContext(){let t=this.options.context;if(t)for(let e=t.depth;e>=0;e--){let n=t.node(e).contentMatchAt(t.indexAfter(e)).defaultType;if(n&&n.isTextblock&&n.defaultAttrs)return n}for(let t in this.parser.schema.nodes){let e=this.parser.schema.nodes[t];if(e.isTextblock&&e.defaultAttrs)return e}}addPendingMark(t){let e=function(t,e){for(let n=0;n<e.length;n++)if(t.eq(e[n]))return e[n]}(t,this.top.pendingMarks);e&&this.top.stashMarks.push(e),this.top.pendingMarks=t.addToSet(this.top.pendingMarks)}removePendingMark(t,e){for(let n=this.open;n>=0;n--){let r=this.nodes[n];if(r.pendingMarks.lastIndexOf(t)>-1)r.pendingMarks=t.removeFromSet(r.pendingMarks);else{r.activeMarks=t.removeFromSet(r.activeMarks);let e=r.popFromStashMark(t);e&&r.type&&r.type.allowsMarkType(e.type)&&(r.activeMarks=e.addToSet(r.activeMarks))}if(r==e)break}}}function ot(t,e){return(t.matches||t.msMatchesSelector||t.webkitMatchesSelector||t.mozMatchesSelector).call(t,e)}function it(t){let e={};for(let n in t)e[n]=t[n];return e}function st(t,e){let n=e.schema.nodes;for(let r in n){let o=n[r];if(!o.allowsMarkType(t))continue;let i=[],s=t=>{i.push(t);for(let n=0;n<t.edgeCount;n++){let{type:r,next:o}=t.edge(n);if(r==e)return!0;if(i.indexOf(o)<0&&s(o))return!0}};if(s(o.contentMatch))return!0}}class at{constructor(t,e){this.nodes=t,this.marks=e}serializeFragment(t,e={},n){n||(n=ct(e).createDocumentFragment());let r=n,o=[];return t.forEach((t=>{if(o.length||t.marks.length){let n=0,i=0;for(;n<o.length&&i<t.marks.length;){let e=t.marks[i];if(this.marks[e.type.name]){if(!e.eq(o[n][0])||!1===e.type.spec.spanning)break;n++,i++}else i++}for(;n<o.length;)r=o.pop()[1];for(;i<t.marks.length;){let n=t.marks[i++],s=this.serializeMark(n,t.isInline,e);s&&(o.push([n,r]),r.appendChild(s.dom),r=s.contentDOM||s.dom)}}r.appendChild(this.serializeNodeInner(t,e))})),n}serializeNodeInner(t,e){let{dom:n,contentDOM:r}=at.renderSpec(ct(e),this.nodes[t.type.name](t));if(r){if(t.isLeaf)throw new RangeError("Content hole not allowed in a leaf node spec");this.serializeFragment(t.content,e,r)}return n}serializeNode(t,e={}){let n=this.serializeNodeInner(t,e);for(let r=t.marks.length-1;r>=0;r--){let o=this.serializeMark(t.marks[r],t.isInline,e);o&&((o.contentDOM||o.dom).appendChild(n),n=o.dom)}return n}serializeMark(t,e,n={}){let r=this.marks[t.type.name];return r&&at.renderSpec(ct(n),r(t,e))}static renderSpec(t,e,n=null){if("string"==typeof e)return{dom:t.createTextNode(e)};if(null!=e.nodeType)return{dom:e};if(e.dom&&null!=e.dom.nodeType)return e;let r,o=e[0],i=o.indexOf(" ");i>0&&(n=o.slice(0,i),o=o.slice(i+1));let s=n?t.createElementNS(n,o):t.createElement(o),a=e[1],l=1;if(a&&"object"==typeof a&&null==a.nodeType&&!Array.isArray(a)){l=2;for(let t in a)if(null!=a[t]){let e=t.indexOf(" ");e>0?s.setAttributeNS(t.slice(0,e),t.slice(e+1),a[t]):s.setAttribute(t,a[t])}}for(let o=l;o<e.length;o++){let i=e[o];if(0===i){if(o<e.length-1||o>l)throw new RangeError("Content hole must be the only child of its parent node");return{dom:s,contentDOM:s}}{let{dom:e,contentDOM:o}=at.renderSpec(t,i,n);if(s.appendChild(e),o){if(r)throw new RangeError("Multiple content holes");r=o}}}return{dom:s,contentDOM:r}}static fromSchema(t){return t.cached.domSerializer||(t.cached.domSerializer=new at(this.nodesFromSchema(t),this.marksFromSchema(t)))}static nodesFromSchema(t){let e=lt(t.nodes);return e.text||(e.text=t=>t.text),e}static marksFromSchema(t){return lt(t.marks)}}function lt(t){let e={};for(let n in t){let r=t[n].spec.toDOM;r&&(e[n]=r)}return e}function ct(t){return t.document||window.document}},2559:(t,e,n)=>{"use strict";n.d(e,{$t:()=>S,LN:()=>s,U3:()=>d,hs:()=>O,i5:()=>f,k_:()=>C,nh:()=>h});var r=n(9679),o=n(196);const i=Object.create(null);class s{constructor(t,e,n){this.$anchor=t,this.$head=e,this.ranges=n||[new a(t.min(e),t.max(e))]}get anchor(){return this.$anchor.pos}get head(){return this.$head.pos}get from(){return this.$from.pos}get to(){return this.$to.pos}get $from(){return this.ranges[0].$from}get $to(){return this.ranges[0].$to}get empty(){let t=this.ranges;for(let e=0;e<t.length;e++)if(t[e].$from.pos!=t[e].$to.pos)return!1;return!0}content(){return this.$from.doc.slice(this.from,this.to,!0)}replace(t,e=r.Ji.empty){let n=e.content.lastChild,o=null;for(let t=0;t<e.openEnd;t++)o=n,n=n.lastChild;let i=t.steps.length,s=this.ranges;for(let a=0;a<s.length;a++){let{$from:l,$to:c}=s[a],d=t.mapping.slice(i);t.replaceRange(d.map(l.pos),d.map(c.pos),a?r.Ji.empty:e),0==a&&y(t,i,(n?n.isInline:o&&o.isTextblock)?-1:1)}}replaceWith(t,e){let n=t.steps.length,r=this.ranges;for(let o=0;o<r.length;o++){let{$from:i,$to:s}=r[o],a=t.mapping.slice(n),l=a.map(i.pos),c=a.map(s.pos);o?t.deleteRange(l,c):(t.replaceRangeWith(l,c,e),y(t,n,e.isInline?-1:1))}}static findFrom(t,e,n=!1){let r=t.parent.inlineContent?new d(t):g(t.node(0),t.parent,t.pos,t.index(),e,n);if(r)return r;for(let r=t.depth-1;r>=0;r--){let o=e<0?g(t.node(0),t.node(r),t.before(r+1),t.index(r),e,n):g(t.node(0),t.node(r),t.after(r+1),t.index(r)+1,e,n);if(o)return o}return null}static near(t,e=1){return this.findFrom(t,e)||this.findFrom(t,-e)||new f(t.node(0))}static atStart(t){return g(t,t,0,0,1)||new f(t)}static atEnd(t){return g(t,t,t.content.size,t.childCount,-1)||new f(t)}static fromJSON(t,e){if(!e||!e.type)throw new RangeError("Invalid input for Selection.fromJSON");let n=i[e.type];if(!n)throw new RangeError(`No selection type ${e.type} defined`);return n.fromJSON(t,e)}static jsonID(t,e){if(t in i)throw new RangeError("Duplicate use of selection JSON ID "+t);return i[t]=e,e.prototype.jsonID=t,e}getBookmark(){return d.between(this.$anchor,this.$head).getBookmark()}}s.prototype.visible=!0;class a{constructor(t,e){this.$from=t,this.$to=e}}let l=!1;function c(t){l||t.parent.inlineContent||(l=!0,console.warn("TextSelection endpoint not pointing into a node with inline content ("+t.parent.type.name+")"))}class d extends s{constructor(t,e=t){c(t),c(e),super(t,e)}get $cursor(){return this.$anchor.pos==this.$head.pos?this.$head:null}map(t,e){let n=t.resolve(e.map(this.head));if(!n.parent.inlineContent)return s.near(n);let r=t.resolve(e.map(this.anchor));return new d(r.parent.inlineContent?r:n,n)}replace(t,e=r.Ji.empty){if(super.replace(t,e),e==r.Ji.empty){let e=this.$from.marksAcross(this.$to);e&&t.ensureMarks(e)}}eq(t){return t instanceof d&&t.anchor==this.anchor&&t.head==this.head}getBookmark(){return new u(this.anchor,this.head)}toJSON(){return{type:"text",anchor:this.anchor,head:this.head}}static fromJSON(t,e){if("number"!=typeof e.anchor||"number"!=typeof e.head)throw new RangeError("Invalid input for TextSelection.fromJSON");return new d(t.resolve(e.anchor),t.resolve(e.head))}static create(t,e,n=e){let r=t.resolve(e);return new this(r,n==e?r:t.resolve(n))}static between(t,e,n){let r=t.pos-e.pos;if(n&&!r||(n=r>=0?1:-1),!e.parent.inlineContent){let t=s.findFrom(e,n,!0)||s.findFrom(e,-n,!0);if(!t)return s.near(e,n);e=t.$head}return t.parent.inlineContent||(0==r||(t=(s.findFrom(t,-n,!0)||s.findFrom(t,n,!0)).$anchor).pos<e.pos!=r<0)&&(t=e),new d(t,e)}}s.jsonID("text",d);class u{constructor(t,e){this.anchor=t,this.head=e}map(t){return new u(t.map(this.anchor),t.map(this.head))}resolve(t){return d.between(t.resolve(this.anchor),t.resolve(this.head))}}class h extends s{constructor(t){let e=t.nodeAfter,n=t.node(0).resolve(t.pos+e.nodeSize);super(t,n),this.node=e}map(t,e){let{deleted:n,pos:r}=e.mapResult(this.anchor),o=t.resolve(r);return n?s.near(o):new h(o)}content(){return new r.Ji(r.FK.from(this.node),0,0)}eq(t){return t instanceof h&&t.anchor==this.anchor}toJSON(){return{type:"node",anchor:this.anchor}}getBookmark(){return new p(this.anchor)}static fromJSON(t,e){if("number"!=typeof e.anchor)throw new RangeError("Invalid input for NodeSelection.fromJSON");return new h(t.resolve(e.anchor))}static create(t,e){return new h(t.resolve(e))}static isSelectable(t){return!t.isText&&!1!==t.type.spec.selectable}}h.prototype.visible=!1,s.jsonID("node",h);class p{constructor(t){this.anchor=t}map(t){let{deleted:e,pos:n}=t.mapResult(this.anchor);return e?new u(n,n):new p(n)}resolve(t){let e=t.resolve(this.anchor),n=e.nodeAfter;return n&&h.isSelectable(n)?new h(e):s.near(e)}}class f extends s{constructor(t){super(t.resolve(0),t.resolve(t.content.size))}replace(t,e=r.Ji.empty){if(e==r.Ji.empty){t.delete(0,t.doc.content.size);let e=s.atStart(t.doc);e.eq(t.selection)||t.setSelection(e)}else super.replace(t,e)}toJSON(){return{type:"all"}}static fromJSON(t){return new f(t)}map(t){return new f(t)}eq(t){return t instanceof f}getBookmark(){return m}}s.jsonID("all",f);const m={map(){return this},resolve:t=>new f(t)};function g(t,e,n,r,o,i=!1){if(e.inlineContent)return d.create(t,n);for(let s=r-(o>0?0:1);o>0?s<e.childCount:s>=0;s+=o){let r=e.child(s);if(r.isAtom){if(!i&&h.isSelectable(r))return h.create(t,n-(o<0?r.nodeSize:0))}else{let e=g(t,r,n+o,o<0?r.childCount:0,o,i);if(e)return e}n+=r.nodeSize*o}return null}function y(t,e,n){let r=t.steps.length-1;if(r<e)return;let i,a=t.steps[r];(a instanceof o.Ln||a instanceof o.Wg)&&(t.mapping.maps[r].forEach(((t,e,n,r)=>{null==i&&(i=r)})),t.setSelection(s.near(t.doc.resolve(i),n)))}class v extends o.dL{constructor(t){super(t.doc),this.curSelectionFor=0,this.updated=0,this.meta=Object.create(null),this.time=Date.now(),this.curSelection=t.selection,this.storedMarks=t.storedMarks}get selection(){return this.curSelectionFor<this.steps.length&&(this.curSelection=this.curSelection.map(this.doc,this.mapping.slice(this.curSelectionFor)),this.curSelectionFor=this.steps.length),this.curSelection}setSelection(t){if(t.$from.doc!=this.doc)throw new RangeError("Selection passed to setSelection must point at the current document");return this.curSelection=t,this.curSelectionFor=this.steps.length,this.updated=-3&this.updated|1,this.storedMarks=null,this}get selectionSet(){return(1&this.updated)>0}setStoredMarks(t){return this.storedMarks=t,this.updated|=2,this}ensureMarks(t){return r.CU.sameSet(this.storedMarks||this.selection.$from.marks(),t)||this.setStoredMarks(t),this}addStoredMark(t){return this.ensureMarks(t.addToSet(this.storedMarks||this.selection.$head.marks()))}removeStoredMark(t){return this.ensureMarks(t.removeFromSet(this.storedMarks||this.selection.$head.marks()))}get storedMarksSet(){return(2&this.updated)>0}addStep(t,e){super.addStep(t,e),this.updated=-3&this.updated,this.storedMarks=null}setTime(t){return this.time=t,this}replaceSelection(t){return this.selection.replace(this,t),this}replaceSelectionWith(t,e=!0){let n=this.selection;return e&&(t=t.mark(this.storedMarks||(n.empty?n.$from.marks():n.$from.marksAcross(n.$to)||r.CU.none))),n.replaceWith(this,t),this}deleteSelection(){return this.selection.replace(this),this}insertText(t,e,n){let r=this.doc.type.schema;if(null==e)return t?this.replaceSelectionWith(r.text(t),!0):this.deleteSelection();{if(null==n&&(n=e),n=null==n?e:n,!t)return this.deleteRange(e,n);let o=this.storedMarks;if(!o){let t=this.doc.resolve(e);o=n==e?t.marks():t.marksAcross(this.doc.resolve(n))}return this.replaceRangeWith(e,n,r.text(t,o)),this.selection.empty||this.setSelection(s.near(this.selection.$to)),this}}setMeta(t,e){return this.meta["string"==typeof t?t:t.key]=e,this}getMeta(t){return this.meta["string"==typeof t?t:t.key]}get isGeneric(){for(let t in this.meta)return!1;return!0}scrollIntoView(){return this.updated|=4,this}get scrolledIntoView(){return(4&this.updated)>0}}function b(t,e){return e&&t?t.bind(e):t}class w{constructor(t,e,n){this.name=t,this.init=b(e.init,n),this.apply=b(e.apply,n)}}const x=[new w("doc",{init:t=>t.doc||t.schema.topNodeType.createAndFill(),apply:t=>t.doc}),new w("selection",{init:(t,e)=>t.selection||s.atStart(e.doc),apply:t=>t.selection}),new w("storedMarks",{init:t=>t.storedMarks||null,apply:(t,e,n,r)=>r.selection.$cursor?t.storedMarks:null}),new w("scrollToSelection",{init:()=>0,apply:(t,e)=>t.scrolledIntoView?e+1:e})];class k{constructor(t,e){this.schema=t,this.plugins=[],this.pluginsByKey=Object.create(null),this.fields=x.slice(),e&&e.forEach((t=>{if(this.pluginsByKey[t.key])throw new RangeError("Adding different instances of a keyed plugin ("+t.key+")");this.plugins.push(t),this.pluginsByKey[t.key]=t,t.spec.state&&this.fields.push(new w(t.key,t.spec.state,t))}))}}class S{constructor(t){this.config=t}get schema(){return this.config.schema}get plugins(){return this.config.plugins}apply(t){return this.applyTransaction(t).state}filterTransaction(t,e=-1){for(let n=0;n<this.config.plugins.length;n++)if(n!=e){let e=this.config.plugins[n];if(e.spec.filterTransaction&&!e.spec.filterTransaction.call(e,t,this))return!1}return!0}applyTransaction(t){if(!this.filterTransaction(t))return{state:this,transactions:[]};let e=[t],n=this.applyInner(t),r=null;for(;;){let o=!1;for(let i=0;i<this.config.plugins.length;i++){let s=this.config.plugins[i];if(s.spec.appendTransaction){let a=r?r[i].n:0,l=r?r[i].state:this,c=a<e.length&&s.spec.appendTransaction.call(s,a?e.slice(a):e,l,n);if(c&&n.filterTransaction(c,i)){if(c.setMeta("appendedTransaction",t),!r){r=[];for(let t=0;t<this.config.plugins.length;t++)r.push(t<i?{state:n,n:e.length}:{state:this,n:0})}e.push(c),n=n.applyInner(c),o=!0}r&&(r[i]={state:n,n:e.length})}}if(!o)return{state:n,transactions:e}}}applyInner(t){if(!t.before.eq(this.doc))throw new RangeError("Applying a mismatched transaction");let e=new S(this.config),n=this.config.fields;for(let r=0;r<n.length;r++){let o=n[r];e[o.name]=o.apply(t,this[o.name],this,e)}return e}get tr(){return new v(this)}static create(t){let e=new k(t.doc?t.doc.type.schema:t.schema,t.plugins),n=new S(e);for(let r=0;r<e.fields.length;r++)n[e.fields[r].name]=e.fields[r].init(t,n);return n}reconfigure(t){let e=new k(this.schema,t.plugins),n=e.fields,r=new S(e);for(let e=0;e<n.length;e++){let o=n[e].name;r[o]=this.hasOwnProperty(o)?this[o]:n[e].init(t,r)}return r}toJSON(t){let e={doc:this.doc.toJSON(),selection:this.selection.toJSON()};if(this.storedMarks&&(e.storedMarks=this.storedMarks.map((t=>t.toJSON()))),t&&"object"==typeof t)for(let n in t){if("doc"==n||"selection"==n)throw new RangeError("The JSON fields `doc` and `selection` are reserved");let r=t[n],o=r.spec.state;o&&o.toJSON&&(e[n]=o.toJSON.call(r,this[r.key]))}return e}static fromJSON(t,e,n){if(!e)throw new RangeError("Invalid input for EditorState.fromJSON");if(!t.schema)throw new RangeError("Required config field 'schema' missing");let o=new k(t.schema,t.plugins),i=new S(o);return o.fields.forEach((o=>{if("doc"==o.name)i.doc=r.bP.fromJSON(t.schema,e.doc);else if("selection"==o.name)i.selection=s.fromJSON(i.doc,e.selection);else if("storedMarks"==o.name)e.storedMarks&&(i.storedMarks=e.storedMarks.map(t.schema.markFromJSON));else{if(n)for(let r in n){let s=n[r],a=s.spec.state;if(s.key==o.name&&a&&a.fromJSON&&Object.prototype.hasOwnProperty.call(e,r))return void(i[o.name]=a.fromJSON.call(s,t,e[r],i))}i[o.name]=o.init(t,i)}})),i}}function M(t,e,n){for(let r in t){let o=t[r];o instanceof Function?o=o.bind(e):"handleDOMEvents"==r&&(o=M(o,e,{})),n[r]=o}return n}class C{constructor(t){this.spec=t,this.props={},t.props&&M(t.props,this,this.props),this.key=t.key?t.key.key:T("plugin")}getState(t){return t[this.key]}}const A=Object.create(null);function T(t){return t in A?t+"$"+ ++A[t]:(A[t]=0,t+"$")}class O{constructor(t="key"){this.key=T(t)}get(t){return t.config.pluginsByKey[this.key]}getState(t){return t[this.key]}}},196:(t,e,n)=>{"use strict";n.d(e,{$L:()=>_,Ln:()=>y,N0:()=>O,Um:()=>E,Wg:()=>v,X9:()=>l,dL:()=>V,jP:()=>k,n9:()=>A,oM:()=>S,zy:()=>C});var r=n(9679);const o=Math.pow(2,16);function i(t){return 65535&t}class s{constructor(t,e,n){this.pos=t,this.delInfo=e,this.recover=n}get deleted(){return(8&this.delInfo)>0}get deletedBefore(){return(5&this.delInfo)>0}get deletedAfter(){return(6&this.delInfo)>0}get deletedAcross(){return(4&this.delInfo)>0}}class a{constructor(t,e=!1){if(this.ranges=t,this.inverted=e,!t.length&&a.empty)return a.empty}recover(t){let e=0,n=i(t);if(!this.inverted)for(let t=0;t<n;t++)e+=this.ranges[3*t+2]-this.ranges[3*t+1];return this.ranges[3*n]+e+function(t){return(t-(65535&t))/o}(t)}mapResult(t,e=1){return this._map(t,e,!1)}map(t,e=1){return this._map(t,e,!0)}_map(t,e,n){let r=0,i=this.inverted?2:1,a=this.inverted?1:2;for(let l=0;l<this.ranges.length;l+=3){let c=this.ranges[l]-(this.inverted?r:0);if(c>t)break;let d=this.ranges[l+i],u=this.ranges[l+a],h=c+d;if(t<=h){let i=c+r+((d?t==c?-1:t==h?1:e:e)<0?0:u);if(n)return i;let a=t==c?2:t==h?1:4;return(e<0?t!=c:t!=h)&&(a|=8),new s(i,a,t==(e<0?c:h)?null:l/3+(t-c)*o)}r+=u-d}return n?t+r:new s(t+r,0,null)}touches(t,e){let n=0,r=i(e),o=this.inverted?2:1,s=this.inverted?1:2;for(let e=0;e<this.ranges.length;e+=3){let i=this.ranges[e]-(this.inverted?n:0);if(i>t)break;let a=this.ranges[e+o];if(t<=i+a&&e==3*r)return!0;n+=this.ranges[e+s]-a}return!1}forEach(t){let e=this.inverted?2:1,n=this.inverted?1:2;for(let r=0,o=0;r<this.ranges.length;r+=3){let i=this.ranges[r],s=i-(this.inverted?o:0),a=i+(this.inverted?0:o),l=this.ranges[r+e],c=this.ranges[r+n];t(s,s+l,a,a+c),o+=c-l}}invert(){return new a(this.ranges,!this.inverted)}toString(){return(this.inverted?"-":"")+JSON.stringify(this.ranges)}static offset(t){return 0==t?a.empty:new a(t<0?[0,-t,0]:[0,0,t])}}a.empty=new a([]);class l{constructor(t=[],e,n=0,r=t.length){this.maps=t,this.mirror=e,this.from=n,this.to=r}slice(t=0,e=this.maps.length){return new l(this.maps,this.mirror,t,e)}copy(){return new l(this.maps.slice(),this.mirror&&this.mirror.slice(),this.from,this.to)}appendMap(t,e){this.to=this.maps.push(t),null!=e&&this.setMirror(this.maps.length-1,e)}appendMapping(t){for(let e=0,n=this.maps.length;e<t.maps.length;e++){let r=t.getMirror(e);this.appendMap(t.maps[e],null!=r&&r<e?n+r:void 0)}}getMirror(t){if(this.mirror)for(let e=0;e<this.mirror.length;e++)if(this.mirror[e]==t)return this.mirror[e+(e%2?-1:1)]}setMirror(t,e){this.mirror||(this.mirror=[]),this.mirror.push(t,e)}appendMappingInverted(t){for(let e=t.maps.length-1,n=this.maps.length+t.maps.length;e>=0;e--){let r=t.getMirror(e);this.appendMap(t.maps[e].invert(),null!=r&&r>e?n-r-1:void 0)}}invert(){let t=new l;return t.appendMappingInverted(this),t}map(t,e=1){if(this.mirror)return this._map(t,e,!0);for(let n=this.from;n<this.to;n++)t=this.maps[n].map(t,e);return t}mapResult(t,e=1){return this._map(t,e,!1)}_map(t,e,n){let r=0;for(let n=this.from;n<this.to;n++){let o=this.maps[n].mapResult(t,e);if(null!=o.recover){let e=this.getMirror(n);if(null!=e&&e>n&&e<this.to){n=e,t=this.maps[e].recover(o.recover);continue}}r|=o.delInfo,t=o.pos}return n?t:new s(t,r,null)}}const c=Object.create(null);class d{getMap(){return a.empty}merge(t){return null}static fromJSON(t,e){if(!e||!e.stepType)throw new RangeError("Invalid input for Step.fromJSON");let n=c[e.stepType];if(!n)throw new RangeError(`No step type ${e.stepType} defined`);return n.fromJSON(t,e)}static jsonID(t,e){if(t in c)throw new RangeError("Duplicate use of step JSON ID "+t);return c[t]=e,e.prototype.jsonID=t,e}}class u{constructor(t,e){this.doc=t,this.failed=e}static ok(t){return new u(t,null)}static fail(t){return new u(null,t)}static fromReplace(t,e,n,o){try{return u.ok(t.replace(e,n,o))}catch(t){if(t instanceof r.vI)return u.fail(t.message);throw t}}}function h(t,e,n){let o=[];for(let r=0;r<t.childCount;r++){let i=t.child(r);i.content.size&&(i=i.copy(h(i.content,e,i))),i.isInline&&(i=e(i,n,r)),o.push(i)}return r.FK.fromArray(o)}class p extends d{constructor(t,e,n){super(),this.from=t,this.to=e,this.mark=n}apply(t){let e=t.slice(this.from,this.to),n=t.resolve(this.from),o=n.node(n.sharedDepth(this.to)),i=new r.Ji(h(e.content,((t,e)=>t.isAtom&&e.type.allowsMarkType(this.mark.type)?t.mark(this.mark.addToSet(t.marks)):t),o),e.openStart,e.openEnd);return u.fromReplace(t,this.from,this.to,i)}invert(){return new f(this.from,this.to,this.mark)}map(t){let e=t.mapResult(this.from,1),n=t.mapResult(this.to,-1);return e.deleted&&n.deleted||e.pos>=n.pos?null:new p(e.pos,n.pos,this.mark)}merge(t){return t instanceof p&&t.mark.eq(this.mark)&&this.from<=t.to&&this.to>=t.from?new p(Math.min(this.from,t.from),Math.max(this.to,t.to),this.mark):null}toJSON(){return{stepType:"addMark",mark:this.mark.toJSON(),from:this.from,to:this.to}}static fromJSON(t,e){if("number"!=typeof e.from||"number"!=typeof e.to)throw new RangeError("Invalid input for AddMarkStep.fromJSON");return new p(e.from,e.to,t.markFromJSON(e.mark))}}d.jsonID("addMark",p);class f extends d{constructor(t,e,n){super(),this.from=t,this.to=e,this.mark=n}apply(t){let e=t.slice(this.from,this.to),n=new r.Ji(h(e.content,(t=>t.mark(this.mark.removeFromSet(t.marks))),t),e.openStart,e.openEnd);return u.fromReplace(t,this.from,this.to,n)}invert(){return new p(this.from,this.to,this.mark)}map(t){let e=t.mapResult(this.from,1),n=t.mapResult(this.to,-1);return e.deleted&&n.deleted||e.pos>=n.pos?null:new f(e.pos,n.pos,this.mark)}merge(t){return t instanceof f&&t.mark.eq(this.mark)&&this.from<=t.to&&this.to>=t.from?new f(Math.min(this.from,t.from),Math.max(this.to,t.to),this.mark):null}toJSON(){return{stepType:"removeMark",mark:this.mark.toJSON(),from:this.from,to:this.to}}static fromJSON(t,e){if("number"!=typeof e.from||"number"!=typeof e.to)throw new RangeError("Invalid input for RemoveMarkStep.fromJSON");return new f(e.from,e.to,t.markFromJSON(e.mark))}}d.jsonID("removeMark",f);class m extends d{constructor(t,e){super(),this.pos=t,this.mark=e}apply(t){let e=t.nodeAt(this.pos);if(!e)return u.fail("No node at mark step's position");let n=e.type.create(e.attrs,null,this.mark.addToSet(e.marks));return u.fromReplace(t,this.pos,this.pos+1,new r.Ji(r.FK.from(n),0,e.isLeaf?0:1))}invert(t){let e=t.nodeAt(this.pos);if(e){let t=this.mark.addToSet(e.marks);if(t.length==e.marks.length){for(let n=0;n<e.marks.length;n++)if(!e.marks[n].isInSet(t))return new m(this.pos,e.marks[n]);return new m(this.pos,this.mark)}}return new g(this.pos,this.mark)}map(t){let e=t.mapResult(this.pos,1);return e.deletedAfter?null:new m(e.pos,this.mark)}toJSON(){return{stepType:"addNodeMark",pos:this.pos,mark:this.mark.toJSON()}}static fromJSON(t,e){if("number"!=typeof e.pos)throw new RangeError("Invalid input for AddNodeMarkStep.fromJSON");return new m(e.pos,t.markFromJSON(e.mark))}}d.jsonID("addNodeMark",m);class g extends d{constructor(t,e){super(),this.pos=t,this.mark=e}apply(t){let e=t.nodeAt(this.pos);if(!e)return u.fail("No node at mark step's position");let n=e.type.create(e.attrs,null,this.mark.removeFromSet(e.marks));return u.fromReplace(t,this.pos,this.pos+1,new r.Ji(r.FK.from(n),0,e.isLeaf?0:1))}invert(t){let e=t.nodeAt(this.pos);return e&&this.mark.isInSet(e.marks)?new m(this.pos,this.mark):this}map(t){let e=t.mapResult(this.pos,1);return e.deletedAfter?null:new g(e.pos,this.mark)}toJSON(){return{stepType:"removeNodeMark",pos:this.pos,mark:this.mark.toJSON()}}static fromJSON(t,e){if("number"!=typeof e.pos)throw new RangeError("Invalid input for RemoveNodeMarkStep.fromJSON");return new g(e.pos,t.markFromJSON(e.mark))}}d.jsonID("removeNodeMark",g);class y extends d{constructor(t,e,n,r=!1){super(),this.from=t,this.to=e,this.slice=n,this.structure=r}apply(t){return this.structure&&b(t,this.from,this.to)?u.fail("Structure replace would overwrite content"):u.fromReplace(t,this.from,this.to,this.slice)}getMap(){return new a([this.from,this.to-this.from,this.slice.size])}invert(t){return new y(this.from,this.from+this.slice.size,t.slice(this.from,this.to))}map(t){let e=t.mapResult(this.from,1),n=t.mapResult(this.to,-1);return e.deletedAcross&&n.deletedAcross?null:new y(e.pos,Math.max(e.pos,n.pos),this.slice)}merge(t){if(!(t instanceof y)||t.structure||this.structure)return null;if(this.from+this.slice.size!=t.from||this.slice.openEnd||t.slice.openStart){if(t.to!=this.from||this.slice.openStart||t.slice.openEnd)return null;{let e=this.slice.size+t.slice.size==0?r.Ji.empty:new r.Ji(t.slice.content.append(this.slice.content),t.slice.openStart,this.slice.openEnd);return new y(t.from,this.to,e,this.structure)}}{let e=this.slice.size+t.slice.size==0?r.Ji.empty:new r.Ji(this.slice.content.append(t.slice.content),this.slice.openStart,t.slice.openEnd);return new y(this.from,this.to+(t.to-t.from),e,this.structure)}}toJSON(){let t={stepType:"replace",from:this.from,to:this.to};return this.slice.size&&(t.slice=this.slice.toJSON()),this.structure&&(t.structure=!0),t}static fromJSON(t,e){if("number"!=typeof e.from||"number"!=typeof e.to)throw new RangeError("Invalid input for ReplaceStep.fromJSON");return new y(e.from,e.to,r.Ji.fromJSON(t,e.slice),!!e.structure)}}d.jsonID("replace",y);class v extends d{constructor(t,e,n,r,o,i,s=!1){super(),this.from=t,this.to=e,this.gapFrom=n,this.gapTo=r,this.slice=o,this.insert=i,this.structure=s}apply(t){if(this.structure&&(b(t,this.from,this.gapFrom)||b(t,this.gapTo,this.to)))return u.fail("Structure gap-replace would overwrite content");let e=t.slice(this.gapFrom,this.gapTo);if(e.openStart||e.openEnd)return u.fail("Gap is not a flat range");let n=this.slice.insertAt(this.insert,e.content);return n?u.fromReplace(t,this.from,this.to,n):u.fail("Content does not fit in gap")}getMap(){return new a([this.from,this.gapFrom-this.from,this.insert,this.gapTo,this.to-this.gapTo,this.slice.size-this.insert])}invert(t){let e=this.gapTo-this.gapFrom;return new v(this.from,this.from+this.slice.size+e,this.from+this.insert,this.from+this.insert+e,t.slice(this.from,this.to).removeBetween(this.gapFrom-this.from,this.gapTo-this.from),this.gapFrom-this.from,this.structure)}map(t){let e=t.mapResult(this.from,1),n=t.mapResult(this.to,-1),r=this.from==this.gapFrom?e.pos:t.map(this.gapFrom,-1),o=this.to==this.gapTo?n.pos:t.map(this.gapTo,1);return e.deletedAcross&&n.deletedAcross||r<e.pos||o>n.pos?null:new v(e.pos,n.pos,r,o,this.slice,this.insert,this.structure)}toJSON(){let t={stepType:"replaceAround",from:this.from,to:this.to,gapFrom:this.gapFrom,gapTo:this.gapTo,insert:this.insert};return this.slice.size&&(t.slice=this.slice.toJSON()),this.structure&&(t.structure=!0),t}static fromJSON(t,e){if("number"!=typeof e.from||"number"!=typeof e.to||"number"!=typeof e.gapFrom||"number"!=typeof e.gapTo||"number"!=typeof e.insert)throw new RangeError("Invalid input for ReplaceAroundStep.fromJSON");return new v(e.from,e.to,e.gapFrom,e.gapTo,r.Ji.fromJSON(t,e.slice),e.insert,!!e.structure)}}function b(t,e,n){let r=t.resolve(e),o=n-e,i=r.depth;for(;o>0&&i>0&&r.indexAfter(i)==r.node(i).childCount;)i--,o--;if(o>0){let t=r.node(i).maybeChild(r.indexAfter(i));for(;o>0;){if(!t||t.isLeaf)return!0;t=t.firstChild,o--}}return!1}function w(t,e,n,o=n.contentMatch,i=!0){let s=t.doc.nodeAt(e),a=[],l=e+1;for(let e=0;e<s.childCount;e++){let c=s.child(e),d=l+c.nodeSize,u=o.matchType(c.type);if(u){o=u;for(let e=0;e<c.marks.length;e++)n.allowsMarkType(c.marks[e].type)||t.step(new f(l,d,c.marks[e]));if(i&&c.isText&&"pre"!=n.whitespace){let t,e,o=/\r?\n|\r/g;for(;t=o.exec(c.text);)e||(e=new r.Ji(r.FK.from(n.schema.text(" ",n.allowedMarks(c.marks))),0,0)),a.push(new y(l+t.index,l+t.index+t[0].length,e))}}else a.push(new y(l,d,r.Ji.empty));l=d}if(!o.validEnd){let e=o.fillBefore(r.FK.empty,!0);t.replace(l,l,new r.Ji(e,0,0))}for(let e=a.length-1;e>=0;e--)t.step(a[e])}function x(t,e,n){return(0==e||t.canReplace(e,t.childCount))&&(n==t.childCount||t.canReplace(0,n))}function k(t){let e=t.parent.content.cutByIndex(t.startIndex,t.endIndex);for(let n=t.depth;;--n){let r=t.$from.node(n),o=t.$from.index(n),i=t.$to.indexAfter(n);if(n<t.depth&&r.canReplace(o,i,e))return n;if(0==n||r.type.spec.isolating||!x(r,o,i))break}return null}function S(t,e,n=null,r=t){let o=function(t,e){let{parent:n,startIndex:r,endIndex:o}=t,i=n.contentMatchAt(r).findWrapping(e);if(!i)return null;let s=i.length?i[0]:e;return n.canReplaceWith(r,o,s)?i:null}(t,e),i=o&&function(t,e){let{parent:n,startIndex:r,endIndex:o}=t,i=n.child(r),s=e.contentMatch.findWrapping(i.type);if(!s)return null;let a=(s.length?s[s.length-1]:e).contentMatch;for(let t=r;a&&t<o;t++)a=a.matchType(n.child(t).type);return a&&a.validEnd?s:null}(r,e);return i?o.map(M).concat({type:e,attrs:n}).concat(i.map(M)):null}function M(t){return{type:t,attrs:null}}function C(t,e,n=1,r){let o=t.resolve(e),i=o.depth-n,s=r&&r[r.length-1]||o.parent;if(i<0||o.parent.type.spec.isolating||!o.parent.canReplace(o.index(),o.parent.childCount)||!s.type.validContent(o.parent.content.cutByIndex(o.index(),o.parent.childCount)))return!1;for(let t=o.depth-1,e=n-2;t>i;t--,e--){let n=o.node(t),i=o.index(t);if(n.type.spec.isolating)return!1;let s=n.content.cutByIndex(i,n.childCount),a=r&&r[e+1];a&&(s=s.replaceChild(0,a.type.create(a.attrs)));let l=r&&r[e]||n;if(!n.canReplace(i+1,n.childCount)||!l.type.validContent(s))return!1}let a=o.indexAfter(i),l=r&&r[0];return o.node(i).canReplaceWith(a,a,l?l.type:o.node(i+1).type)}function A(t,e){let n=t.resolve(e),r=n.index();return T(n.nodeBefore,n.nodeAfter)&&n.parent.canReplace(r,r+1)}function T(t,e){return!(!t||!e||t.isLeaf||!t.canAppend(e))}function O(t,e,n=-1){let r=t.resolve(e);for(let t=r.depth;;t--){let o,i,s=r.index(t);if(t==r.depth?(o=r.nodeBefore,i=r.nodeAfter):n>0?(o=r.node(t+1),s++,i=r.node(t).maybeChild(s)):(o=r.node(t).maybeChild(s-1),i=r.node(t+1)),o&&!o.isTextblock&&T(o,i)&&r.node(t).canReplace(s,s+1))return e;if(0==t)break;e=n<0?r.before(t):r.after(t)}}function E(t,e,n){let r=t.resolve(e);if(!n.content.size)return e;let o=n.content;for(let t=0;t<n.openStart;t++)o=o.firstChild.content;for(let t=1;t<=(0==n.openStart&&n.size?2:1);t++)for(let e=r.depth;e>=0;e--){let n=e==r.depth?0:r.pos<=(r.start(e+1)+r.end(e+1))/2?-1:1,i=r.index(e)+(n>0?1:0),s=r.node(e),a=!1;if(1==t)a=s.canReplace(i,i,o);else{let t=s.contentMatchAt(i).findWrapping(o.firstChild.type);a=t&&s.canReplaceWith(i,i,t[0])}if(a)return 0==n?r.pos:n<0?r.before(e+1):r.after(e+1)}return null}function _(t,e,n=e,o=r.Ji.empty){if(e==n&&!o.size)return null;let i=t.resolve(e),s=t.resolve(n);return N(i,s,o)?new y(e,n,o):new $(i,s,o).fit()}function N(t,e,n){return!n.openStart&&!n.openEnd&&t.start()==e.start()&&t.parent.canReplace(t.index(),e.index(),n.content)}d.jsonID("replaceAround",v);class ${constructor(t,e,n){this.$from=t,this.$to=e,this.unplaced=n,this.frontier=[],this.placed=r.FK.empty;for(let e=0;e<=t.depth;e++){let n=t.node(e);this.frontier.push({type:n.type,match:n.contentMatchAt(t.indexAfter(e))})}for(let e=t.depth;e>0;e--)this.placed=r.FK.from(t.node(e).copy(this.placed))}get depth(){return this.frontier.length-1}fit(){for(;this.unplaced.size;){let t=this.findFittable();t?this.placeNodes(t):this.openMore()||this.dropNode()}let t=this.mustMoveInline(),e=this.placed.size-this.depth-this.$from.depth,n=this.$from,o=this.close(t<0?this.$to:n.doc.resolve(t));if(!o)return null;let i=this.placed,s=n.depth,a=o.depth;for(;s&&a&&1==i.childCount;)i=i.firstChild.content,s--,a--;let l=new r.Ji(i,s,a);return t>-1?new v(n.pos,t,this.$to.pos,this.$to.end(),l,e):l.size||n.pos!=this.$to.pos?new y(n.pos,o.pos,l):null}findFittable(){let t=this.unplaced.openStart;for(let e=this.unplaced.content,n=0,r=this.unplaced.openEnd;n<t;n++){let o=e.firstChild;if(e.childCount>1&&(r=0),o.type.spec.isolating&&r<=n){t=n;break}e=o.content}for(let e=1;e<=2;e++)for(let n=1==e?t:this.unplaced.openStart;n>=0;n--){let t,o=null;n?(o=P(this.unplaced.content,n-1).firstChild,t=o.content):t=this.unplaced.content;let i=t.firstChild;for(let t=this.depth;t>=0;t--){let s,{type:a,match:l}=this.frontier[t],c=null;if(1==e&&(i?l.matchType(i.type)||(c=l.fillBefore(r.FK.from(i),!1)):o&&a.compatibleContent(o.type)))return{sliceDepth:n,frontierDepth:t,parent:o,inject:c};if(2==e&&i&&(s=l.findWrapping(i.type)))return{sliceDepth:n,frontierDepth:t,parent:o,wrap:s};if(o&&l.matchType(o.type))break}}}openMore(){let{content:t,openStart:e,openEnd:n}=this.unplaced,o=P(t,e);return!(!o.childCount||o.firstChild.isLeaf||(this.unplaced=new r.Ji(t,e+1,Math.max(n,o.size+e>=t.size-n?e+1:0)),0))}dropNode(){let{content:t,openStart:e,openEnd:n}=this.unplaced,o=P(t,e);if(o.childCount<=1&&e>0){let i=t.size-e<=e+o.size;this.unplaced=new r.Ji(R(t,e-1,1),e-1,i?e-1:n)}else this.unplaced=new r.Ji(R(t,e,1),e,n)}placeNodes({sliceDepth:t,frontierDepth:e,parent:n,inject:o,wrap:i}){for(;this.depth>e;)this.closeFrontierNode();if(i)for(let t=0;t<i.length;t++)this.openFrontierNode(i[t]);let s=this.unplaced,a=n?n.content:s.content,l=s.openStart-t,c=0,d=[],{match:u,type:h}=this.frontier[e];if(o){for(let t=0;t<o.childCount;t++)d.push(o.child(t));u=u.matchFragment(o)}let p=a.size+t-(s.content.size-s.openEnd);for(;c<a.childCount;){let t=a.child(c),e=u.matchType(t.type);if(!e)break;c++,(c>1||0==l||t.content.size)&&(u=e,d.push(D(t.mark(h.allowedMarks(t.marks)),1==c?l:0,c==a.childCount?p:-1)))}let f=c==a.childCount;f||(p=-1),this.placed=z(this.placed,e,r.FK.from(d)),this.frontier[e].match=u,f&&p<0&&n&&n.type==this.frontier[this.depth].type&&this.frontier.length>1&&this.closeFrontierNode();for(let t=0,e=a;t<p;t++){let t=e.lastChild;this.frontier.push({type:t.type,match:t.contentMatchAt(t.childCount)}),e=t.content}this.unplaced=f?0==t?r.Ji.empty:new r.Ji(R(s.content,t-1,1),t-1,p<0?s.openEnd:t-1):new r.Ji(R(s.content,t,c),s.openStart,s.openEnd)}mustMoveInline(){if(!this.$to.parent.isTextblock)return-1;let t,e=this.frontier[this.depth];if(!e.type.isTextblock||!L(this.$to,this.$to.depth,e.type,e.match,!1)||this.$to.depth==this.depth&&(t=this.findCloseLevel(this.$to))&&t.depth==this.depth)return-1;let{depth:n}=this.$to,r=this.$to.after(n);for(;n>1&&r==this.$to.end(--n);)++r;return r}findCloseLevel(t){t:for(let e=Math.min(this.depth,t.depth);e>=0;e--){let{match:n,type:r}=this.frontier[e],o=e<t.depth&&t.end(e+1)==t.pos+(t.depth-(e+1)),i=L(t,e,r,n,o);if(i){for(let n=e-1;n>=0;n--){let{match:e,type:r}=this.frontier[n],o=L(t,n,r,e,!0);if(!o||o.childCount)continue t}return{depth:e,fit:i,move:o?t.doc.resolve(t.after(e+1)):t}}}}close(t){let e=this.findCloseLevel(t);if(!e)return null;for(;this.depth>e.depth;)this.closeFrontierNode();e.fit.childCount&&(this.placed=z(this.placed,e.depth,e.fit)),t=e.move;for(let n=e.depth+1;n<=t.depth;n++){let e=t.node(n),r=e.type.contentMatch.fillBefore(e.content,!0,t.index(n));this.openFrontierNode(e.type,e.attrs,r)}return t}openFrontierNode(t,e=null,n){let o=this.frontier[this.depth];o.match=o.match.matchType(t),this.placed=z(this.placed,this.depth,r.FK.from(t.create(e,n))),this.frontier.push({type:t,match:t.contentMatch})}closeFrontierNode(){let t=this.frontier.pop().match.fillBefore(r.FK.empty,!0);t.childCount&&(this.placed=z(this.placed,this.frontier.length,t))}}function R(t,e,n){return 0==e?t.cutByIndex(n,t.childCount):t.replaceChild(0,t.firstChild.copy(R(t.firstChild.content,e-1,n)))}function z(t,e,n){return 0==e?t.append(n):t.replaceChild(t.childCount-1,t.lastChild.copy(z(t.lastChild.content,e-1,n)))}function P(t,e){for(let n=0;n<e;n++)t=t.firstChild.content;return t}function D(t,e,n){if(e<=0)return t;let o=t.content;return e>1&&(o=o.replaceChild(0,D(o.firstChild,e-1,1==o.childCount?n-1:0))),e>0&&(o=t.type.contentMatch.fillBefore(o).append(o),n<=0&&(o=o.append(t.type.contentMatch.matchFragment(o).fillBefore(r.FK.empty,!0)))),t.copy(o)}function L(t,e,n,r,o){let i=t.node(e),s=o?t.indexAfter(e):t.index(e);if(s==i.childCount&&!n.compatibleContent(i.type))return null;let a=r.fillBefore(i.content,!0,s);return a&&!function(t,e,n){for(let r=n;r<e.childCount;r++)if(!t.allowsMarks(e.child(r).marks))return!0;return!1}(n,i.content,s)?a:null}function I(t,e,n,o,i){if(e<n){let r=t.firstChild;t=t.replaceChild(0,r.copy(I(r.content,e+1,n,o,r)))}if(e>o){let e=i.contentMatchAt(0),n=e.fillBefore(t).append(t);t=n.append(e.matchFragment(n).fillBefore(r.FK.empty,!0))}return t}function F(t,e){let n=[];for(let r=Math.min(t.depth,e.depth);r>=0;r--){let o=t.start(r);if(o<t.pos-(t.depth-r)||e.end(r)>e.pos+(e.depth-r)||t.node(r).type.spec.isolating||e.node(r).type.spec.isolating)break;(o==e.start(r)||r==t.depth&&r==e.depth&&t.parent.inlineContent&&e.parent.inlineContent&&r&&e.start(r-1)==o-1)&&n.push(r)}return n}class j extends d{constructor(t,e,n){super(),this.pos=t,this.attr=e,this.value=n}apply(t){let e=t.nodeAt(this.pos);if(!e)return u.fail("No node at attribute step's position");let n=Object.create(null);for(let t in e.attrs)n[t]=e.attrs[t];n[this.attr]=this.value;let o=e.type.create(n,null,e.marks);return u.fromReplace(t,this.pos,this.pos+1,new r.Ji(r.FK.from(o),0,e.isLeaf?0:1))}getMap(){return a.empty}invert(t){return new j(this.pos,this.attr,t.nodeAt(this.pos).attrs[this.attr])}map(t){let e=t.mapResult(this.pos,1);return e.deletedAfter?null:new j(e.pos,this.attr,this.value)}toJSON(){return{stepType:"attr",pos:this.pos,attr:this.attr,value:this.value}}static fromJSON(t,e){if("number"!=typeof e.pos||"string"!=typeof e.attr)throw new RangeError("Invalid input for AttrStep.fromJSON");return new j(e.pos,e.attr,e.value)}}d.jsonID("attr",j);class B extends d{constructor(t,e){super(),this.attr=t,this.value=e}apply(t){let e=Object.create(null);for(let n in t.attrs)e[n]=t.attrs[n];e[this.attr]=this.value;let n=t.type.create(e,t.content,t.marks);return u.ok(n)}getMap(){return a.empty}invert(t){return new B(this.attr,t.attrs[this.attr])}map(t){return this}toJSON(){return{stepType:"docAttr",attr:this.attr,value:this.value}}static fromJSON(t,e){if("string"!=typeof e.attr)throw new RangeError("Invalid input for DocAttrStep.fromJSON");return new B(e.attr,e.value)}}d.jsonID("docAttr",B);let H=class extends Error{};H=function t(e){let n=Error.call(this,e);return n.__proto__=t.prototype,n},(H.prototype=Object.create(Error.prototype)).constructor=H,H.prototype.name="TransformError";class V{constructor(t){this.doc=t,this.steps=[],this.docs=[],this.mapping=new l}get before(){return this.docs.length?this.docs[0]:this.doc}step(t){let e=this.maybeStep(t);if(e.failed)throw new H(e.failed);return this}maybeStep(t){let e=t.apply(this.doc);return e.failed||this.addStep(t,e.doc),e}get docChanged(){return this.steps.length>0}addStep(t,e){this.docs.push(this.doc),this.steps.push(t),this.mapping.appendMap(t.getMap()),this.doc=e}replace(t,e=t,n=r.Ji.empty){let o=_(this.doc,t,e,n);return o&&this.step(o),this}replaceWith(t,e,n){return this.replace(t,e,new r.Ji(r.FK.from(n),0,0))}delete(t,e){return this.replace(t,e,r.Ji.empty)}insert(t,e){return this.replaceWith(t,t,e)}replaceRange(t,e,n){return function(t,e,n,o){if(!o.size)return t.deleteRange(e,n);let i=t.doc.resolve(e),s=t.doc.resolve(n);if(N(i,s,o))return t.step(new y(e,n,o));let a=F(i,t.doc.resolve(n));0==a[a.length-1]&&a.pop();let l=-(i.depth+1);a.unshift(l);for(let t=i.depth,e=i.pos-1;t>0;t--,e--){let n=i.node(t).type.spec;if(n.defining||n.definingAsContext||n.isolating)break;a.indexOf(t)>-1?l=t:i.before(t)==e&&a.splice(1,0,-t)}let c=a.indexOf(l),d=[],u=o.openStart;for(let t=o.content,e=0;;e++){let n=t.firstChild;if(d.push(n),e==o.openStart)break;t=n.content}for(let t=u-1;t>=0;t--){let e=d[t],n=(h=e.type).spec.defining||h.spec.definingForContent;if(n&&!e.sameMarkup(i.node(Math.abs(l)-1)))u=t;else if(n||!e.type.isTextblock)break}var h;for(let e=o.openStart;e>=0;e--){let l=(e+u+1)%(o.openStart+1),h=d[l];if(h)for(let e=0;e<a.length;e++){let d=a[(e+c)%a.length],u=!0;d<0&&(u=!1,d=-d);let p=i.node(d-1),f=i.index(d-1);if(p.canReplaceWith(f,f,h.type,h.marks))return t.replace(i.before(d),u?s.after(d):n,new r.Ji(I(o.content,0,o.openStart,l),l,o.openEnd))}}let p=t.steps.length;for(let r=a.length-1;r>=0&&(t.replace(e,n,o),!(t.steps.length>p));r--){let t=a[r];t<0||(e=i.before(t),n=s.after(t))}}(this,t,e,n),this}replaceRangeWith(t,e,n){return function(t,e,n,o){if(!o.isInline&&e==n&&t.doc.resolve(e).parent.content.size){let r=function(t,e,n){let r=t.resolve(e);if(r.parent.canReplaceWith(r.index(),r.index(),n))return e;if(0==r.parentOffset)for(let t=r.depth-1;t>=0;t--){let e=r.index(t);if(r.node(t).canReplaceWith(e,e,n))return r.before(t+1);if(e>0)return null}if(r.parentOffset==r.parent.content.size)for(let t=r.depth-1;t>=0;t--){let e=r.indexAfter(t);if(r.node(t).canReplaceWith(e,e,n))return r.after(t+1);if(e<r.node(t).childCount)return null}return null}(t.doc,e,o.type);null!=r&&(e=n=r)}t.replaceRange(e,n,new r.Ji(r.FK.from(o),0,0))}(this,t,e,n),this}deleteRange(t,e){return function(t,e,n){let r=t.doc.resolve(e),o=t.doc.resolve(n),i=F(r,o);for(let e=0;e<i.length;e++){let n=i[e],s=e==i.length-1;if(s&&0==n||r.node(n).type.contentMatch.validEnd)return t.delete(r.start(n),o.end(n));if(n>0&&(s||r.node(n-1).canReplace(r.index(n-1),o.indexAfter(n-1))))return t.delete(r.before(n),o.after(n))}for(let i=1;i<=r.depth&&i<=o.depth;i++)if(e-r.start(i)==r.depth-i&&n>r.end(i)&&o.end(i)-n!=o.depth-i)return t.delete(r.before(i),n);t.delete(e,n)}(this,t,e),this}lift(t,e){return function(t,e,n){let{$from:o,$to:i,depth:s}=e,a=o.before(s+1),l=i.after(s+1),c=a,d=l,u=r.FK.empty,h=0;for(let t=s,e=!1;t>n;t--)e||o.index(t)>0?(e=!0,u=r.FK.from(o.node(t).copy(u)),h++):c--;let p=r.FK.empty,f=0;for(let t=s,e=!1;t>n;t--)e||i.after(t+1)<i.end(t)?(e=!0,p=r.FK.from(i.node(t).copy(p)),f++):d++;t.step(new v(c,d,a,l,new r.Ji(u.append(p),h,f),u.size-h,!0))}(this,t,e),this}join(t,e=1){return function(t,e,n){let o=new y(e-n,e+n,r.Ji.empty,!0);t.step(o)}(this,t,e),this}wrap(t,e){return function(t,e,n){let o=r.FK.empty;for(let t=n.length-1;t>=0;t--){if(o.size){let e=n[t].type.contentMatch.matchFragment(o);if(!e||!e.validEnd)throw new RangeError("Wrapper type given to Transform.wrap does not form valid content of its parent wrapper")}o=r.FK.from(n[t].type.create(n[t].attrs,o))}let i=e.start,s=e.end;t.step(new v(i,s,i,s,new r.Ji(o,0,0),n.length,!0))}(this,t,e),this}setBlockType(t,e=t,n,o=null){return function(t,e,n,o,i){if(!o.isTextblock)throw new RangeError("Type given to setBlockType should be a textblock");let s=t.steps.length;t.doc.nodesBetween(e,n,((e,n)=>{if(e.isTextblock&&!e.hasMarkup(o,i)&&function(t,e,n){let r=t.resolve(e),o=r.index();return r.parent.canReplaceWith(o,o+1,n)}(t.doc,t.mapping.slice(s).map(n),o)){let a=null;if(o.schema.linebreakReplacement){let t="pre"==o.whitespace,e=!!o.contentMatch.matchType(o.schema.linebreakReplacement);t&&!e?a=!1:!t&&e&&(a=!0)}!1===a&&function(t,e,n,r){e.forEach(((o,i)=>{if(o.type==o.type.schema.linebreakReplacement){let o=t.mapping.slice(r).map(n+1+i);t.replaceWith(o,o+1,e.type.schema.text("\n"))}}))}(t,e,n,s),w(t,t.mapping.slice(s).map(n,1),o,void 0,null===a);let l=t.mapping.slice(s),c=l.map(n,1),d=l.map(n+e.nodeSize,1);return t.step(new v(c,d,c+1,d-1,new r.Ji(r.FK.from(o.create(i,null,e.marks)),0,0),1,!0)),!0===a&&function(t,e,n,r){e.forEach(((o,i)=>{if(o.isText){let s,a=/\r?\n|\r/g;for(;s=a.exec(o.text);){let o=t.mapping.slice(r).map(n+1+i+s.index);t.replaceWith(o,o+1,e.type.schema.linebreakReplacement.create())}}}))}(t,e,n,s),!1}}))}(this,t,e,n,o),this}setNodeMarkup(t,e,n=null,o){return function(t,e,n,o,i){let s=t.doc.nodeAt(e);if(!s)throw new RangeError("No node at given position");n||(n=s.type);let a=n.create(o,null,i||s.marks);if(s.isLeaf)return t.replaceWith(e,e+s.nodeSize,a);if(!n.validContent(s.content))throw new RangeError("Invalid content for node type "+n.name);t.step(new v(e,e+s.nodeSize,e+1,e+s.nodeSize-1,new r.Ji(r.FK.from(a),0,0),1,!0))}(this,t,e,n,o),this}setNodeAttribute(t,e,n){return this.step(new j(t,e,n)),this}setDocAttribute(t,e){return this.step(new B(t,e)),this}addNodeMark(t,e){return this.step(new m(t,e)),this}removeNodeMark(t,e){if(!(e instanceof r.CU)){let n=this.doc.nodeAt(t);if(!n)throw new RangeError("No node at position "+t);if(!(e=e.isInSet(n.marks)))return this}return this.step(new g(t,e)),this}split(t,e=1,n){return function(t,e,n=1,o){let i=t.doc.resolve(e),s=r.FK.empty,a=r.FK.empty;for(let t=i.depth,e=i.depth-n,l=n-1;t>e;t--,l--){s=r.FK.from(i.node(t).copy(s));let e=o&&o[l];a=r.FK.from(e?e.type.create(e.attrs,a):i.node(t).copy(a))}t.step(new y(e,e,new r.Ji(s.append(a),n,n),!0))}(this,t,e,n),this}addMark(t,e,n){return function(t,e,n,r){let o,i,s=[],a=[];t.doc.nodesBetween(e,n,((t,l,c)=>{if(!t.isInline)return;let d=t.marks;if(!r.isInSet(d)&&c.type.allowsMarkType(r.type)){let c=Math.max(l,e),u=Math.min(l+t.nodeSize,n),h=r.addToSet(d);for(let t=0;t<d.length;t++)d[t].isInSet(h)||(o&&o.to==c&&o.mark.eq(d[t])?o.to=u:s.push(o=new f(c,u,d[t])));i&&i.to==c?i.to=u:a.push(i=new p(c,u,r))}})),s.forEach((e=>t.step(e))),a.forEach((e=>t.step(e)))}(this,t,e,n),this}removeMark(t,e,n){return function(t,e,n,o){let i=[],s=0;t.doc.nodesBetween(e,n,((t,a)=>{if(!t.isInline)return;s++;let l=null;if(o instanceof r.sX){let e,n=t.marks;for(;e=o.isInSet(n);)(l||(l=[])).push(e),n=e.removeFromSet(n)}else o?o.isInSet(t.marks)&&(l=[o]):l=t.marks;if(l&&l.length){let r=Math.min(a+t.nodeSize,n);for(let t=0;t<l.length;t++){let n,o=l[t];for(let t=0;t<i.length;t++){let e=i[t];e.step==s-1&&o.eq(i[t].style)&&(n=e)}n?(n.to=r,n.step=s):i.push({style:o,from:Math.max(a,e),to:r,step:s})}}})),i.forEach((e=>t.step(new f(e.from,e.to,e.style))))}(this,t,e,n),this}clearIncompatible(t,e,n){return w(this,t,e,n),this}}},5873:(t,e,n)=>{"use strict";n.d(e,{Lz:()=>dn,NZ:()=>De,zF:()=>Fe});var r=n(2559),o=n(9679),i=n(196);const s=function(t){for(var e=0;;e++)if(!(t=t.previousSibling))return e},a=function(t){let e=t.assignedSlot||t.parentNode;return e&&11==e.nodeType?e.host:e};let l=null;const c=function(t,e,n){let r=l||(l=document.createRange());return r.setEnd(t,null==n?t.nodeValue.length:n),r.setStart(t,e||0),r},d=function(t,e,n,r){return n&&(h(t,e,n,r,-1)||h(t,e,n,r,1))},u=/^(img|br|input|textarea|hr)$/i;function h(t,e,n,r,o){for(;;){if(t==n&&e==r)return!0;if(e==(o<0?0:p(t))){let n=t.parentNode;if(!n||1!=n.nodeType||f(t)||u.test(t.nodeName)||"false"==t.contentEditable)return!1;e=s(t)+(o<0?0:1),t=n}else{if(1!=t.nodeType)return!1;if("false"==(t=t.childNodes[e+(o<0?-1:0)]).contentEditable)return!1;e=o<0?p(t):0}}}function p(t){return 3==t.nodeType?t.nodeValue.length:t.childNodes.length}function f(t){let e;for(let n=t;n&&!(e=n.pmViewDesc);n=n.parentNode);return e&&e.node&&e.node.isBlock&&(e.dom==t||e.contentDOM==t)}const m=function(t){return t.focusNode&&d(t.focusNode,t.focusOffset,t.anchorNode,t.anchorOffset)};function g(t,e){let n=document.createEvent("Event");return n.initEvent("keydown",!0,!0),n.keyCode=t,n.key=n.code=e,n}const y="undefined"!=typeof navigator?navigator:null,v="undefined"!=typeof document?document:null,b=y&&y.userAgent||"",w=/Edge\/(\d+)/.exec(b),x=/MSIE \d/.exec(b),k=/Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(b),S=!!(x||k||w),M=x?document.documentMode:k?+k[1]:w?+w[1]:0,C=!S&&/gecko\/(\d+)/i.test(b);C&&(/Firefox\/(\d+)/.exec(b)||[0,0])[1];const A=!S&&/Chrome\/(\d+)/.exec(b),T=!!A,O=A?+A[1]:0,E=!S&&!!y&&/Apple Computer/.test(y.vendor),_=E&&(/Mobile\/\w+/.test(b)||!!y&&y.maxTouchPoints>2),N=_||!!y&&/Mac/.test(y.platform),$=!!y&&/Win/.test(y.platform),R=/Android \d/.test(b),z=!!v&&"webkitFontSmoothing"in v.documentElement.style,P=z?+(/\bAppleWebKit\/(\d+)/.exec(navigator.userAgent)||[0,0])[1]:0;function D(t){let e=t.defaultView&&t.defaultView.visualViewport;return e?{left:0,right:e.width,top:0,bottom:e.height}:{left:0,right:t.documentElement.clientWidth,top:0,bottom:t.documentElement.clientHeight}}function L(t,e){return"number"==typeof t?t:t[e]}function I(t){let e=t.getBoundingClientRect(),n=e.width/t.offsetWidth||1,r=e.height/t.offsetHeight||1;return{left:e.left,right:e.left+t.clientWidth*n,top:e.top,bottom:e.top+t.clientHeight*r}}function F(t,e,n){let r=t.someProp("scrollThreshold")||0,o=t.someProp("scrollMargin")||5,i=t.dom.ownerDocument;for(let s=n||t.dom;s;s=a(s)){if(1!=s.nodeType)continue;let t=s,n=t==i.body,a=n?D(i):I(t),l=0,c=0;if(e.top<a.top+L(r,"top")?c=-(a.top-e.top+L(o,"top")):e.bottom>a.bottom-L(r,"bottom")&&(c=e.bottom-e.top>a.bottom-a.top?e.top+L(o,"top")-a.top:e.bottom-a.bottom+L(o,"bottom")),e.left<a.left+L(r,"left")?l=-(a.left-e.left+L(o,"left")):e.right>a.right-L(r,"right")&&(l=e.right-a.right+L(o,"right")),l||c)if(n)i.defaultView.scrollBy(l,c);else{let n=t.scrollLeft,r=t.scrollTop;c&&(t.scrollTop+=c),l&&(t.scrollLeft+=l);let o=t.scrollLeft-n,i=t.scrollTop-r;e={left:e.left-o,top:e.top-i,right:e.right-o,bottom:e.bottom-i}}if(n||/^(fixed|sticky)$/.test(getComputedStyle(s).position))break}}function j(t){let e=[],n=t.ownerDocument;for(let r=t;r&&(e.push({dom:r,top:r.scrollTop,left:r.scrollLeft}),t!=n);r=a(r));return e}function B(t,e){for(let n=0;n<t.length;n++){let{dom:r,top:o,left:i}=t[n];r.scrollTop!=o+e&&(r.scrollTop=o+e),r.scrollLeft!=i&&(r.scrollLeft=i)}}let H=null;function V(t,e){let n,r,o,i,s=2e8,a=0,l=e.top,d=e.top;for(let u=t.firstChild,h=0;u;u=u.nextSibling,h++){let t;if(1==u.nodeType)t=u.getClientRects();else{if(3!=u.nodeType)continue;t=c(u).getClientRects()}for(let c=0;c<t.length;c++){let p=t[c];if(p.top<=l&&p.bottom>=d){l=Math.max(p.bottom,l),d=Math.min(p.top,d);let t=p.left>e.left?p.left-e.left:p.right<e.left?e.left-p.right:0;if(t<s){n=u,s=t,r=t&&3==n.nodeType?{left:p.right<e.left?p.right:p.left,top:e.top}:e,1==u.nodeType&&t&&(a=h+(e.left>=(p.left+p.right)/2?1:0));continue}}else p.top>e.top&&!o&&p.left<=e.left&&p.right>=e.left&&(o=u,i={left:Math.max(p.left,Math.min(p.right,e.left)),top:p.top});!n&&(e.left>=p.right&&e.top>=p.top||e.left>=p.left&&e.top>=p.bottom)&&(a=h+1)}}return!n&&o&&(n=o,r=i,s=0),n&&3==n.nodeType?function(t,e){let n=t.nodeValue.length,r=document.createRange();for(let o=0;o<n;o++){r.setEnd(t,o+1),r.setStart(t,o);let n=q(r,1);if(n.top!=n.bottom&&K(e,n))return{node:t,offset:o+(e.left>=(n.left+n.right)/2?1:0)}}return{node:t,offset:0}}(n,r):!n||s&&1==n.nodeType?{node:t,offset:a}:V(n,r)}function K(t,e){return t.left>=e.left-1&&t.left<=e.right+1&&t.top>=e.top-1&&t.top<=e.bottom+1}function U(t,e,n){let r=t.childNodes.length;if(r&&n.top<n.bottom)for(let o=Math.max(0,Math.min(r-1,Math.floor(r*(e.top-n.top)/(n.bottom-n.top))-2)),i=o;;){let n=t.childNodes[i];if(1==n.nodeType){let t=n.getClientRects();for(let r=0;r<t.length;r++){let o=t[r];if(K(e,o))return U(n,e,o)}}if((i=(i+1)%r)==o)break}return t}function J(t,e){let n,r=t.dom.ownerDocument,o=0,i=function(t,e,n){if(t.caretPositionFromPoint)try{let r=t.caretPositionFromPoint(e,n);if(r)return{node:r.offsetNode,offset:r.offset}}catch(t){}if(t.caretRangeFromPoint){let r=t.caretRangeFromPoint(e,n);if(r)return{node:r.startContainer,offset:r.startOffset}}}(r,e.left,e.top);i&&({node:n,offset:o}=i);let s,l=(t.root.elementFromPoint?t.root:r).elementFromPoint(e.left,e.top);if(!l||!t.dom.contains(1!=l.nodeType?l.parentNode:l)){let n=t.dom.getBoundingClientRect();if(!K(e,n))return null;if(l=U(t.dom,e,n),!l)return null}if(E)for(let t=l;n&&t;t=a(t))t.draggable&&(n=void 0);if(l=function(t,e){let n=t.parentNode;return n&&/^li$/i.test(n.nodeName)&&e.left<t.getBoundingClientRect().left?n:t}(l,e),n){if(C&&1==n.nodeType&&(o=Math.min(o,n.childNodes.length),o<n.childNodes.length)){let t,r=n.childNodes[o];"IMG"==r.nodeName&&(t=r.getBoundingClientRect()).right<=e.left&&t.bottom>e.top&&o++}let r;z&&o&&1==n.nodeType&&1==(r=n.childNodes[o-1]).nodeType&&"false"==r.contentEditable&&r.getBoundingClientRect().top>=e.top&&o--,n==t.dom&&o==n.childNodes.length-1&&1==n.lastChild.nodeType&&e.top>n.lastChild.getBoundingClientRect().bottom?s=t.state.doc.content.size:0!=o&&1==n.nodeType&&"BR"==n.childNodes[o-1].nodeName||(s=function(t,e,n,r){let o=-1;for(let n=e,i=!1;n!=t.dom;){let e=t.docView.nearestDesc(n,!0);if(!e)return null;if(1==e.dom.nodeType&&(e.node.isBlock&&e.parent||!e.contentDOM)){let t=e.dom.getBoundingClientRect();if(e.node.isBlock&&e.parent&&(!i&&t.left>r.left||t.top>r.top?o=e.posBefore:(!i&&t.right<r.left||t.bottom<r.top)&&(o=e.posAfter),i=!0),!e.contentDOM&&o<0&&!e.node.isText)return(e.node.isBlock?r.top<(t.top+t.bottom)/2:r.left<(t.left+t.right)/2)?e.posBefore:e.posAfter}n=e.dom.parentNode}return o>-1?o:t.docView.posFromDOM(e,n,-1)}(t,n,o,e))}null==s&&(s=function(t,e,n){let{node:r,offset:o}=V(e,n),i=-1;if(1==r.nodeType&&!r.firstChild){let t=r.getBoundingClientRect();i=t.left!=t.right&&n.left>(t.left+t.right)/2?1:-1}return t.docView.posFromDOM(r,o,i)}(t,l,e));let c=t.docView.nearestDesc(l,!0);return{pos:s,inside:c?c.posAtStart-c.border:-1}}function W(t){return t.top<t.bottom||t.left<t.right}function q(t,e){let n=t.getClientRects();if(n.length){let t=n[e<0?0:n.length-1];if(W(t))return t}return Array.prototype.find.call(n,W)||t.getBoundingClientRect()}const Z=/[\u0590-\u05f4\u0600-\u06ff\u0700-\u08ac]/;function G(t,e,n){let{node:r,offset:o,atom:i}=t.docView.domFromPos(e,n<0?-1:1),s=z||C;if(3==r.nodeType){if(!s||!Z.test(r.nodeValue)&&(n<0?o:o!=r.nodeValue.length)){let t=o,e=o,i=n<0?1:-1;return n<0&&!o?(e++,i=-1):n>=0&&o==r.nodeValue.length?(t--,i=1):n<0?t--:e++,Y(q(c(r,t,e),i),i<0)}{let t=q(c(r,o,o),n);if(C&&o&&/\s/.test(r.nodeValue[o-1])&&o<r.nodeValue.length){let e=q(c(r,o-1,o-1),-1);if(e.top==t.top){let n=q(c(r,o,o+1),-1);if(n.top!=t.top)return Y(n,n.left<e.left)}}return t}}if(!t.state.doc.resolve(e-(i||0)).parent.inlineContent){if(null==i&&o&&(n<0||o==p(r))){let t=r.childNodes[o-1];if(1==t.nodeType)return X(t.getBoundingClientRect(),!1)}if(null==i&&o<p(r)){let t=r.childNodes[o];if(1==t.nodeType)return X(t.getBoundingClientRect(),!0)}return X(r.getBoundingClientRect(),n>=0)}if(null==i&&o&&(n<0||o==p(r))){let t=r.childNodes[o-1],e=3==t.nodeType?c(t,p(t)-(s?0:1)):1!=t.nodeType||"BR"==t.nodeName&&t.nextSibling?null:t;if(e)return Y(q(e,1),!1)}if(null==i&&o<p(r)){let t=r.childNodes[o];for(;t.pmViewDesc&&t.pmViewDesc.ignoreForCoords;)t=t.nextSibling;let e=t?3==t.nodeType?c(t,0,s?0:1):1==t.nodeType?t:null:null;if(e)return Y(q(e,-1),!0)}return Y(q(3==r.nodeType?c(r):r,-n),n>=0)}function Y(t,e){if(0==t.width)return t;let n=e?t.left:t.right;return{top:t.top,bottom:t.bottom,left:n,right:n}}function X(t,e){if(0==t.height)return t;let n=e?t.top:t.bottom;return{top:n,bottom:n,left:t.left,right:t.right}}function Q(t,e,n){let r=t.state,o=t.root.activeElement;r!=e&&t.updateState(e),o!=t.dom&&t.focus();try{return n()}finally{r!=e&&t.updateState(r),o!=t.dom&&o&&o.focus()}}const tt=/[\u0590-\u08ac]/;let et=null,nt=null,rt=!1;class ot{constructor(t,e,n,r){this.parent=t,this.children=e,this.dom=n,this.contentDOM=r,this.dirty=0,n.pmViewDesc=this}matchesWidget(t){return!1}matchesMark(t){return!1}matchesNode(t,e,n){return!1}matchesHack(t){return!1}parseRule(){return null}stopEvent(t){return!1}get size(){let t=0;for(let e=0;e<this.children.length;e++)t+=this.children[e].size;return t}get border(){return 0}destroy(){this.parent=void 0,this.dom.pmViewDesc==this&&(this.dom.pmViewDesc=void 0);for(let t=0;t<this.children.length;t++)this.children[t].destroy()}posBeforeChild(t){for(let e=0,n=this.posAtStart;;e++){let r=this.children[e];if(r==t)return n;n+=r.size}}get posBefore(){return this.parent.posBeforeChild(this)}get posAtStart(){return this.parent?this.parent.posBeforeChild(this)+this.border:0}get posAfter(){return this.posBefore+this.size}get posAtEnd(){return this.posAtStart+this.size-2*this.border}localPosFromDOM(t,e,n){if(this.contentDOM&&this.contentDOM.contains(1==t.nodeType?t:t.parentNode)){if(n<0){let n,r;if(t==this.contentDOM)n=t.childNodes[e-1];else{for(;t.parentNode!=this.contentDOM;)t=t.parentNode;n=t.previousSibling}for(;n&&(!(r=n.pmViewDesc)||r.parent!=this);)n=n.previousSibling;return n?this.posBeforeChild(r)+r.size:this.posAtStart}{let n,r;if(t==this.contentDOM)n=t.childNodes[e];else{for(;t.parentNode!=this.contentDOM;)t=t.parentNode;n=t.nextSibling}for(;n&&(!(r=n.pmViewDesc)||r.parent!=this);)n=n.nextSibling;return n?this.posBeforeChild(r):this.posAtEnd}}let r;if(t==this.dom&&this.contentDOM)r=e>s(this.contentDOM);else if(this.contentDOM&&this.contentDOM!=this.dom&&this.dom.contains(this.contentDOM))r=2&t.compareDocumentPosition(this.contentDOM);else if(this.dom.firstChild){if(0==e)for(let e=t;;e=e.parentNode){if(e==this.dom){r=!1;break}if(e.previousSibling)break}if(null==r&&e==t.childNodes.length)for(let e=t;;e=e.parentNode){if(e==this.dom){r=!0;break}if(e.nextSibling)break}}return(null==r?n>0:r)?this.posAtEnd:this.posAtStart}nearestDesc(t,e=!1){for(let n=!0,r=t;r;r=r.parentNode){let o,i=this.getDesc(r);if(i&&(!e||i.node)){if(!n||!(o=i.nodeDOM)||(1==o.nodeType?o.contains(1==t.nodeType?t:t.parentNode):o==t))return i;n=!1}}}getDesc(t){let e=t.pmViewDesc;for(let t=e;t;t=t.parent)if(t==this)return e}posFromDOM(t,e,n){for(let r=t;r;r=r.parentNode){let o=this.getDesc(r);if(o)return o.localPosFromDOM(t,e,n)}return-1}descAt(t){for(let e=0,n=0;e<this.children.length;e++){let r=this.children[e],o=n+r.size;if(n==t&&o!=n){for(;!r.border&&r.children.length;)r=r.children[0];return r}if(t<o)return r.descAt(t-n-r.border);n=o}}domFromPos(t,e){if(!this.contentDOM)return{node:this.dom,offset:0,atom:t+1};let n=0,r=0;for(let e=0;n<this.children.length;n++){let o=this.children[n],i=e+o.size;if(i>t||o instanceof ut){r=t-e;break}e=i}if(r)return this.children[n].domFromPos(r-this.children[n].border,e);for(let t;n&&!(t=this.children[n-1]).size&&t instanceof it&&t.side>=0;n--);if(e<=0){let t,r=!0;for(;t=n?this.children[n-1]:null,t&&t.dom.parentNode!=this.contentDOM;n--,r=!1);return t&&e&&r&&!t.border&&!t.domAtom?t.domFromPos(t.size,e):{node:this.contentDOM,offset:t?s(t.dom)+1:0}}{let t,r=!0;for(;t=n<this.children.length?this.children[n]:null,t&&t.dom.parentNode!=this.contentDOM;n++,r=!1);return t&&r&&!t.border&&!t.domAtom?t.domFromPos(0,e):{node:this.contentDOM,offset:t?s(t.dom):this.contentDOM.childNodes.length}}}parseRange(t,e,n=0){if(0==this.children.length)return{node:this.contentDOM,from:t,to:e,fromOffset:0,toOffset:this.contentDOM.childNodes.length};let r=-1,o=-1;for(let i=n,a=0;;a++){let n=this.children[a],l=i+n.size;if(-1==r&&t<=l){let o=i+n.border;if(t>=o&&e<=l-n.border&&n.node&&n.contentDOM&&this.contentDOM.contains(n.contentDOM))return n.parseRange(t,e,o);t=i;for(let e=a;e>0;e--){let n=this.children[e-1];if(n.size&&n.dom.parentNode==this.contentDOM&&!n.emptyChildAt(1)){r=s(n.dom)+1;break}t-=n.size}-1==r&&(r=0)}if(r>-1&&(l>e||a==this.children.length-1)){e=l;for(let t=a+1;t<this.children.length;t++){let n=this.children[t];if(n.size&&n.dom.parentNode==this.contentDOM&&!n.emptyChildAt(-1)){o=s(n.dom);break}e+=n.size}-1==o&&(o=this.contentDOM.childNodes.length);break}i=l}return{node:this.contentDOM,from:t,to:e,fromOffset:r,toOffset:o}}emptyChildAt(t){if(this.border||!this.contentDOM||!this.children.length)return!1;let e=this.children[t<0?0:this.children.length-1];return 0==e.size||e.emptyChildAt(t)}domAfterPos(t){let{node:e,offset:n}=this.domFromPos(t,0);if(1!=e.nodeType||n==e.childNodes.length)throw new RangeError("No node after pos "+t);return e.childNodes[n]}setSelection(t,e,n,r=!1){let o=Math.min(t,e),i=Math.max(t,e);for(let s=0,a=0;s<this.children.length;s++){let l=this.children[s],c=a+l.size;if(o>a&&i<c)return l.setSelection(t-a-l.border,e-a-l.border,n,r);a=c}let a=this.domFromPos(t,t?-1:1),l=e==t?a:this.domFromPos(e,e?-1:1),c=n.getSelection(),u=!1;if((C||E)&&t==e){let{node:t,offset:e}=a;if(3==t.nodeType){if(u=!(!e||"\n"!=t.nodeValue[e-1]),u&&e==t.nodeValue.length)for(let e,n=t;n;n=n.parentNode){if(e=n.nextSibling){"BR"==e.nodeName&&(a=l={node:e.parentNode,offset:s(e)+1});break}let t=n.pmViewDesc;if(t&&t.node&&t.node.isBlock)break}}else{let n=t.childNodes[e-1];u=n&&("BR"==n.nodeName||"false"==n.contentEditable)}}if(C&&c.focusNode&&c.focusNode!=l.node&&1==c.focusNode.nodeType){let t=c.focusNode.childNodes[c.focusOffset];t&&"false"==t.contentEditable&&(r=!0)}if(!(r||u&&E)&&d(a.node,a.offset,c.anchorNode,c.anchorOffset)&&d(l.node,l.offset,c.focusNode,c.focusOffset))return;let h=!1;if((c.extend||t==e)&&!u){c.collapse(a.node,a.offset);try{t!=e&&c.extend(l.node,l.offset),h=!0}catch(t){}}if(!h){if(t>e){let t=a;a=l,l=t}let n=document.createRange();n.setEnd(l.node,l.offset),n.setStart(a.node,a.offset),c.removeAllRanges(),c.addRange(n)}}ignoreMutation(t){return!this.contentDOM&&"selection"!=t.type}get contentLost(){return this.contentDOM&&this.contentDOM!=this.dom&&!this.dom.contains(this.contentDOM)}markDirty(t,e){for(let n=0,r=0;r<this.children.length;r++){let o=this.children[r],i=n+o.size;if(n==i?t<=i&&e>=n:t<i&&e>n){let r=n+o.border,s=i-o.border;if(t>=r&&e<=s)return this.dirty=t==n||e==i?2:1,void(t!=r||e!=s||!o.contentLost&&o.dom.parentNode==this.contentDOM?o.markDirty(t-r,e-r):o.dirty=3);o.dirty=o.dom!=o.contentDOM||o.dom.parentNode!=this.contentDOM||o.children.length?3:2}n=i}this.dirty=2}markParentsDirty(){let t=1;for(let e=this.parent;e;e=e.parent,t++){let n=1==t?2:1;e.dirty<n&&(e.dirty=n)}}get domAtom(){return!1}get ignoreForCoords(){return!1}isText(t){return!1}}class it extends ot{constructor(t,e,n,r){let o,i=e.type.toDOM;if("function"==typeof i&&(i=i(n,(()=>o?o.parent?o.parent.posBeforeChild(o):void 0:r))),!e.type.spec.raw){if(1!=i.nodeType){let t=document.createElement("span");t.appendChild(i),i=t}i.contentEditable="false",i.classList.add("ProseMirror-widget")}super(t,[],i,null),this.widget=e,this.widget=e,o=this}matchesWidget(t){return 0==this.dirty&&t.type.eq(this.widget.type)}parseRule(){return{ignore:!0}}stopEvent(t){let e=this.widget.spec.stopEvent;return!!e&&e(t)}ignoreMutation(t){return"selection"!=t.type||this.widget.spec.ignoreSelection}destroy(){this.widget.type.destroy(this.dom),super.destroy()}get domAtom(){return!0}get side(){return this.widget.type.side}}class st extends ot{constructor(t,e,n,r){super(t,[],e,null),this.textDOM=n,this.text=r}get size(){return this.text.length}localPosFromDOM(t,e){return t!=this.textDOM?this.posAtStart+(e?this.size:0):this.posAtStart+e}domFromPos(t){return{node:this.textDOM,offset:t}}ignoreMutation(t){return"characterData"===t.type&&t.target.nodeValue==t.oldValue}}class at extends ot{constructor(t,e,n,r){super(t,[],n,r),this.mark=e}static create(t,e,n,r){let i=r.nodeViews[e.type.name],s=i&&i(e,r,n);return s&&s.dom||(s=o.ZF.renderSpec(document,e.type.spec.toDOM(e,n))),new at(t,e,s.dom,s.contentDOM||s.dom)}parseRule(){return 3&this.dirty||this.mark.type.spec.reparseInView?null:{mark:this.mark.type.name,attrs:this.mark.attrs,contentElement:this.contentDOM}}matchesMark(t){return 3!=this.dirty&&this.mark.eq(t)}markDirty(t,e){if(super.markDirty(t,e),0!=this.dirty){let t=this.parent;for(;!t.node;)t=t.parent;t.dirty<this.dirty&&(t.dirty=this.dirty),this.dirty=0}}slice(t,e,n){let r=at.create(this.parent,this.mark,!0,n),o=this.children,i=this.size;e<i&&(o=Mt(o,e,i,n)),t>0&&(o=Mt(o,0,t,n));for(let t=0;t<o.length;t++)o[t].parent=r;return r.children=o,r}}class lt extends ot{constructor(t,e,n,r,o,i,s,a,l){super(t,[],o,i),this.node=e,this.outerDeco=n,this.innerDeco=r,this.nodeDOM=s}static create(t,e,n,r,i,s){let a,l=i.nodeViews[e.type.name],c=l&&l(e,i,(()=>a?a.parent?a.parent.posBeforeChild(a):void 0:s),n,r),d=c&&c.dom,u=c&&c.contentDOM;if(e.isText)if(d){if(3!=d.nodeType)throw new RangeError("Text must be rendered as a DOM text node")}else d=document.createTextNode(e.text);else d||({dom:d,contentDOM:u}=o.ZF.renderSpec(document,e.type.spec.toDOM(e)));u||e.isText||"BR"==d.nodeName||(d.hasAttribute("contenteditable")||(d.contentEditable="false"),e.type.spec.draggable&&(d.draggable=!0));let h=d;return d=bt(d,n,e),c?a=new ht(t,e,n,r,d,u||null,h,c,i,s+1):e.isText?new dt(t,e,n,r,d,h,i):new lt(t,e,n,r,d,u||null,h,i,s+1)}parseRule(){if(this.node.type.spec.reparseInView)return null;let t={node:this.node.type.name,attrs:this.node.attrs};if("pre"==this.node.type.whitespace&&(t.preserveWhitespace="full"),this.contentDOM)if(this.contentLost){for(let e=this.children.length-1;e>=0;e--){let n=this.children[e];if(this.dom.contains(n.dom.parentNode)){t.contentElement=n.dom.parentNode;break}}t.contentElement||(t.getContent=()=>o.FK.empty)}else t.contentElement=this.contentDOM;else t.getContent=()=>this.node.content;return t}matchesNode(t,e,n){return 0==this.dirty&&t.eq(this.node)&&wt(e,this.outerDeco)&&n.eq(this.innerDeco)}get size(){return this.node.nodeSize}get border(){return this.node.isLeaf?0:1}updateChildren(t,e){let n=this.node.inlineContent,r=e,i=t.composing?this.localCompositionInfo(t,e):null,s=i&&i.pos>-1?i:null,a=i&&i.pos<0,l=new kt(this,s&&s.node,t);!function(t,e,n,r){let o=e.locals(t),i=0;if(0==o.length){for(let n=0;n<t.childCount;n++){let s=t.child(n);r(s,o,e.forChild(i,s),n),i+=s.nodeSize}return}let s=0,a=[],l=null;for(let c=0;;){let d,u,h,p;for(;s<o.length&&o[s].to==i;){let t=o[s++];t.widget&&(d?(u||(u=[d])).push(t):d=t)}if(d)if(u){u.sort(St);for(let t=0;t<u.length;t++)n(u[t],c,!!l)}else n(d,c,!!l);if(l)p=-1,h=l,l=null;else{if(!(c<t.childCount))break;p=c,h=t.child(c++)}for(let t=0;t<a.length;t++)a[t].to<=i&&a.splice(t--,1);for(;s<o.length&&o[s].from<=i&&o[s].to>i;)a.push(o[s++]);let f=i+h.nodeSize;if(h.isText){let t=f;s<o.length&&o[s].from<t&&(t=o[s].from);for(let e=0;e<a.length;e++)a[e].to<t&&(t=a[e].to);t<f&&(l=h.cut(t-i),h=h.cut(0,t-i),f=t,p=-1)}else for(;s<o.length&&o[s].to<f;)s++;r(h,h.isInline&&!h.isLeaf?a.filter((t=>!t.inline)):a.slice(),e.forChild(i,h),p),i=f}}(this.node,this.innerDeco,((e,i,s)=>{e.spec.marks?l.syncToMarks(e.spec.marks,n,t):e.type.side>=0&&!s&&l.syncToMarks(i==this.node.childCount?o.CU.none:this.node.child(i).marks,n,t),l.placeWidget(e,t,r)}),((e,o,s,c)=>{let d;l.syncToMarks(e.marks,n,t),l.findNodeMatch(e,o,s,c)||a&&t.state.selection.from>r&&t.state.selection.to<r+e.nodeSize&&(d=l.findIndexWithChild(i.node))>-1&&l.updateNodeAt(e,o,s,d,t)||l.updateNextNode(e,o,s,t,c,r)||l.addNode(e,o,s,t,r),r+=e.nodeSize})),l.syncToMarks([],n,t),this.node.isTextblock&&l.addTextblockHacks(),l.destroyRest(),(l.changed||2==this.dirty)&&(s&&this.protectLocalComposition(t,s),pt(this.contentDOM,this.children,t),_&&function(t){if("UL"==t.nodeName||"OL"==t.nodeName){let e=t.style.cssText;t.style.cssText=e+"; list-style: square !important",window.getComputedStyle(t).listStyle,t.style.cssText=e}}(this.dom))}localCompositionInfo(t,e){let{from:n,to:o}=t.state.selection;if(!(t.state.selection instanceof r.U3)||n<e||o>e+this.node.content.size)return null;let i=t.input.compositionNode;if(!i||!this.dom.contains(i.parentNode))return null;if(this.node.inlineContent){let t=i.nodeValue,r=function(t,e,n,r){for(let o=0,i=0;o<t.childCount&&i<=r;){let s=t.child(o++),a=i;if(i+=s.nodeSize,!s.isText)continue;let l=s.text;for(;o<t.childCount;){let e=t.child(o++);if(i+=e.nodeSize,!e.isText)break;l+=e.text}if(i>=n){if(i>=r&&l.slice(r-e.length-a,r-a)==e)return r-e.length;let t=a<r?l.lastIndexOf(e,r-a-1):-1;if(t>=0&&t+e.length+a>=n)return a+t;if(n==r&&l.length>=r+e.length-a&&l.slice(r-a,r-a+e.length)==e)return r}}return-1}(this.node.content,t,n-e,o-e);return r<0?null:{node:i,pos:r,text:t}}return{node:i,pos:-1,text:""}}protectLocalComposition(t,{node:e,pos:n,text:r}){if(this.getDesc(e))return;let o=e;for(;o.parentNode!=this.contentDOM;o=o.parentNode){for(;o.previousSibling;)o.parentNode.removeChild(o.previousSibling);for(;o.nextSibling;)o.parentNode.removeChild(o.nextSibling);o.pmViewDesc&&(o.pmViewDesc=void 0)}let i=new st(this,o,e,r);t.input.compositionNodes.push(i),this.children=Mt(this.children,n,n+r.length,t,i)}update(t,e,n,r){return!(3==this.dirty||!t.sameMarkup(this.node)||(this.updateInner(t,e,n,r),0))}updateInner(t,e,n,r){this.updateOuterDeco(e),this.node=t,this.innerDeco=n,this.contentDOM&&this.updateChildren(r,this.posAtStart),this.dirty=0}updateOuterDeco(t){if(wt(t,this.outerDeco))return;let e=1!=this.nodeDOM.nodeType,n=this.dom;this.dom=yt(this.dom,this.nodeDOM,gt(this.outerDeco,this.node,e),gt(t,this.node,e)),this.dom!=n&&(n.pmViewDesc=void 0,this.dom.pmViewDesc=this),this.outerDeco=t}selectNode(){1==this.nodeDOM.nodeType&&this.nodeDOM.classList.add("ProseMirror-selectednode"),!this.contentDOM&&this.node.type.spec.draggable||(this.dom.draggable=!0)}deselectNode(){1==this.nodeDOM.nodeType&&(this.nodeDOM.classList.remove("ProseMirror-selectednode"),!this.contentDOM&&this.node.type.spec.draggable||this.dom.removeAttribute("draggable"))}get domAtom(){return this.node.isAtom}}function ct(t,e,n,r,o){bt(r,e,t);let i=new lt(void 0,t,e,n,r,r,r,o,0);return i.contentDOM&&i.updateChildren(o,0),i}class dt extends lt{constructor(t,e,n,r,o,i,s){super(t,e,n,r,o,null,i,s,0)}parseRule(){let t=this.nodeDOM.parentNode;for(;t&&t!=this.dom&&!t.pmIsDeco;)t=t.parentNode;return{skip:t||!0}}update(t,e,n,r){return!(3==this.dirty||0!=this.dirty&&!this.inParent()||!t.sameMarkup(this.node)||(this.updateOuterDeco(e),0==this.dirty&&t.text==this.node.text||t.text==this.nodeDOM.nodeValue||(this.nodeDOM.nodeValue=t.text,r.trackWrites==this.nodeDOM&&(r.trackWrites=null)),this.node=t,this.dirty=0,0))}inParent(){let t=this.parent.contentDOM;for(let e=this.nodeDOM;e;e=e.parentNode)if(e==t)return!0;return!1}domFromPos(t){return{node:this.nodeDOM,offset:t}}localPosFromDOM(t,e,n){return t==this.nodeDOM?this.posAtStart+Math.min(e,this.node.text.length):super.localPosFromDOM(t,e,n)}ignoreMutation(t){return"characterData"!=t.type&&"selection"!=t.type}slice(t,e,n){let r=this.node.cut(t,e),o=document.createTextNode(r.text);return new dt(this.parent,r,this.outerDeco,this.innerDeco,o,o,n)}markDirty(t,e){super.markDirty(t,e),this.dom==this.nodeDOM||0!=t&&e!=this.nodeDOM.nodeValue.length||(this.dirty=3)}get domAtom(){return!1}isText(t){return this.node.text==t}}class ut extends ot{parseRule(){return{ignore:!0}}matchesHack(t){return 0==this.dirty&&this.dom.nodeName==t}get domAtom(){return!0}get ignoreForCoords(){return"IMG"==this.dom.nodeName}}class ht extends lt{constructor(t,e,n,r,o,i,s,a,l,c){super(t,e,n,r,o,i,s,l,c),this.spec=a}update(t,e,n,r){if(3==this.dirty)return!1;if(this.spec.update){let o=this.spec.update(t,e,n);return o&&this.updateInner(t,e,n,r),o}return!(!this.contentDOM&&!t.isLeaf)&&super.update(t,e,n,r)}selectNode(){this.spec.selectNode?this.spec.selectNode():super.selectNode()}deselectNode(){this.spec.deselectNode?this.spec.deselectNode():super.deselectNode()}setSelection(t,e,n,r){this.spec.setSelection?this.spec.setSelection(t,e,n):super.setSelection(t,e,n,r)}destroy(){this.spec.destroy&&this.spec.destroy(),super.destroy()}stopEvent(t){return!!this.spec.stopEvent&&this.spec.stopEvent(t)}ignoreMutation(t){return this.spec.ignoreMutation?this.spec.ignoreMutation(t):super.ignoreMutation(t)}}function pt(t,e,n){let r=t.firstChild,o=!1;for(let i=0;i<e.length;i++){let s=e[i],a=s.dom;if(a.parentNode==t){for(;a!=r;)r=xt(r),o=!0;r=r.nextSibling}else o=!0,t.insertBefore(a,r);if(s instanceof at){let e=r?r.previousSibling:t.lastChild;pt(s.contentDOM,s.children,n),r=e?e.nextSibling:t.firstChild}}for(;r;)r=xt(r),o=!0;o&&n.trackWrites==t&&(n.trackWrites=null)}const ft=function(t){t&&(this.nodeName=t)};ft.prototype=Object.create(null);const mt=[new ft];function gt(t,e,n){if(0==t.length)return mt;let r=n?mt[0]:new ft,o=[r];for(let i=0;i<t.length;i++){let s=t[i].type.attrs;if(s){s.nodeName&&o.push(r=new ft(s.nodeName));for(let t in s){let i=s[t];null!=i&&(n&&1==o.length&&o.push(r=new ft(e.isInline?"span":"div")),"class"==t?r.class=(r.class?r.class+" ":"")+i:"style"==t?r.style=(r.style?r.style+";":"")+i:"nodeName"!=t&&(r[t]=i))}}}return o}function yt(t,e,n,r){if(n==mt&&r==mt)return e;let o=e;for(let e=0;e<r.length;e++){let i=r[e],s=n[e];if(e){let e;s&&s.nodeName==i.nodeName&&o!=t&&(e=o.parentNode)&&e.nodeName.toLowerCase()==i.nodeName||(e=document.createElement(i.nodeName),e.pmIsDeco=!0,e.appendChild(o),s=mt[0]),o=e}vt(o,s||mt[0],i)}return o}function vt(t,e,n){for(let r in e)"class"==r||"style"==r||"nodeName"==r||r in n||t.removeAttribute(r);for(let r in n)"class"!=r&&"style"!=r&&"nodeName"!=r&&n[r]!=e[r]&&t.setAttribute(r,n[r]);if(e.class!=n.class){let r=e.class?e.class.split(" ").filter(Boolean):[],o=n.class?n.class.split(" ").filter(Boolean):[];for(let e=0;e<r.length;e++)-1==o.indexOf(r[e])&&t.classList.remove(r[e]);for(let e=0;e<o.length;e++)-1==r.indexOf(o[e])&&t.classList.add(o[e]);0==t.classList.length&&t.removeAttribute("class")}if(e.style!=n.style){if(e.style){let n,r=/\s*([\w\-\xa1-\uffff]+)\s*:(?:"(?:\\.|[^"])*"|'(?:\\.|[^'])*'|\(.*?\)|[^;])*/g;for(;n=r.exec(e.style);)t.style.removeProperty(n[1])}n.style&&(t.style.cssText+=n.style)}}function bt(t,e,n){return yt(t,t,mt,gt(e,n,1!=t.nodeType))}function wt(t,e){if(t.length!=e.length)return!1;for(let n=0;n<t.length;n++)if(!t[n].type.eq(e[n].type))return!1;return!0}function xt(t){let e=t.nextSibling;return t.parentNode.removeChild(t),e}class kt{constructor(t,e,n){this.lock=e,this.view=n,this.index=0,this.stack=[],this.changed=!1,this.top=t,this.preMatch=function(t,e){let n=e,r=n.children.length,o=t.childCount,i=new Map,s=[];t:for(;o>0;){let a;for(;;)if(r){let t=n.children[r-1];if(!(t instanceof at)){a=t,r--;break}n=t,r=t.children.length}else{if(n==e)break t;r=n.parent.children.indexOf(n),n=n.parent}let l=a.node;if(l){if(l!=t.child(o-1))break;--o,i.set(a,o),s.push(a)}}return{index:o,matched:i,matches:s.reverse()}}(t.node.content,t)}destroyBetween(t,e){if(t!=e){for(let n=t;n<e;n++)this.top.children[n].destroy();this.top.children.splice(t,e-t),this.changed=!0}}destroyRest(){this.destroyBetween(this.index,this.top.children.length)}syncToMarks(t,e,n){let r=0,o=this.stack.length>>1,i=Math.min(o,t.length);for(;r<i&&(r==o-1?this.top:this.stack[r+1<<1]).matchesMark(t[r])&&!1!==t[r].type.spec.spanning;)r++;for(;r<o;)this.destroyRest(),this.top.dirty=0,this.index=this.stack.pop(),this.top=this.stack.pop(),o--;for(;o<t.length;){this.stack.push(this.top,this.index+1);let r=-1;for(let e=this.index;e<Math.min(this.index+3,this.top.children.length);e++){let n=this.top.children[e];if(n.matchesMark(t[o])&&!this.isLocked(n.dom)){r=e;break}}if(r>-1)r>this.index&&(this.changed=!0,this.destroyBetween(this.index,r)),this.top=this.top.children[this.index];else{let r=at.create(this.top,t[o],e,n);this.top.children.splice(this.index,0,r),this.top=r,this.changed=!0}this.index=0,o++}}findNodeMatch(t,e,n,r){let o,i=-1;if(r>=this.preMatch.index&&(o=this.preMatch.matches[r-this.preMatch.index]).parent==this.top&&o.matchesNode(t,e,n))i=this.top.children.indexOf(o,this.index);else for(let r=this.index,o=Math.min(this.top.children.length,r+5);r<o;r++){let o=this.top.children[r];if(o.matchesNode(t,e,n)&&!this.preMatch.matched.has(o)){i=r;break}}return!(i<0||(this.destroyBetween(this.index,i),this.index++,0))}updateNodeAt(t,e,n,r,o){let i=this.top.children[r];return 3==i.dirty&&i.dom==i.contentDOM&&(i.dirty=2),!!i.update(t,e,n,o)&&(this.destroyBetween(this.index,r),this.index++,!0)}findIndexWithChild(t){for(;;){let e=t.parentNode;if(!e)return-1;if(e==this.top.contentDOM){let e=t.pmViewDesc;if(e)for(let t=this.index;t<this.top.children.length;t++)if(this.top.children[t]==e)return t;return-1}t=e}}updateNextNode(t,e,n,r,o,i){for(let s=this.index;s<this.top.children.length;s++){let a=this.top.children[s];if(a instanceof lt){let l=this.preMatch.matched.get(a);if(null!=l&&l!=o)return!1;let c,d=a.dom,u=this.isLocked(d)&&!(t.isText&&a.node&&a.node.isText&&a.nodeDOM.nodeValue==t.text&&3!=a.dirty&&wt(e,a.outerDeco));if(!u&&a.update(t,e,n,r))return this.destroyBetween(this.index,s),a.dom!=d&&(this.changed=!0),this.index++,!0;if(!u&&(c=this.recreateWrapper(a,t,e,n,r,i)))return this.top.children[this.index]=c,c.contentDOM&&(c.dirty=2,c.updateChildren(r,i+1),c.dirty=0),this.changed=!0,this.index++,!0;break}}return!1}recreateWrapper(t,e,n,r,o,i){if(t.dirty||e.isAtom||!t.children.length||!t.node.content.eq(e.content))return null;let s=lt.create(this.top,e,n,r,o,i);if(s.contentDOM){s.children=t.children,t.children=[];for(let t of s.children)t.parent=s}return t.destroy(),s}addNode(t,e,n,r,o){let i=lt.create(this.top,t,e,n,r,o);i.contentDOM&&i.updateChildren(r,o+1),this.top.children.splice(this.index++,0,i),this.changed=!0}placeWidget(t,e,n){let r=this.index<this.top.children.length?this.top.children[this.index]:null;if(!r||!r.matchesWidget(t)||t!=r.widget&&r.widget.type.toDOM.parentNode){let r=new it(this.top,t,e,n);this.top.children.splice(this.index++,0,r),this.changed=!0}else this.index++}addTextblockHacks(){let t=this.top.children[this.index-1],e=this.top;for(;t instanceof at;)e=t,t=e.children[e.children.length-1];(!t||!(t instanceof dt)||/\n$/.test(t.node.text)||this.view.requiresGeckoHackNode&&/\s$/.test(t.node.text))&&((E||T)&&t&&"false"==t.dom.contentEditable&&this.addHackNode("IMG",e),this.addHackNode("BR",this.top))}addHackNode(t,e){if(e==this.top&&this.index<e.children.length&&e.children[this.index].matchesHack(t))this.index++;else{let n=document.createElement(t);"IMG"==t&&(n.className="ProseMirror-separator",n.alt=""),"BR"==t&&(n.className="ProseMirror-trailingBreak");let r=new ut(this.top,[],n,null);e!=this.top?e.children.push(r):e.children.splice(this.index++,0,r),this.changed=!0}}isLocked(t){return this.lock&&(t==this.lock||1==t.nodeType&&t.contains(this.lock.parentNode))}}function St(t,e){return t.type.side-e.type.side}function Mt(t,e,n,r,o){let i=[];for(let s=0,a=0;s<t.length;s++){let l=t[s],c=a,d=a+=l.size;c>=n||d<=e?i.push(l):(c<e&&i.push(l.slice(0,e-c,r)),o&&(i.push(o),o=void 0),d>n&&i.push(l.slice(n-c,l.size,r)))}return i}function Ct(t,e=null){let n=t.domSelectionRange(),o=t.state.doc;if(!n.focusNode)return null;let i=t.docView.nearestDesc(n.focusNode),a=i&&0==i.size,l=t.docView.posFromDOM(n.focusNode,n.focusOffset,1);if(l<0)return null;let c,d,u=o.resolve(l);if(m(n)){for(c=u;i&&!i.node;)i=i.parent;let t=i.node;if(i&&t.isAtom&&r.nh.isSelectable(t)&&i.parent&&(!t.isInline||!function(t,e,n){for(let r=0==e,o=e==p(t);r||o;){if(t==n)return!0;let e=s(t);if(!(t=t.parentNode))return!1;r=r&&0==e,o=o&&e==p(t)}}(n.focusNode,n.focusOffset,i.dom))){let t=i.posBefore;d=new r.nh(l==t?u:o.resolve(t))}}else{let e=t.docView.posFromDOM(n.anchorNode,n.anchorOffset,1);if(e<0)return null;c=o.resolve(e)}return d||(d=zt(t,c,u,"pointer"==e||t.state.selection.head<u.pos&&!a?1:-1)),d}function At(t){return t.editable?t.hasFocus():Dt(t)&&document.activeElement&&document.activeElement.contains(t.dom)}function Tt(t,e=!1){let n=t.state.selection;if($t(t,n),At(t)){if(!e&&t.input.mouseDown&&t.input.mouseDown.allowDefault&&T){let e=t.domSelectionRange(),n=t.domObserver.currentSelection;if(e.anchorNode&&n.anchorNode&&d(e.anchorNode,e.anchorOffset,n.anchorNode,n.anchorOffset))return t.input.mouseDown.delayedSelectionSync=!0,void t.domObserver.setCurSelection()}if(t.domObserver.disconnectSelection(),t.cursorWrapper)!function(t){let e=t.domSelection(),n=document.createRange(),r=t.cursorWrapper.dom,o="IMG"==r.nodeName;o?n.setEnd(r.parentNode,s(r)+1):n.setEnd(r,0),n.collapse(!1),e.removeAllRanges(),e.addRange(n),!o&&!t.state.selection.visible&&S&&M<=11&&(r.disabled=!0,r.disabled=!1)}(t);else{let o,i,{anchor:s,head:a}=n;!Ot||n instanceof r.U3||(n.$from.parent.inlineContent||(o=Et(t,n.from)),n.empty||n.$from.parent.inlineContent||(i=Et(t,n.to))),t.docView.setSelection(s,a,t.root,e),Ot&&(o&&Nt(o),i&&Nt(i)),n.visible?t.dom.classList.remove("ProseMirror-hideselection"):(t.dom.classList.add("ProseMirror-hideselection"),"onselectionchange"in document&&function(t){let e=t.dom.ownerDocument;e.removeEventListener("selectionchange",t.input.hideSelectionGuard);let n=t.domSelectionRange(),r=n.anchorNode,o=n.anchorOffset;e.addEventListener("selectionchange",t.input.hideSelectionGuard=()=>{n.anchorNode==r&&n.anchorOffset==o||(e.removeEventListener("selectionchange",t.input.hideSelectionGuard),setTimeout((()=>{At(t)&&!t.state.selection.visible||t.dom.classList.remove("ProseMirror-hideselection")}),20))})}(t))}t.domObserver.setCurSelection(),t.domObserver.connectSelection()}}const Ot=E||T&&O<63;function Et(t,e){let{node:n,offset:r}=t.docView.domFromPos(e,0),o=r<n.childNodes.length?n.childNodes[r]:null,i=r?n.childNodes[r-1]:null;if(E&&o&&"false"==o.contentEditable)return _t(o);if(!(o&&"false"!=o.contentEditable||i&&"false"!=i.contentEditable)){if(o)return _t(o);if(i)return _t(i)}}function _t(t){return t.contentEditable="true",E&&t.draggable&&(t.draggable=!1,t.wasDraggable=!0),t}function Nt(t){t.contentEditable="false",t.wasDraggable&&(t.draggable=!0,t.wasDraggable=null)}function $t(t,e){if(e instanceof r.nh){let n=t.docView.descAt(e.from);n!=t.lastSelectedViewDesc&&(Rt(t),n&&n.selectNode(),t.lastSelectedViewDesc=n)}else Rt(t)}function Rt(t){t.lastSelectedViewDesc&&(t.lastSelectedViewDesc.parent&&t.lastSelectedViewDesc.deselectNode(),t.lastSelectedViewDesc=void 0)}function zt(t,e,n,o){return t.someProp("createSelectionBetween",(r=>r(t,e,n)))||r.U3.between(e,n,o)}function Pt(t){return!(t.editable&&!t.hasFocus())&&Dt(t)}function Dt(t){let e=t.domSelectionRange();if(!e.anchorNode)return!1;try{return t.dom.contains(3==e.anchorNode.nodeType?e.anchorNode.parentNode:e.anchorNode)&&(t.editable||t.dom.contains(3==e.focusNode.nodeType?e.focusNode.parentNode:e.focusNode))}catch(t){return!1}}function Lt(t,e){let{$anchor:n,$head:o}=t.selection,i=e>0?n.max(o):n.min(o),s=i.parent.inlineContent?i.depth?t.doc.resolve(e>0?i.after():i.before()):null:i;return s&&r.LN.findFrom(s,e)}function It(t,e){return t.dispatch(t.state.tr.setSelection(e).scrollIntoView()),!0}function Ft(t,e,n){let o=t.state.selection;if(!(o instanceof r.U3)){if(o instanceof r.nh&&o.node.isInline)return It(t,new r.U3(e>0?o.$to:o.$from));{let n=Lt(t.state,e);return!!n&&It(t,n)}}if(n.indexOf("s")>-1){let{$head:n}=o,i=n.textOffset?null:e<0?n.nodeBefore:n.nodeAfter;if(!i||i.isText||!i.isLeaf)return!1;let s=t.state.doc.resolve(n.pos+i.nodeSize*(e<0?-1:1));return It(t,new r.U3(o.$anchor,s))}if(!o.empty)return!1;if(t.endOfTextblock(e>0?"forward":"backward")){let n=Lt(t.state,e);return!!(n&&n instanceof r.nh)&&It(t,n)}if(!(N&&n.indexOf("m")>-1)){let n,i=o.$head,s=i.textOffset?null:e<0?i.nodeBefore:i.nodeAfter;if(!s||s.isText)return!1;let a=e<0?i.pos-s.nodeSize:i.pos;return!!(s.isAtom||(n=t.docView.descAt(a))&&!n.contentDOM)&&(r.nh.isSelectable(s)?It(t,new r.nh(e<0?t.state.doc.resolve(i.pos-s.nodeSize):i)):!!z&&It(t,new r.U3(t.state.doc.resolve(e<0?a:a+s.nodeSize))))}}function jt(t){return 3==t.nodeType?t.nodeValue.length:t.childNodes.length}function Bt(t,e){let n=t.pmViewDesc;return n&&0==n.size&&(e<0||t.nextSibling||"BR"!=t.nodeName)}function Ht(t,e){return e<0?function(t){let e=t.domSelectionRange(),n=e.focusNode,r=e.focusOffset;if(!n)return;let o,i,a=!1;for(C&&1==n.nodeType&&r<jt(n)&&Bt(n.childNodes[r],-1)&&(a=!0);;)if(r>0){if(1!=n.nodeType)break;{let t=n.childNodes[r-1];if(Bt(t,-1))o=n,i=--r;else{if(3!=t.nodeType)break;n=t,r=n.nodeValue.length}}}else{if(Vt(n))break;{let e=n.previousSibling;for(;e&&Bt(e,-1);)o=n.parentNode,i=s(e),e=e.previousSibling;if(e)n=e,r=jt(n);else{if(n=n.parentNode,n==t.dom)break;r=0}}}a?Kt(t,n,r):o&&Kt(t,o,i)}(t):function(t){let e=t.domSelectionRange(),n=e.focusNode,r=e.focusOffset;if(!n)return;let o,i,a=jt(n);for(;;)if(r<a){if(1!=n.nodeType)break;if(!Bt(n.childNodes[r],1))break;o=n,i=++r}else{if(Vt(n))break;{let e=n.nextSibling;for(;e&&Bt(e,1);)o=e.parentNode,i=s(e)+1,e=e.nextSibling;if(e)n=e,r=0,a=jt(n);else{if(n=n.parentNode,n==t.dom)break;r=a=0}}}o&&Kt(t,o,i)}(t)}function Vt(t){let e=t.pmViewDesc;return e&&e.node&&e.node.isBlock}function Kt(t,e,n){if(3!=e.nodeType){let t,r;(r=function(t,e){for(;t&&e==t.childNodes.length&&!f(t);)e=s(t)+1,t=t.parentNode;for(;t&&e<t.childNodes.length;){let n=t.childNodes[e];if(3==n.nodeType)return n;if(1==n.nodeType&&"false"==n.contentEditable)break;t=n,e=0}}(e,n))?(e=r,n=0):(t=function(t,e){for(;t&&!e&&!f(t);)e=s(t),t=t.parentNode;for(;t&&e;){let n=t.childNodes[e-1];if(3==n.nodeType)return n;if(1==n.nodeType&&"false"==n.contentEditable)break;e=(t=n).childNodes.length}}(e,n))&&(e=t,n=t.nodeValue.length)}let r=t.domSelection();if(m(r)){let t=document.createRange();t.setEnd(e,n),t.setStart(e,n),r.removeAllRanges(),r.addRange(t)}else r.extend&&r.extend(e,n);t.domObserver.setCurSelection();let{state:o}=t;setTimeout((()=>{t.state==o&&Tt(t)}),50)}function Ut(t,e){let n=t.state.doc.resolve(e);if(!T&&!$&&n.parent.inlineContent){let r=t.coordsAtPos(e);if(e>n.start()){let n=t.coordsAtPos(e-1),o=(n.top+n.bottom)/2;if(o>r.top&&o<r.bottom&&Math.abs(n.left-r.left)>1)return n.left<r.left?"ltr":"rtl"}if(e<n.end()){let n=t.coordsAtPos(e+1),o=(n.top+n.bottom)/2;if(o>r.top&&o<r.bottom&&Math.abs(n.left-r.left)>1)return n.left>r.left?"ltr":"rtl"}}return"rtl"==getComputedStyle(t.dom).direction?"rtl":"ltr"}function Jt(t,e,n){let o=t.state.selection;if(o instanceof r.U3&&!o.empty||n.indexOf("s")>-1)return!1;if(N&&n.indexOf("m")>-1)return!1;let{$from:i,$to:s}=o;if(!i.parent.inlineContent||t.endOfTextblock(e<0?"up":"down")){let n=Lt(t.state,e);if(n&&n instanceof r.nh)return It(t,n)}if(!i.parent.inlineContent){let n=e<0?i:s,a=o instanceof r.i5?r.LN.near(n,e):r.LN.findFrom(n,e);return!!a&&It(t,a)}return!1}function Wt(t,e){if(!(t.state.selection instanceof r.U3))return!0;let{$head:n,$anchor:o,empty:i}=t.state.selection;if(!n.sameParent(o))return!0;if(!i)return!1;if(t.endOfTextblock(e>0?"forward":"backward"))return!0;let s=!n.textOffset&&(e<0?n.nodeBefore:n.nodeAfter);if(s&&!s.isText){let r=t.state.tr;return e<0?r.delete(n.pos-s.nodeSize,n.pos):r.delete(n.pos,n.pos+s.nodeSize),t.dispatch(r),!0}return!1}function qt(t,e,n){t.domObserver.stop(),e.contentEditable=n,t.domObserver.start()}function Zt(t,e){t.someProp("transformCopied",(n=>{e=n(e,t)}));let n=[],{content:r,openStart:i,openEnd:s}=e;for(;i>1&&s>1&&1==r.childCount&&1==r.firstChild.childCount;){i--,s--;let t=r.firstChild;n.push(t.type.name,t.attrs!=t.type.defaultAttrs?t.attrs:null),r=t.content}let a=t.someProp("clipboardSerializer")||o.ZF.fromSchema(t.state.schema),l=ie(),c=l.createElement("div");c.appendChild(a.serializeFragment(r,{document:l}));let d,u=c.firstChild,h=0;for(;u&&1==u.nodeType&&(d=re[u.nodeName.toLowerCase()]);){for(let t=d.length-1;t>=0;t--){let e=l.createElement(d[t]);for(;c.firstChild;)e.appendChild(c.firstChild);c.appendChild(e),h++}u=c.firstChild}return u&&1==u.nodeType&&u.setAttribute("data-pm-slice",`${i} ${s}${h?` -${h}`:""} ${JSON.stringify(n)}`),{dom:c,text:t.someProp("clipboardTextSerializer",(n=>n(e,t)))||e.content.textBetween(0,e.content.size,"\n\n"),slice:e}}function Gt(t,e,n,r,i){let s,a,l=i.parent.type.spec.code;if(!n&&!e)return null;let c=e&&(r||l||!n);if(c){if(t.someProp("transformPastedText",(n=>{e=n(e,l||r,t)})),l)return e?new o.Ji(o.FK.from(t.state.schema.text(e.replace(/\r\n?/g,"\n"))),0,0):o.Ji.empty;let n=t.someProp("clipboardTextParser",(n=>n(e,i,r,t)));if(n)a=n;else{let n=i.marks(),{schema:r}=t.state,a=o.ZF.fromSchema(r);s=document.createElement("div"),e.split(/(?:\r\n?|\n)+/).forEach((t=>{let e=s.appendChild(document.createElement("p"));t&&e.appendChild(a.serializeNode(r.text(t,n)))}))}}else t.someProp("transformPastedHTML",(e=>{n=e(n,t)})),s=function(t){let e=/^(\s*<meta [^>]*>)*/.exec(t);e&&(t=t.slice(e[0].length));let n,r=ie().createElement("div"),o=/<([a-z][^>\s]+)/i.exec(t);if((n=o&&re[o[1].toLowerCase()])&&(t=n.map((t=>"<"+t+">")).join("")+t+n.map((t=>"</"+t+">")).reverse().join("")),r.innerHTML=t,n)for(let t=0;t<n.length;t++)r=r.querySelector(n[t])||r;return r}(n),z&&function(t){let e=t.querySelectorAll(T?"span:not([class]):not([style])":"span.Apple-converted-space");for(let n=0;n<e.length;n++){let r=e[n];1==r.childNodes.length&&" "==r.textContent&&r.parentNode&&r.parentNode.replaceChild(t.ownerDocument.createTextNode(" "),r)}}(s);let d=s&&s.querySelector("[data-pm-slice]"),u=d&&/^(\d+) (\d+)(?: -(\d+))? (.*)/.exec(d.getAttribute("data-pm-slice")||"");if(u&&u[3])for(let t=+u[3];t>0;t--){let t=s.firstChild;for(;t&&1!=t.nodeType;)t=t.nextSibling;if(!t)break;s=t}if(!a){let e=t.someProp("clipboardParser")||t.someProp("domParser")||o.S4.fromSchema(t.state.schema);a=e.parseSlice(s,{preserveWhitespace:!(!c&&!u),context:i,ruleFromNode:t=>"BR"!=t.nodeName||t.nextSibling||!t.parentNode||Yt.test(t.parentNode.nodeName)?null:{ignore:!0}})}if(u)a=function(t,e){if(!t.size)return t;let n,r=t.content.firstChild.type.schema;try{n=JSON.parse(e)}catch(e){return t}let{content:i,openStart:s,openEnd:a}=t;for(let t=n.length-2;t>=0;t-=2){let e=r.nodes[n[t]];if(!e||e.hasRequiredAttrs())break;i=o.FK.from(e.create(n[t+1],i)),s++,a++}return new o.Ji(i,s,a)}(ne(a,+u[1],+u[2]),u[4]);else if(a=o.Ji.maxOpen(function(t,e){if(t.childCount<2)return t;for(let n=e.depth;n>=0;n--){let r,i=e.node(n).contentMatchAt(e.index(n)),s=[];if(t.forEach((t=>{if(!s)return;let e,n=i.findWrapping(t.type);if(!n)return s=null;if(e=s.length&&r.length&&Qt(n,r,t,s[s.length-1],0))s[s.length-1]=e;else{s.length&&(s[s.length-1]=te(s[s.length-1],r.length));let e=Xt(t,n);s.push(e),i=i.matchType(e.type),r=n}})),s)return o.FK.from(s)}return t}(a.content,i),!0),a.openStart||a.openEnd){let t=0,e=0;for(let e=a.content.firstChild;t<a.openStart&&!e.type.spec.isolating;t++,e=e.firstChild);for(let t=a.content.lastChild;e<a.openEnd&&!t.type.spec.isolating;e++,t=t.lastChild);a=ne(a,t,e)}return t.someProp("transformPasted",(e=>{a=e(a,t)})),a}const Yt=/^(a|abbr|acronym|b|cite|code|del|em|i|ins|kbd|label|output|q|ruby|s|samp|span|strong|sub|sup|time|u|tt|var)$/i;function Xt(t,e,n=0){for(let r=e.length-1;r>=n;r--)t=e[r].create(null,o.FK.from(t));return t}function Qt(t,e,n,r,i){if(i<t.length&&i<e.length&&t[i]==e[i]){let s=Qt(t,e,n,r.lastChild,i+1);if(s)return r.copy(r.content.replaceChild(r.childCount-1,s));if(r.contentMatchAt(r.childCount).matchType(i==t.length-1?n.type:t[i+1]))return r.copy(r.content.append(o.FK.from(Xt(n,t,i+1))))}}function te(t,e){if(0==e)return t;let n=t.content.replaceChild(t.childCount-1,te(t.lastChild,e-1)),r=t.contentMatchAt(t.childCount).fillBefore(o.FK.empty,!0);return t.copy(n.append(r))}function ee(t,e,n,r,i,s){let a=e<0?t.firstChild:t.lastChild,l=a.content;return t.childCount>1&&(s=0),i<r-1&&(l=ee(l,e,n,r,i+1,s)),i>=n&&(l=e<0?a.contentMatchAt(0).fillBefore(l,s<=i).append(l):l.append(a.contentMatchAt(a.childCount).fillBefore(o.FK.empty,!0))),t.replaceChild(e<0?0:t.childCount-1,a.copy(l))}function ne(t,e,n){return e<t.openStart&&(t=new o.Ji(ee(t.content,-1,e,t.openStart,0,t.openEnd),e,t.openEnd)),n<t.openEnd&&(t=new o.Ji(ee(t.content,1,n,t.openEnd,0,0),t.openStart,n)),t}const re={thead:["table"],tbody:["table"],tfoot:["table"],caption:["table"],colgroup:["table"],col:["table","colgroup"],tr:["table","tbody"],td:["table","tbody","tr"],th:["table","tbody","tr"]};let oe=null;function ie(){return oe||(oe=document.implementation.createHTMLDocument("title"))}const se={},ae={},le={touchstart:!0,touchmove:!0};class ce{constructor(){this.shiftKey=!1,this.mouseDown=null,this.lastKeyCode=null,this.lastKeyCodeTime=0,this.lastClick={time:0,x:0,y:0,type:""},this.lastSelectionOrigin=null,this.lastSelectionTime=0,this.lastIOSEnter=0,this.lastIOSEnterFallbackTimeout=-1,this.lastFocus=0,this.lastTouch=0,this.lastAndroidDelete=0,this.composing=!1,this.compositionNode=null,this.composingTimeout=-1,this.compositionNodes=[],this.compositionEndedAt=-2e8,this.compositionID=1,this.compositionPendingChanges=0,this.domChangeCount=0,this.eventHandlers=Object.create(null),this.hideSelectionGuard=null}}function de(t,e){t.input.lastSelectionOrigin=e,t.input.lastSelectionTime=Date.now()}function ue(t){t.someProp("handleDOMEvents",(e=>{for(let n in e)t.input.eventHandlers[n]||t.dom.addEventListener(n,t.input.eventHandlers[n]=e=>he(t,e))}))}function he(t,e){return t.someProp("handleDOMEvents",(n=>{let r=n[e.type];return!!r&&(r(t,e)||e.defaultPrevented)}))}function pe(t,e){if(!e.bubbles)return!0;if(e.defaultPrevented)return!1;for(let n=e.target;n!=t.dom;n=n.parentNode)if(!n||11==n.nodeType||n.pmViewDesc&&n.pmViewDesc.stopEvent(e))return!1;return!0}function fe(t){return{left:t.clientX,top:t.clientY}}function me(t,e,n,r,o){if(-1==r)return!1;let i=t.state.doc.resolve(r);for(let r=i.depth+1;r>0;r--)if(t.someProp(e,(e=>r>i.depth?e(t,n,i.nodeAfter,i.before(r),o,!0):e(t,n,i.node(r),i.before(r),o,!1))))return!0;return!1}function ge(t,e,n){t.focused||t.focus();let r=t.state.tr.setSelection(e);"pointer"==n&&r.setMeta("pointer",!0),t.dispatch(r)}function ye(t,e,n,r){return me(t,"handleDoubleClickOn",e,n,r)||t.someProp("handleDoubleClick",(n=>n(t,e,r)))}function ve(t,e,n,o){return me(t,"handleTripleClickOn",e,n,o)||t.someProp("handleTripleClick",(n=>n(t,e,o)))||function(t,e,n){if(0!=n.button)return!1;let o=t.state.doc;if(-1==e)return!!o.inlineContent&&(ge(t,r.U3.create(o,0,o.content.size),"pointer"),!0);let i=o.resolve(e);for(let e=i.depth+1;e>0;e--){let n=e>i.depth?i.nodeAfter:i.node(e),s=i.before(e);if(n.inlineContent)ge(t,r.U3.create(o,s+1,s+1+n.content.size),"pointer");else{if(!r.nh.isSelectable(n))continue;ge(t,r.nh.create(o,s),"pointer")}return!0}}(t,n,o)}function be(t){return Ae(t)}ae.keydown=(t,e)=>{let n=e;if(t.input.shiftKey=16==n.keyCode||n.shiftKey,!ke(t,n)&&(t.input.lastKeyCode=n.keyCode,t.input.lastKeyCodeTime=Date.now(),!R||!T||13!=n.keyCode))if(229!=n.keyCode&&t.domObserver.forceFlush(),!_||13!=n.keyCode||n.ctrlKey||n.altKey||n.metaKey)t.someProp("handleKeyDown",(e=>e(t,n)))||function(t,e){let n=e.keyCode,r=function(t){let e="";return t.ctrlKey&&(e+="c"),t.metaKey&&(e+="m"),t.altKey&&(e+="a"),t.shiftKey&&(e+="s"),e}(e);if(8==n||N&&72==n&&"c"==r)return Wt(t,-1)||Ht(t,-1);if(46==n&&!e.shiftKey||N&&68==n&&"c"==r)return Wt(t,1)||Ht(t,1);if(13==n||27==n)return!0;if(37==n||N&&66==n&&"c"==r){let e=37==n?"ltr"==Ut(t,t.state.selection.from)?-1:1:-1;return Ft(t,e,r)||Ht(t,e)}if(39==n||N&&70==n&&"c"==r){let e=39==n?"ltr"==Ut(t,t.state.selection.from)?1:-1:1;return Ft(t,e,r)||Ht(t,e)}return 38==n||N&&80==n&&"c"==r?Jt(t,-1,r)||Ht(t,-1):40==n||N&&78==n&&"c"==r?function(t){if(!E||t.state.selection.$head.parentOffset>0)return!1;let{focusNode:e,focusOffset:n}=t.domSelectionRange();if(e&&1==e.nodeType&&0==n&&e.firstChild&&"false"==e.firstChild.contentEditable){let n=e.firstChild;qt(t,n,"true"),setTimeout((()=>qt(t,n,"false")),20)}return!1}(t)||Jt(t,1,r)||Ht(t,1):r==(N?"m":"c")&&(66==n||73==n||89==n||90==n)}(t,n)?n.preventDefault():de(t,"key");else{let e=Date.now();t.input.lastIOSEnter=e,t.input.lastIOSEnterFallbackTimeout=setTimeout((()=>{t.input.lastIOSEnter==e&&(t.someProp("handleKeyDown",(e=>e(t,g(13,"Enter")))),t.input.lastIOSEnter=0)}),200)}},ae.keyup=(t,e)=>{16==e.keyCode&&(t.input.shiftKey=!1)},ae.keypress=(t,e)=>{let n=e;if(ke(t,n)||!n.charCode||n.ctrlKey&&!n.altKey||N&&n.metaKey)return;if(t.someProp("handleKeyPress",(e=>e(t,n))))return void n.preventDefault();let o=t.state.selection;if(!(o instanceof r.U3&&o.$from.sameParent(o.$to))){let e=String.fromCharCode(n.charCode);/[\r\n]/.test(e)||t.someProp("handleTextInput",(n=>n(t,o.$from.pos,o.$to.pos,e)))||t.dispatch(t.state.tr.insertText(e).scrollIntoView()),n.preventDefault()}};const we=N?"metaKey":"ctrlKey";se.mousedown=(t,e)=>{let n=e;t.input.shiftKey=n.shiftKey;let r=be(t),o=Date.now(),i="singleClick";o-t.input.lastClick.time<500&&function(t,e){let n=e.x-t.clientX,r=e.y-t.clientY;return n*n+r*r<100}(n,t.input.lastClick)&&!n[we]&&("singleClick"==t.input.lastClick.type?i="doubleClick":"doubleClick"==t.input.lastClick.type&&(i="tripleClick")),t.input.lastClick={time:o,x:n.clientX,y:n.clientY,type:i};let s=t.posAtCoords(fe(n));s&&("singleClick"==i?(t.input.mouseDown&&t.input.mouseDown.done(),t.input.mouseDown=new xe(t,s,n,!!r)):("doubleClick"==i?ye:ve)(t,s.pos,s.inside,n)?n.preventDefault():de(t,"pointer"))};class xe{constructor(t,e,n,o){let i,s;if(this.view=t,this.pos=e,this.event=n,this.flushed=o,this.delayedSelectionSync=!1,this.mightDrag=null,this.startDoc=t.state.doc,this.selectNode=!!n[we],this.allowDefault=n.shiftKey,e.inside>-1)i=t.state.doc.nodeAt(e.inside),s=e.inside;else{let n=t.state.doc.resolve(e.pos);i=n.parent,s=n.depth?n.before():0}const a=o?null:n.target,l=a?t.docView.nearestDesc(a,!0):null;this.target=l&&1==l.dom.nodeType?l.dom:null;let{selection:c}=t.state;(0==n.button&&i.type.spec.draggable&&!1!==i.type.spec.selectable||c instanceof r.nh&&c.from<=s&&c.to>s)&&(this.mightDrag={node:i,pos:s,addAttr:!(!this.target||this.target.draggable),setUneditable:!(!this.target||!C||this.target.hasAttribute("contentEditable"))}),this.target&&this.mightDrag&&(this.mightDrag.addAttr||this.mightDrag.setUneditable)&&(this.view.domObserver.stop(),this.mightDrag.addAttr&&(this.target.draggable=!0),this.mightDrag.setUneditable&&setTimeout((()=>{this.view.input.mouseDown==this&&this.target.setAttribute("contentEditable","false")}),20),this.view.domObserver.start()),t.root.addEventListener("mouseup",this.up=this.up.bind(this)),t.root.addEventListener("mousemove",this.move=this.move.bind(this)),de(t,"pointer")}done(){this.view.root.removeEventListener("mouseup",this.up),this.view.root.removeEventListener("mousemove",this.move),this.mightDrag&&this.target&&(this.view.domObserver.stop(),this.mightDrag.addAttr&&this.target.removeAttribute("draggable"),this.mightDrag.setUneditable&&this.target.removeAttribute("contentEditable"),this.view.domObserver.start()),this.delayedSelectionSync&&setTimeout((()=>Tt(this.view))),this.view.input.mouseDown=null}up(t){if(this.done(),!this.view.dom.contains(t.target))return;let e=this.pos;this.view.state.doc!=this.startDoc&&(e=this.view.posAtCoords(fe(t))),this.updateAllowDefault(t),this.allowDefault||!e?de(this.view,"pointer"):function(t,e,n,o,i){return me(t,"handleClickOn",e,n,o)||t.someProp("handleClick",(n=>n(t,e,o)))||(i?function(t,e){if(-1==e)return!1;let n,o,i=t.state.selection;i instanceof r.nh&&(n=i.node);let s=t.state.doc.resolve(e);for(let t=s.depth+1;t>0;t--){let e=t>s.depth?s.nodeAfter:s.node(t);if(r.nh.isSelectable(e)){o=n&&i.$from.depth>0&&t>=i.$from.depth&&s.before(i.$from.depth+1)==i.$from.pos?s.before(i.$from.depth):s.before(t);break}}return null!=o&&(ge(t,r.nh.create(t.state.doc,o),"pointer"),!0)}(t,n):function(t,e){if(-1==e)return!1;let n=t.state.doc.resolve(e),o=n.nodeAfter;return!!(o&&o.isAtom&&r.nh.isSelectable(o))&&(ge(t,new r.nh(n),"pointer"),!0)}(t,n))}(this.view,e.pos,e.inside,t,this.selectNode)?t.preventDefault():0==t.button&&(this.flushed||E&&this.mightDrag&&!this.mightDrag.node.isAtom||T&&!this.view.state.selection.visible&&Math.min(Math.abs(e.pos-this.view.state.selection.from),Math.abs(e.pos-this.view.state.selection.to))<=2)?(ge(this.view,r.LN.near(this.view.state.doc.resolve(e.pos)),"pointer"),t.preventDefault()):de(this.view,"pointer")}move(t){this.updateAllowDefault(t),de(this.view,"pointer"),0==t.buttons&&this.done()}updateAllowDefault(t){!this.allowDefault&&(Math.abs(this.event.x-t.clientX)>4||Math.abs(this.event.y-t.clientY)>4)&&(this.allowDefault=!0)}}function ke(t,e){return!!t.composing||!!(E&&Math.abs(e.timeStamp-t.input.compositionEndedAt)<500)&&(t.input.compositionEndedAt=-2e8,!0)}se.touchstart=t=>{t.input.lastTouch=Date.now(),be(t),de(t,"pointer")},se.touchmove=t=>{t.input.lastTouch=Date.now(),de(t,"pointer")},se.contextmenu=t=>be(t);const Se=R?5e3:-1;function Me(t,e){clearTimeout(t.input.composingTimeout),e>-1&&(t.input.composingTimeout=setTimeout((()=>Ae(t)),e))}function Ce(t){for(t.composing&&(t.input.composing=!1,t.input.compositionEndedAt=function(){let t=document.createEvent("Event");return t.initEvent("event",!0,!0),t.timeStamp}());t.input.compositionNodes.length>0;)t.input.compositionNodes.pop().markParentsDirty()}function Ae(t,e=!1){if(!(R&&t.domObserver.flushingSoon>=0)){if(t.domObserver.forceFlush(),Ce(t),e||t.docView&&t.docView.dirty){let e=Ct(t);return e&&!e.eq(t.state.selection)?t.dispatch(t.state.tr.setSelection(e)):t.updateState(t.state),!0}return!1}}ae.compositionstart=ae.compositionupdate=t=>{if(!t.composing){t.domObserver.flush();let{state:e}=t,n=e.selection.$from;if(e.selection.empty&&(e.storedMarks||!n.textOffset&&n.parentOffset&&n.nodeBefore.marks.some((t=>!1===t.type.spec.inclusive))))t.markCursor=t.state.storedMarks||n.marks(),Ae(t,!0),t.markCursor=null;else if(Ae(t),C&&e.selection.empty&&n.parentOffset&&!n.textOffset&&n.nodeBefore.marks.length){let e=t.domSelectionRange();for(let n=e.focusNode,r=e.focusOffset;n&&1==n.nodeType&&0!=r;){let e=r<0?n.lastChild:n.childNodes[r-1];if(!e)break;if(3==e.nodeType){t.domSelection().collapse(e,e.nodeValue.length);break}n=e,r=-1}}t.input.composing=!0}Me(t,Se)},ae.compositionend=(t,e)=>{t.composing&&(t.input.composing=!1,t.input.compositionEndedAt=e.timeStamp,t.input.compositionPendingChanges=t.domObserver.pendingRecords().length?t.input.compositionID:0,t.input.compositionNode=null,t.input.compositionPendingChanges&&Promise.resolve().then((()=>t.domObserver.flush())),t.input.compositionID++,Me(t,20))};const Te=S&&M<15||_&&P<604;function Oe(t,e,n,r,i){let s=Gt(t,e,n,r,t.state.selection.$from);if(t.someProp("handlePaste",(e=>e(t,i,s||o.Ji.empty))))return!0;if(!s)return!1;let a=function(t){return 0==t.openStart&&0==t.openEnd&&1==t.content.childCount?t.content.firstChild:null}(s),l=a?t.state.tr.replaceSelectionWith(a,r):t.state.tr.replaceSelection(s);return t.dispatch(l.scrollIntoView().setMeta("paste",!0).setMeta("uiEvent","paste")),!0}function Ee(t){let e=t.getData("text/plain")||t.getData("Text");if(e)return e;let n=t.getData("text/uri-list");return n?n.replace(/\r?\n/g," "):""}se.copy=ae.cut=(t,e)=>{let n=e,r=t.state.selection,o="cut"==n.type;if(r.empty)return;let i=Te?null:n.clipboardData,s=r.content(),{dom:a,text:l}=Zt(t,s);i?(n.preventDefault(),i.clearData(),i.setData("text/html",a.innerHTML),i.setData("text/plain",l)):function(t,e){if(!t.dom.parentNode)return;let n=t.dom.parentNode.appendChild(document.createElement("div"));n.appendChild(e),n.style.cssText="position: fixed; left: -10000px; top: 10px";let r=getSelection(),o=document.createRange();o.selectNodeContents(e),t.dom.blur(),r.removeAllRanges(),r.addRange(o),setTimeout((()=>{n.parentNode&&n.parentNode.removeChild(n),t.focus()}),50)}(t,a),o&&t.dispatch(t.state.tr.deleteSelection().scrollIntoView().setMeta("uiEvent","cut"))},ae.paste=(t,e)=>{let n=e;if(t.composing&&!R)return;let r=Te?null:n.clipboardData,o=t.input.shiftKey&&45!=t.input.lastKeyCode;r&&Oe(t,Ee(r),r.getData("text/html"),o,n)?n.preventDefault():function(t,e){if(!t.dom.parentNode)return;let n=t.input.shiftKey||t.state.selection.$from.parent.type.spec.code,r=t.dom.parentNode.appendChild(document.createElement(n?"textarea":"div"));n||(r.contentEditable="true"),r.style.cssText="position: fixed; left: -10000px; top: 10px",r.focus();let o=t.input.shiftKey&&45!=t.input.lastKeyCode;setTimeout((()=>{t.focus(),r.parentNode&&r.parentNode.removeChild(r),n?Oe(t,r.value,null,o,e):Oe(t,r.textContent,r.innerHTML,o,e)}),50)}(t,n)};class _e{constructor(t,e,n){this.slice=t,this.move=e,this.node=n}}const Ne=N?"altKey":"ctrlKey";se.dragstart=(t,e)=>{let n=e,o=t.input.mouseDown;if(o&&o.done(),!n.dataTransfer)return;let i,s=t.state.selection,a=s.empty?null:t.posAtCoords(fe(n));if(a&&a.pos>=s.from&&a.pos<=(s instanceof r.nh?s.to-1:s.to));else if(o&&o.mightDrag)i=r.nh.create(t.state.doc,o.mightDrag.pos);else if(n.target&&1==n.target.nodeType){let e=t.docView.nearestDesc(n.target,!0);e&&e.node.type.spec.draggable&&e!=t.docView&&(i=r.nh.create(t.state.doc,e.posBefore))}let l=(i||t.state.selection).content(),{dom:c,text:d,slice:u}=Zt(t,l);n.dataTransfer.clearData(),n.dataTransfer.setData(Te?"Text":"text/html",c.innerHTML),n.dataTransfer.effectAllowed="copyMove",Te||n.dataTransfer.setData("text/plain",d),t.dragging=new _e(u,!n[Ne],i)},se.dragend=t=>{let e=t.dragging;window.setTimeout((()=>{t.dragging==e&&(t.dragging=null)}),50)},ae.dragover=ae.dragenter=(t,e)=>e.preventDefault(),ae.drop=(t,e)=>{let n=e,s=t.dragging;if(t.dragging=null,!n.dataTransfer)return;let a=t.posAtCoords(fe(n));if(!a)return;let l=t.state.doc.resolve(a.pos),c=s&&s.slice;c?t.someProp("transformPasted",(e=>{c=e(c,t)})):c=Gt(t,Ee(n.dataTransfer),Te?null:n.dataTransfer.getData("text/html"),!1,l);let d=!(!s||n[Ne]);if(t.someProp("handleDrop",(e=>e(t,n,c||o.Ji.empty,d))))return void n.preventDefault();if(!c)return;n.preventDefault();let u=c?(0,i.Um)(t.state.doc,l.pos,c):l.pos;null==u&&(u=l.pos);let h=t.state.tr;if(d){let{node:t}=s;t?t.replace(h):h.deleteSelection()}let p=h.mapping.map(u),f=0==c.openStart&&0==c.openEnd&&1==c.content.childCount,m=h.doc;if(f?h.replaceRangeWith(p,p,c.content.firstChild):h.replaceRange(p,p,c),h.doc.eq(m))return;let g=h.doc.resolve(p);if(f&&r.nh.isSelectable(c.content.firstChild)&&g.nodeAfter&&g.nodeAfter.sameMarkup(c.content.firstChild))h.setSelection(new r.nh(g));else{let e=h.mapping.map(u);h.mapping.maps[h.mapping.maps.length-1].forEach(((t,n,r,o)=>e=o)),h.setSelection(zt(t,g,h.doc.resolve(e)))}t.focus(),t.dispatch(h.setMeta("uiEvent","drop"))},se.focus=t=>{t.input.lastFocus=Date.now(),t.focused||(t.domObserver.stop(),t.dom.classList.add("ProseMirror-focused"),t.domObserver.start(),t.focused=!0,setTimeout((()=>{t.docView&&t.hasFocus()&&!t.domObserver.currentSelection.eq(t.domSelectionRange())&&Tt(t)}),20))},se.blur=(t,e)=>{let n=e;t.focused&&(t.domObserver.stop(),t.dom.classList.remove("ProseMirror-focused"),t.domObserver.start(),n.relatedTarget&&t.dom.contains(n.relatedTarget)&&t.domObserver.currentSelection.clear(),t.focused=!1)},se.beforeinput=(t,e)=>{if(T&&R&&"deleteContentBackward"==e.inputType){t.domObserver.flushSoon();let{domChangeCount:e}=t.input;setTimeout((()=>{if(t.input.domChangeCount!=e)return;if(t.dom.blur(),t.focus(),t.someProp("handleKeyDown",(e=>e(t,g(8,"Backspace")))))return;let{$cursor:n}=t.state.selection;n&&n.pos>0&&t.dispatch(t.state.tr.delete(n.pos-1,n.pos).scrollIntoView())}),50)}};for(let t in ae)se[t]=ae[t];function $e(t,e){if(t==e)return!0;for(let n in t)if(t[n]!==e[n])return!1;for(let n in e)if(!(n in t))return!1;return!0}class Re{constructor(t,e){this.toDOM=t,this.spec=e||Ie,this.side=this.spec.side||0}map(t,e,n,r){let{pos:o,deleted:i}=t.mapResult(e.from+r,this.side<0?-1:1);return i?null:new De(o-n,o-n,this)}valid(){return!0}eq(t){return this==t||t instanceof Re&&(this.spec.key&&this.spec.key==t.spec.key||this.toDOM==t.toDOM&&$e(this.spec,t.spec))}destroy(t){this.spec.destroy&&this.spec.destroy(t)}}class ze{constructor(t,e){this.attrs=t,this.spec=e||Ie}map(t,e,n,r){let o=t.map(e.from+r,this.spec.inclusiveStart?-1:1)-n,i=t.map(e.to+r,this.spec.inclusiveEnd?1:-1)-n;return o>=i?null:new De(o,i,this)}valid(t,e){return e.from<e.to}eq(t){return this==t||t instanceof ze&&$e(this.attrs,t.attrs)&&$e(this.spec,t.spec)}static is(t){return t.type instanceof ze}destroy(){}}class Pe{constructor(t,e){this.attrs=t,this.spec=e||Ie}map(t,e,n,r){let o=t.mapResult(e.from+r,1);if(o.deleted)return null;let i=t.mapResult(e.to+r,-1);return i.deleted||i.pos<=o.pos?null:new De(o.pos-n,i.pos-n,this)}valid(t,e){let n,{index:r,offset:o}=t.content.findIndex(e.from);return o==e.from&&!(n=t.child(r)).isText&&o+n.nodeSize==e.to}eq(t){return this==t||t instanceof Pe&&$e(this.attrs,t.attrs)&&$e(this.spec,t.spec)}destroy(){}}class De{constructor(t,e,n){this.from=t,this.to=e,this.type=n}copy(t,e){return new De(t,e,this.type)}eq(t,e=0){return this.type.eq(t.type)&&this.from+e==t.from&&this.to+e==t.to}map(t,e,n){return this.type.map(t,this,e,n)}static widget(t,e,n){return new De(t,t,new Re(e,n))}static inline(t,e,n,r){return new De(t,e,new ze(n,r))}static node(t,e,n,r){return new De(t,e,new Pe(n,r))}get spec(){return this.type.spec}get inline(){return this.type instanceof ze}get widget(){return this.type instanceof Re}}const Le=[],Ie={};class Fe{constructor(t,e){this.local=t.length?t:Le,this.children=e.length?e:Le}static create(t,e){return e.length?Ue(e,t,0,Ie):je}find(t,e,n){let r=[];return this.findInner(null==t?0:t,null==e?1e9:e,r,0,n),r}findInner(t,e,n,r,o){for(let i=0;i<this.local.length;i++){let s=this.local[i];s.from<=e&&s.to>=t&&(!o||o(s.spec))&&n.push(s.copy(s.from+r,s.to+r))}for(let i=0;i<this.children.length;i+=3)if(this.children[i]<e&&this.children[i+1]>t){let s=this.children[i]+1;this.children[i+2].findInner(t-s,e-s,n,r+s,o)}}map(t,e,n){return this==je||0==t.maps.length?this:this.mapInner(t,e,0,0,n||Ie)}mapInner(t,e,n,r,o){let i;for(let s=0;s<this.local.length;s++){let a=this.local[s].map(t,n,r);a&&a.type.valid(e,a)?(i||(i=[])).push(a):o.onRemove&&o.onRemove(this.local[s].spec)}return this.children.length?function(t,e,n,r,o,i,s){let a=t.slice();for(let t=0,e=i;t<n.maps.length;t++){let r=0;n.maps[t].forEach(((t,n,o,i)=>{let s=i-o-(n-t);for(let o=0;o<a.length;o+=3){let i=a[o+1];if(i<0||t>i+e-r)continue;let l=a[o]+e-r;n>=l?a[o+1]=t<=l?-2:-1:t>=e&&s&&(a[o]+=s,a[o+1]+=s)}r+=s})),e=n.maps[t].map(e,-1)}let l=!1;for(let e=0;e<a.length;e+=3)if(a[e+1]<0){if(-2==a[e+1]){l=!0,a[e+1]=-1;continue}let c=n.map(t[e]+i),d=c-o;if(d<0||d>=r.content.size){l=!0;continue}let u=n.map(t[e+1]+i,-1)-o,{index:h,offset:p}=r.content.findIndex(d),f=r.maybeChild(h);if(f&&p==d&&p+f.nodeSize==u){let r=a[e+2].mapInner(n,f,c+1,t[e]+i+1,s);r!=je?(a[e]=d,a[e+1]=u,a[e+2]=r):(a[e+1]=-2,l=!0)}else l=!0}if(l){let l=function(t,e,n,r,o,i,s){function a(t,e){for(let i=0;i<t.local.length;i++){let a=t.local[i].map(r,o,e);a?n.push(a):s.onRemove&&s.onRemove(t.local[i].spec)}for(let n=0;n<t.children.length;n+=3)a(t.children[n+2],t.children[n]+e+1)}for(let n=0;n<t.length;n+=3)-1==t[n+1]&&a(t[n+2],e[n]+i+1);return n}(a,t,e,n,o,i,s),c=Ue(l,r,0,s);e=c.local;for(let t=0;t<a.length;t+=3)a[t+1]<0&&(a.splice(t,3),t-=3);for(let t=0,e=0;t<c.children.length;t+=3){let n=c.children[t];for(;e<a.length&&a[e]<n;)e+=3;a.splice(e,0,c.children[t],c.children[t+1],c.children[t+2])}}return new Fe(e.sort(Je),a)}(this.children,i||[],t,e,n,r,o):i?new Fe(i.sort(Je),Le):je}add(t,e){return e.length?this==je?Fe.create(t,e):this.addInner(t,e,0):this}addInner(t,e,n){let r,o=0;t.forEach(((t,i)=>{let s,a=i+n;if(s=Ve(e,t,a)){for(r||(r=this.children.slice());o<r.length&&r[o]<i;)o+=3;r[o]==i?r[o+2]=r[o+2].addInner(t,s,a+1):r.splice(o,0,i,i+t.nodeSize,Ue(s,t,a+1,Ie)),o+=3}}));let i=He(o?Ke(e):e,-n);for(let e=0;e<i.length;e++)i[e].type.valid(t,i[e])||i.splice(e--,1);return new Fe(i.length?this.local.concat(i).sort(Je):this.local,r||this.children)}remove(t){return 0==t.length||this==je?this:this.removeInner(t,0)}removeInner(t,e){let n=this.children,r=this.local;for(let r=0;r<n.length;r+=3){let o,i=n[r]+e,s=n[r+1]+e;for(let e,n=0;n<t.length;n++)(e=t[n])&&e.from>i&&e.to<s&&(t[n]=null,(o||(o=[])).push(e));if(!o)continue;n==this.children&&(n=this.children.slice());let a=n[r+2].removeInner(o,i+1);a!=je?n[r+2]=a:(n.splice(r,3),r-=3)}if(r.length)for(let n,o=0;o<t.length;o++)if(n=t[o])for(let t=0;t<r.length;t++)r[t].eq(n,e)&&(r==this.local&&(r=this.local.slice()),r.splice(t--,1));return n==this.children&&r==this.local?this:r.length||n.length?new Fe(r,n):je}forChild(t,e){if(this==je)return this;if(e.isLeaf)return Fe.empty;let n,r;for(let e=0;e<this.children.length;e+=3)if(this.children[e]>=t){this.children[e]==t&&(n=this.children[e+2]);break}let o=t+1,i=o+e.content.size;for(let t=0;t<this.local.length;t++){let e=this.local[t];if(e.from<i&&e.to>o&&e.type instanceof ze){let t=Math.max(o,e.from)-o,n=Math.min(i,e.to)-o;t<n&&(r||(r=[])).push(e.copy(t,n))}}if(r){let t=new Fe(r.sort(Je),Le);return n?new Be([t,n]):t}return n||je}eq(t){if(this==t)return!0;if(!(t instanceof Fe)||this.local.length!=t.local.length||this.children.length!=t.children.length)return!1;for(let e=0;e<this.local.length;e++)if(!this.local[e].eq(t.local[e]))return!1;for(let e=0;e<this.children.length;e+=3)if(this.children[e]!=t.children[e]||this.children[e+1]!=t.children[e+1]||!this.children[e+2].eq(t.children[e+2]))return!1;return!0}locals(t){return We(this.localsInner(t))}localsInner(t){if(this==je)return Le;if(t.inlineContent||!this.local.some(ze.is))return this.local;let e=[];for(let t=0;t<this.local.length;t++)this.local[t].type instanceof ze||e.push(this.local[t]);return e}}Fe.empty=new Fe([],[]),Fe.removeOverlap=We;const je=Fe.empty;class Be{constructor(t){this.members=t}map(t,e){const n=this.members.map((n=>n.map(t,e,Ie)));return Be.from(n)}forChild(t,e){if(e.isLeaf)return Fe.empty;let n=[];for(let r=0;r<this.members.length;r++){let o=this.members[r].forChild(t,e);o!=je&&(o instanceof Be?n=n.concat(o.members):n.push(o))}return Be.from(n)}eq(t){if(!(t instanceof Be)||t.members.length!=this.members.length)return!1;for(let e=0;e<this.members.length;e++)if(!this.members[e].eq(t.members[e]))return!1;return!0}locals(t){let e,n=!0;for(let r=0;r<this.members.length;r++){let o=this.members[r].localsInner(t);if(o.length)if(e){n&&(e=e.slice(),n=!1);for(let t=0;t<o.length;t++)e.push(o[t])}else e=o}return e?We(n?e:e.sort(Je)):Le}static from(t){switch(t.length){case 0:return je;case 1:return t[0];default:return new Be(t.every((t=>t instanceof Fe))?t:t.reduce(((t,e)=>t.concat(e instanceof Fe?e:e.members)),[]))}}}function He(t,e){if(!e||!t.length)return t;let n=[];for(let r=0;r<t.length;r++){let o=t[r];n.push(new De(o.from+e,o.to+e,o.type))}return n}function Ve(t,e,n){if(e.isLeaf)return null;let r=n+e.nodeSize,o=null;for(let e,i=0;i<t.length;i++)(e=t[i])&&e.from>n&&e.to<r&&((o||(o=[])).push(e),t[i]=null);return o}function Ke(t){let e=[];for(let n=0;n<t.length;n++)null!=t[n]&&e.push(t[n]);return e}function Ue(t,e,n,r){let o=[],i=!1;e.forEach(((e,s)=>{let a=Ve(t,e,s+n);if(a){i=!0;let t=Ue(a,e,n+s+1,r);t!=je&&o.push(s,s+e.nodeSize,t)}}));let s=He(i?Ke(t):t,-n).sort(Je);for(let t=0;t<s.length;t++)s[t].type.valid(e,s[t])||(r.onRemove&&r.onRemove(s[t].spec),s.splice(t--,1));return s.length||o.length?new Fe(s,o):je}function Je(t,e){return t.from-e.from||t.to-e.to}function We(t){let e=t;for(let n=0;n<e.length-1;n++){let r=e[n];if(r.from!=r.to)for(let o=n+1;o<e.length;o++){let i=e[o];if(i.from!=r.from){i.from<r.to&&(e==t&&(e=t.slice()),e[n]=r.copy(r.from,i.from),qe(e,o,r.copy(i.from,r.to)));break}i.to!=r.to&&(e==t&&(e=t.slice()),e[o]=i.copy(i.from,r.to),qe(e,o+1,i.copy(r.to,i.to)))}}return e}function qe(t,e,n){for(;e<t.length&&Je(n,t[e])>0;)e++;t.splice(e,0,n)}function Ze(t){let e=[];return t.someProp("decorations",(n=>{let r=n(t.state);r&&r!=je&&e.push(r)})),t.cursorWrapper&&e.push(Fe.create(t.state.doc,[t.cursorWrapper.deco])),Be.from(e)}const Ge={childList:!0,characterData:!0,characterDataOldValue:!0,attributes:!0,attributeOldValue:!0,subtree:!0},Ye=S&&M<=11;class Xe{constructor(){this.anchorNode=null,this.anchorOffset=0,this.focusNode=null,this.focusOffset=0}set(t){this.anchorNode=t.anchorNode,this.anchorOffset=t.anchorOffset,this.focusNode=t.focusNode,this.focusOffset=t.focusOffset}clear(){this.anchorNode=this.focusNode=null}eq(t){return t.anchorNode==this.anchorNode&&t.anchorOffset==this.anchorOffset&&t.focusNode==this.focusNode&&t.focusOffset==this.focusOffset}}class Qe{constructor(t,e){this.view=t,this.handleDOMChange=e,this.queue=[],this.flushingSoon=-1,this.observer=null,this.currentSelection=new Xe,this.onCharData=null,this.suppressingSelectionUpdates=!1,this.lastChangedTextNode=null,this.observer=window.MutationObserver&&new window.MutationObserver((t=>{for(let e=0;e<t.length;e++)this.queue.push(t[e]);S&&M<=11&&t.some((t=>"childList"==t.type&&t.removedNodes.length||"characterData"==t.type&&t.oldValue.length>t.target.nodeValue.length))?this.flushSoon():this.flush()})),Ye&&(this.onCharData=t=>{this.queue.push({target:t.target,type:"characterData",oldValue:t.prevValue}),this.flushSoon()}),this.onSelectionChange=this.onSelectionChange.bind(this)}flushSoon(){this.flushingSoon<0&&(this.flushingSoon=window.setTimeout((()=>{this.flushingSoon=-1,this.flush()}),20))}forceFlush(){this.flushingSoon>-1&&(window.clearTimeout(this.flushingSoon),this.flushingSoon=-1,this.flush())}start(){this.observer&&(this.observer.takeRecords(),this.observer.observe(this.view.dom,Ge)),this.onCharData&&this.view.dom.addEventListener("DOMCharacterDataModified",this.onCharData),this.connectSelection()}stop(){if(this.observer){let t=this.observer.takeRecords();if(t.length){for(let e=0;e<t.length;e++)this.queue.push(t[e]);window.setTimeout((()=>this.flush()),20)}this.observer.disconnect()}this.onCharData&&this.view.dom.removeEventListener("DOMCharacterDataModified",this.onCharData),this.disconnectSelection()}connectSelection(){this.view.dom.ownerDocument.addEventListener("selectionchange",this.onSelectionChange)}disconnectSelection(){this.view.dom.ownerDocument.removeEventListener("selectionchange",this.onSelectionChange)}suppressSelectionUpdates(){this.suppressingSelectionUpdates=!0,setTimeout((()=>this.suppressingSelectionUpdates=!1),50)}onSelectionChange(){if(Pt(this.view)){if(this.suppressingSelectionUpdates)return Tt(this.view);if(S&&M<=11&&!this.view.state.selection.empty){let t=this.view.domSelectionRange();if(t.focusNode&&d(t.focusNode,t.focusOffset,t.anchorNode,t.anchorOffset))return this.flushSoon()}this.flush()}}setCurSelection(){this.currentSelection.set(this.view.domSelectionRange())}ignoreSelectionChange(t){if(!t.focusNode)return!0;let e,n=new Set;for(let e=t.focusNode;e;e=a(e))n.add(e);for(let r=t.anchorNode;r;r=a(r))if(n.has(r)){e=r;break}let r=e&&this.view.docView.nearestDesc(e);return r&&r.ignoreMutation({type:"selection",target:3==e.nodeType?e.parentNode:e})?(this.setCurSelection(),!0):void 0}pendingRecords(){if(this.observer)for(let t of this.observer.takeRecords())this.queue.push(t);return this.queue}flush(){let{view:t}=this;if(!t.docView||this.flushingSoon>-1)return;let e=this.pendingRecords();e.length&&(this.queue=[]);let n=t.domSelectionRange(),o=!this.suppressingSelectionUpdates&&!this.currentSelection.eq(n)&&Pt(t)&&!this.ignoreSelectionChange(n),i=-1,s=-1,a=!1,l=[];if(t.editable)for(let t=0;t<e.length;t++){let n=this.registerMutation(e[t],l);n&&(i=i<0?n.from:Math.min(n.from,i),s=s<0?n.to:Math.max(n.to,s),n.typeOver&&(a=!0))}if(C&&l.length){let e=l.filter((t=>"BR"==t.nodeName));if(2==e.length){let[t,n]=e;t.parentNode&&t.parentNode.parentNode==n.parentNode?n.remove():t.remove()}else{let{focusNode:n}=this.currentSelection;for(let r of e){let e=r.parentNode;!e||"LI"!=e.nodeName||n&&rn(t,n)==e||r.remove()}}}let c=null;i<0&&o&&t.input.lastFocus>Date.now()-200&&Math.max(t.input.lastTouch,t.input.lastClick.time)<Date.now()-300&&m(n)&&(c=Ct(t))&&c.eq(r.LN.near(t.state.doc.resolve(0),1))?(t.input.lastFocus=0,Tt(t),this.currentSelection.set(n),t.scrollToSelection()):(i>-1||o)&&(i>-1&&(t.docView.markDirty(i,s),function(t){if(!tn.has(t)&&(tn.set(t,null),-1!==["normal","nowrap","pre-line"].indexOf(getComputedStyle(t.dom).whiteSpace))){if(t.requiresGeckoHackNode=C,en)return;console.warn("ProseMirror expects the CSS white-space property to be set, preferably to 'pre-wrap'. It is recommended to load style/prosemirror.css from the prosemirror-view package."),en=!0}}(t)),this.handleDOMChange(i,s,a,l),t.docView&&t.docView.dirty?t.updateState(t.state):this.currentSelection.eq(n)||Tt(t),this.currentSelection.set(n))}registerMutation(t,e){if(e.indexOf(t.target)>-1)return null;let n=this.view.docView.nearestDesc(t.target);if("attributes"==t.type&&(n==this.view.docView||"contenteditable"==t.attributeName||"style"==t.attributeName&&!t.oldValue&&!t.target.getAttribute("style")))return null;if(!n||n.ignoreMutation(t))return null;if("childList"==t.type){for(let n=0;n<t.addedNodes.length;n++){let r=t.addedNodes[n];e.push(r),3==r.nodeType&&(this.lastChangedTextNode=r)}if(n.contentDOM&&n.contentDOM!=n.dom&&!n.contentDOM.contains(t.target))return{from:n.posBefore,to:n.posAfter};let r=t.previousSibling,o=t.nextSibling;if(S&&M<=11&&t.addedNodes.length)for(let e=0;e<t.addedNodes.length;e++){let{previousSibling:n,nextSibling:i}=t.addedNodes[e];(!n||Array.prototype.indexOf.call(t.addedNodes,n)<0)&&(r=n),(!i||Array.prototype.indexOf.call(t.addedNodes,i)<0)&&(o=i)}let i=r&&r.parentNode==t.target?s(r)+1:0,a=n.localPosFromDOM(t.target,i,-1),l=o&&o.parentNode==t.target?s(o):t.target.childNodes.length;return{from:a,to:n.localPosFromDOM(t.target,l,1)}}return"attributes"==t.type?{from:n.posAtStart-n.border,to:n.posAtEnd+n.border}:(this.lastChangedTextNode=t.target,{from:n.posAtStart,to:n.posAtEnd,typeOver:t.target.nodeValue==t.oldValue})}}let tn=new WeakMap,en=!1;function nn(t,e){let n=e.startContainer,r=e.startOffset,o=e.endContainer,i=e.endOffset,s=t.domAtPos(t.state.selection.anchor);return d(s.node,s.offset,o,i)&&([n,r,o,i]=[o,i,n,r]),{anchorNode:n,anchorOffset:r,focusNode:o,focusOffset:i}}function rn(t,e){for(let n=e.parentNode;n&&n!=t.dom;n=n.parentNode){let e=t.docView.nearestDesc(n,!0);if(e&&e.node.isBlock)return n}return null}function on(t){let e=t.pmViewDesc;if(e)return e.parseRule();if("BR"==t.nodeName&&t.parentNode){if(E&&/^(ul|ol)$/i.test(t.parentNode.nodeName)){let t=document.createElement("div");return t.appendChild(document.createElement("li")),{skip:t}}if(t.parentNode.lastChild==t||E&&/^(tr|table)$/i.test(t.parentNode.nodeName))return{ignore:!0}}else if("IMG"==t.nodeName&&t.getAttribute("mark-placeholder"))return{ignore:!0};return null}const sn=/^(a|abbr|acronym|b|bd[io]|big|br|button|cite|code|data(list)?|del|dfn|em|i|ins|kbd|label|map|mark|meter|output|q|ruby|s|samp|small|span|strong|su[bp]|time|u|tt|var)$/i;function an(t,e,n){return Math.max(n.anchor,n.head)>e.content.size?null:zt(t,e.resolve(n.anchor),e.resolve(n.head))}function ln(t,e,n){let r=t.depth,o=e?t.end():t.pos;for(;r>0&&(e||t.indexAfter(r)==t.node(r).childCount);)r--,o++,e=!1;if(n){let e=t.node(r).maybeChild(t.indexAfter(r));for(;e&&!e.isLeaf;)e=e.firstChild,o++}return o}function cn(t){if(2!=t.length)return!1;let e=t.charCodeAt(0),n=t.charCodeAt(1);return e>=56320&&e<=57343&&n>=55296&&n<=56319}class dn{constructor(t,e){this._root=null,this.focused=!1,this.trackWrites=null,this.mounted=!1,this.markCursor=null,this.cursorWrapper=null,this.lastSelectedViewDesc=void 0,this.input=new ce,this.prevDirectPlugins=[],this.pluginViews=[],this.requiresGeckoHackNode=!1,this.dragging=null,this._props=e,this.state=e.state,this.directPlugins=e.plugins||[],this.directPlugins.forEach(mn),this.dispatch=this.dispatch.bind(this),this.dom=t&&t.mount||document.createElement("div"),t&&(t.appendChild?t.appendChild(this.dom):"function"==typeof t?t(this.dom):t.mount&&(this.mounted=!0)),this.editable=pn(this),hn(this),this.nodeViews=fn(this),this.docView=ct(this.state.doc,un(this),Ze(this),this.dom,this),this.domObserver=new Qe(this,((t,e,n,i)=>function(t,e,n,i,s){let a=t.input.compositionPendingChanges||(t.composing?t.input.compositionID:0);if(t.input.compositionPendingChanges=0,e<0){let e=t.input.lastSelectionTime>Date.now()-50?t.input.lastSelectionOrigin:null,n=Ct(t,e);if(n&&!t.state.selection.eq(n)){if(T&&R&&13===t.input.lastKeyCode&&Date.now()-100<t.input.lastKeyCodeTime&&t.someProp("handleKeyDown",(e=>e(t,g(13,"Enter")))))return;let r=t.state.tr.setSelection(n);"pointer"==e?r.setMeta("pointer",!0):"key"==e&&r.scrollIntoView(),a&&r.setMeta("composition",a),t.dispatch(r)}return}let l=t.state.doc.resolve(e),c=l.sharedDepth(n);e=l.before(c+1),n=t.state.doc.resolve(n).after(c+1);let d,u,h=t.state.selection,p=function(t,e,n){let r,{node:i,fromOffset:s,toOffset:a,from:l,to:c}=t.docView.parseRange(e,n),d=t.domSelectionRange(),u=d.anchorNode;if(u&&t.dom.contains(1==u.nodeType?u:u.parentNode)&&(r=[{node:u,offset:d.anchorOffset}],m(d)||r.push({node:d.focusNode,offset:d.focusOffset})),T&&8===t.input.lastKeyCode)for(let t=a;t>s;t--){let e=i.childNodes[t-1],n=e.pmViewDesc;if("BR"==e.nodeName&&!n){a=t;break}if(!n||n.size)break}let h=t.state.doc,p=t.someProp("domParser")||o.S4.fromSchema(t.state.schema),f=h.resolve(l),g=null,y=p.parse(i,{topNode:f.parent,topMatch:f.parent.contentMatchAt(f.index()),topOpen:!0,from:s,to:a,preserveWhitespace:"pre"!=f.parent.type.whitespace||"full",findPositions:r,ruleFromNode:on,context:f});if(r&&null!=r[0].pos){let t=r[0].pos,e=r[1]&&r[1].pos;null==e&&(e=t),g={anchor:t+l,head:e+l}}return{doc:y,sel:g,from:l,to:c}}(t,e,n),f=t.state.doc,y=f.slice(p.from,p.to);8===t.input.lastKeyCode&&Date.now()-100<t.input.lastKeyCodeTime?(d=t.state.selection.to,u="end"):(d=t.state.selection.from,u="start"),t.input.lastKeyCode=null;let v=function(t,e,n,r,o){let i=t.findDiffStart(e,n);if(null==i)return null;let{a:s,b:a}=t.findDiffEnd(e,n+t.size,n+e.size);if("end"==o&&(r-=s+Math.max(0,i-Math.min(s,a))-i),s<i&&t.size<e.size){let t=r<=i&&r>=s?i-r:0;i-=t,i&&i<e.size&&cn(e.textBetween(i-1,i+1))&&(i+=t?1:-1),a=i+(a-s),s=i}else if(a<i){let e=r<=i&&r>=a?i-r:0;i-=e,i&&i<t.size&&cn(t.textBetween(i-1,i+1))&&(i+=e?1:-1),s=i+(s-a),a=i}return{start:i,endA:s,endB:a}}(y.content,p.doc.content,p.from,d,u);if((_&&t.input.lastIOSEnter>Date.now()-225||R)&&s.some((t=>1==t.nodeType&&!sn.test(t.nodeName)))&&(!v||v.endA>=v.endB)&&t.someProp("handleKeyDown",(e=>e(t,g(13,"Enter")))))return void(t.input.lastIOSEnter=0);if(!v){if(!(i&&h instanceof r.U3&&!h.empty&&h.$head.sameParent(h.$anchor))||t.composing||p.sel&&p.sel.anchor!=p.sel.head){if(p.sel){let e=an(t,t.state.doc,p.sel);if(e&&!e.eq(t.state.selection)){let n=t.state.tr.setSelection(e);a&&n.setMeta("composition",a),t.dispatch(n)}}return}v={start:h.from,endA:h.to,endB:h.to}}t.input.domChangeCount++,t.state.selection.from<t.state.selection.to&&v.start==v.endB&&t.state.selection instanceof r.U3&&(v.start>t.state.selection.from&&v.start<=t.state.selection.from+2&&t.state.selection.from>=p.from?v.start=t.state.selection.from:v.endA<t.state.selection.to&&v.endA>=t.state.selection.to-2&&t.state.selection.to<=p.to&&(v.endB+=t.state.selection.to-v.endA,v.endA=t.state.selection.to)),S&&M<=11&&v.endB==v.start+1&&v.endA==v.start&&v.start>p.from&&"  "==p.doc.textBetween(v.start-p.from-1,v.start-p.from+1)&&(v.start--,v.endA--,v.endB--);let b,w=p.doc.resolveNoCache(v.start-p.from),x=p.doc.resolveNoCache(v.endB-p.from),k=f.resolve(v.start),C=w.sameParent(x)&&w.parent.inlineContent&&k.end()>=v.endA;if((_&&t.input.lastIOSEnter>Date.now()-225&&(!C||s.some((t=>"DIV"==t.nodeName||"P"==t.nodeName)))||!C&&w.pos<p.doc.content.size&&!w.sameParent(x)&&(b=r.LN.findFrom(p.doc.resolve(w.pos+1),1,!0))&&b.head==x.pos)&&t.someProp("handleKeyDown",(e=>e(t,g(13,"Enter")))))return void(t.input.lastIOSEnter=0);if(t.state.selection.anchor>v.start&&function(t,e,n,r,o){if(n-e<=o.pos-r.pos||ln(r,!0,!1)<o.pos)return!1;let i=t.resolve(e);if(!r.parent.isTextblock){let t=i.nodeAfter;return null!=t&&n==e+t.nodeSize}if(i.parentOffset<i.parent.content.size||!i.parent.isTextblock)return!1;let s=t.resolve(ln(i,!0,!0));return!(!s.parent.isTextblock||s.pos>n||ln(s,!0,!1)<n)&&r.parent.content.cut(r.parentOffset).eq(s.parent.content)}(f,v.start,v.endA,w,x)&&t.someProp("handleKeyDown",(e=>e(t,g(8,"Backspace")))))return void(R&&T&&t.domObserver.suppressSelectionUpdates());T&&R&&v.endB==v.start&&(t.input.lastAndroidDelete=Date.now()),R&&!C&&w.start()!=x.start()&&0==x.parentOffset&&w.depth==x.depth&&p.sel&&p.sel.anchor==p.sel.head&&p.sel.head==v.endA&&(v.endB-=2,x=p.doc.resolveNoCache(v.endB-p.from),setTimeout((()=>{t.someProp("handleKeyDown",(function(e){return e(t,g(13,"Enter"))}))}),20));let A,O,E,N=v.start,$=v.endA;if(C)if(w.pos==x.pos)S&&M<=11&&0==w.parentOffset&&(t.domObserver.suppressSelectionUpdates(),setTimeout((()=>Tt(t)),20)),A=t.state.tr.delete(N,$),O=f.resolve(v.start).marksAcross(f.resolve(v.endA));else if(v.endA==v.endB&&(E=function(t,e){let n,r,i,s=t.firstChild.marks,a=e.firstChild.marks,l=s,c=a;for(let t=0;t<a.length;t++)l=a[t].removeFromSet(l);for(let t=0;t<s.length;t++)c=s[t].removeFromSet(c);if(1==l.length&&0==c.length)r=l[0],n="add",i=t=>t.mark(r.addToSet(t.marks));else{if(0!=l.length||1!=c.length)return null;r=c[0],n="remove",i=t=>t.mark(r.removeFromSet(t.marks))}let d=[];for(let t=0;t<e.childCount;t++)d.push(i(e.child(t)));if(o.FK.from(d).eq(t))return{mark:r,type:n}}(w.parent.content.cut(w.parentOffset,x.parentOffset),k.parent.content.cut(k.parentOffset,v.endA-k.start()))))A=t.state.tr,"add"==E.type?A.addMark(N,$,E.mark):A.removeMark(N,$,E.mark);else if(w.parent.child(w.index()).isText&&w.index()==x.index()-(x.textOffset?0:1)){let e=w.parent.textBetween(w.parentOffset,x.parentOffset);if(t.someProp("handleTextInput",(n=>n(t,N,$,e))))return;A=t.state.tr.insertText(e,N,$)}if(A||(A=t.state.tr.replace(N,$,p.doc.slice(v.start-p.from,v.endB-p.from))),p.sel){let e=an(t,A.doc,p.sel);e&&!(T&&R&&t.composing&&e.empty&&(v.start!=v.endB||t.input.lastAndroidDelete<Date.now()-100)&&(e.head==N||e.head==A.mapping.map($)-1)||S&&e.empty&&e.head==N)&&A.setSelection(e)}O&&A.ensureMarks(O),a&&A.setMeta("composition",a),t.dispatch(A.scrollIntoView())}(this,t,e,n,i))),this.domObserver.start(),function(t){for(let e in se){let n=se[e];t.dom.addEventListener(e,t.input.eventHandlers[e]=e=>{!pe(t,e)||he(t,e)||!t.editable&&e.type in ae||n(t,e)},le[e]?{passive:!0}:void 0)}E&&t.dom.addEventListener("input",(()=>null)),ue(t)}(this),this.updatePluginViews()}get composing(){return this.input.composing}get props(){if(this._props.state!=this.state){let t=this._props;this._props={};for(let e in t)this._props[e]=t[e];this._props.state=this.state}return this._props}update(t){t.handleDOMEvents!=this._props.handleDOMEvents&&ue(this);let e=this._props;this._props=t,t.plugins&&(t.plugins.forEach(mn),this.directPlugins=t.plugins),this.updateStateInner(t.state,e)}setProps(t){let e={};for(let t in this._props)e[t]=this._props[t];e.state=this.state;for(let n in t)e[n]=t[n];this.update(e)}updateState(t){this.updateStateInner(t,this._props)}updateStateInner(t,e){var n;let r=this.state,o=!1,i=!1;t.storedMarks&&this.composing&&(Ce(this),i=!0),this.state=t;let a=r.plugins!=t.plugins||this._props.plugins!=e.plugins;if(a||this._props.plugins!=e.plugins||this._props.nodeViews!=e.nodeViews){let t=fn(this);(function(t,e){let n=0,r=0;for(let r in t){if(t[r]!=e[r])return!0;n++}for(let t in e)r++;return n!=r})(t,this.nodeViews)&&(this.nodeViews=t,o=!0)}(a||e.handleDOMEvents!=this._props.handleDOMEvents)&&ue(this),this.editable=pn(this),hn(this);let l=Ze(this),c=un(this),u=r.plugins==t.plugins||r.doc.eq(t.doc)?t.scrollToSelection>r.scrollToSelection?"to selection":"preserve":"reset",h=o||!this.docView.matchesNode(t.doc,c,l);!h&&t.selection.eq(r.selection)||(i=!0);let m="preserve"==u&&i&&null==this.dom.style.overflowAnchor&&function(t){let e,n,r=t.dom.getBoundingClientRect(),o=Math.max(0,r.top);for(let i=(r.left+r.right)/2,s=o+1;s<Math.min(innerHeight,r.bottom);s+=5){let r=t.root.elementFromPoint(i,s);if(!r||r==t.dom||!t.dom.contains(r))continue;let a=r.getBoundingClientRect();if(a.top>=o-20){e=r,n=a.top;break}}return{refDOM:e,refTop:n,stack:j(t.dom)}}(this);if(i){this.domObserver.stop();let e=h&&(S||T)&&!this.composing&&!r.selection.empty&&!t.selection.empty&&function(t,e){let n=Math.min(t.$anchor.sharedDepth(t.head),e.$anchor.sharedDepth(e.head));return t.$anchor.start(n)!=e.$anchor.start(n)}(r.selection,t.selection);if(h){let n=T?this.trackWrites=this.domSelectionRange().focusNode:null;this.composing&&(this.input.compositionNode=function(t){let e=t.domSelectionRange();if(!e.focusNode)return null;let n=function(t,e){for(;;){if(3==t.nodeType&&e)return t;if(1==t.nodeType&&e>0){if("false"==t.contentEditable)return null;e=p(t=t.childNodes[e-1])}else{if(!t.parentNode||f(t))return null;e=s(t),t=t.parentNode}}}(e.focusNode,e.focusOffset),r=function(t,e){for(;;){if(3==t.nodeType&&e<t.nodeValue.length)return t;if(1==t.nodeType&&e<t.childNodes.length){if("false"==t.contentEditable)return null;t=t.childNodes[e],e=0}else{if(!t.parentNode||f(t))return null;e=s(t)+1,t=t.parentNode}}}(e.focusNode,e.focusOffset);if(n&&r&&n!=r){let e=r.pmViewDesc,o=t.domObserver.lastChangedTextNode;if(n==o||r==o)return o;if(!e||!e.isText(r.nodeValue))return r;if(t.input.compositionNode==r){let t=n.pmViewDesc;if(t&&t.isText(n.nodeValue))return r}}return n||r}(this)),!o&&this.docView.update(t.doc,c,l,this)||(this.docView.updateOuterDeco(c),this.docView.destroy(),this.docView=ct(t.doc,c,l,this.dom,this)),n&&!this.trackWrites&&(e=!0)}e||!(this.input.mouseDown&&this.domObserver.currentSelection.eq(this.domSelectionRange())&&function(t){let e=t.docView.domFromPos(t.state.selection.anchor,0),n=t.domSelectionRange();return d(e.node,e.offset,n.anchorNode,n.anchorOffset)}(this))?Tt(this,e):($t(this,t.selection),this.domObserver.setCurSelection()),this.domObserver.start()}this.updatePluginViews(r),(null===(n=this.dragging)||void 0===n?void 0:n.node)&&!r.doc.eq(t.doc)&&this.updateDraggedNode(this.dragging,r),"reset"==u?this.dom.scrollTop=0:"to selection"==u?this.scrollToSelection():m&&function({refDOM:t,refTop:e,stack:n}){let r=t?t.getBoundingClientRect().top:0;B(n,0==r?0:r-e)}(m)}scrollToSelection(){let t=this.domSelectionRange().focusNode;if(this.someProp("handleScrollToSelection",(t=>t(this))));else if(this.state.selection instanceof r.nh){let e=this.docView.domAfterPos(this.state.selection.from);1==e.nodeType&&F(this,e.getBoundingClientRect(),t)}else F(this,this.coordsAtPos(this.state.selection.head,1),t)}destroyPluginViews(){let t;for(;t=this.pluginViews.pop();)t.destroy&&t.destroy()}updatePluginViews(t){if(t&&t.plugins==this.state.plugins&&this.directPlugins==this.prevDirectPlugins)for(let e=0;e<this.pluginViews.length;e++){let n=this.pluginViews[e];n.update&&n.update(this,t)}else{this.prevDirectPlugins=this.directPlugins,this.destroyPluginViews();for(let t=0;t<this.directPlugins.length;t++){let e=this.directPlugins[t];e.spec.view&&this.pluginViews.push(e.spec.view(this))}for(let t=0;t<this.state.plugins.length;t++){let e=this.state.plugins[t];e.spec.view&&this.pluginViews.push(e.spec.view(this))}}}updateDraggedNode(t,e){let n=t.node,o=-1;if(this.state.doc.nodeAt(n.from)==n.node)o=n.from;else{let t=n.from+(this.state.doc.content.size-e.doc.content.size);(t>0&&this.state.doc.nodeAt(t))==n.node&&(o=t)}this.dragging=new _e(t.slice,t.move,o<0?void 0:r.nh.create(this.state.doc,o))}someProp(t,e){let n,r=this._props&&this._props[t];if(null!=r&&(n=e?e(r):r))return n;for(let r=0;r<this.directPlugins.length;r++){let o=this.directPlugins[r].props[t];if(null!=o&&(n=e?e(o):o))return n}let o=this.state.plugins;if(o)for(let r=0;r<o.length;r++){let i=o[r].props[t];if(null!=i&&(n=e?e(i):i))return n}}hasFocus(){if(S){let t=this.root.activeElement;if(t==this.dom)return!0;if(!t||!this.dom.contains(t))return!1;for(;t&&this.dom!=t&&this.dom.contains(t);){if("false"==t.contentEditable)return!1;t=t.parentElement}return!0}return this.root.activeElement==this.dom}focus(){this.domObserver.stop(),this.editable&&function(t){if(t.setActive)return t.setActive();if(H)return t.focus(H);let e=j(t);t.focus(null==H?{get preventScroll(){return H={preventScroll:!0},!0}}:void 0),H||(H=!1,B(e,0))}(this.dom),Tt(this),this.domObserver.start()}get root(){let t=this._root;if(null==t)for(let t=this.dom.parentNode;t;t=t.parentNode)if(9==t.nodeType||11==t.nodeType&&t.host)return t.getSelection||(Object.getPrototypeOf(t).getSelection=()=>t.ownerDocument.getSelection()),this._root=t;return t||document}updateRoot(){this._root=null}posAtCoords(t){return J(this,t)}coordsAtPos(t,e=1){return G(this,t,e)}domAtPos(t,e=0){return this.docView.domFromPos(t,e)}nodeDOM(t){let e=this.docView.descAt(t);return e?e.nodeDOM:null}posAtDOM(t,e,n=-1){let r=this.docView.posFromDOM(t,e,n);if(null==r)throw new RangeError("DOM position not inside the editor");return r}endOfTextblock(t,e){return function(t,e,n){return et==e&&nt==n?rt:(et=e,nt=n,rt="up"==n||"down"==n?function(t,e,n){let r=e.selection,o="up"==n?r.$from:r.$to;return Q(t,e,(()=>{let{node:e}=t.docView.domFromPos(o.pos,"up"==n?-1:1);for(;;){let n=t.docView.nearestDesc(e,!0);if(!n)break;if(n.node.isBlock){e=n.contentDOM||n.dom;break}e=n.dom.parentNode}let r=G(t,o.pos,1);for(let t=e.firstChild;t;t=t.nextSibling){let e;if(1==t.nodeType)e=t.getClientRects();else{if(3!=t.nodeType)continue;e=c(t,0,t.nodeValue.length).getClientRects()}for(let t=0;t<e.length;t++){let o=e[t];if(o.bottom>o.top+1&&("up"==n?r.top-o.top>2*(o.bottom-r.top):o.bottom-r.bottom>2*(r.bottom-o.top)))return!1}}return!0}))}(t,e,n):function(t,e,n){let{$head:r}=e.selection;if(!r.parent.isTextblock)return!1;let o=r.parentOffset,i=!o,s=o==r.parent.content.size,a=t.domSelection();return tt.test(r.parent.textContent)&&a.modify?Q(t,e,(()=>{let{focusNode:e,focusOffset:o,anchorNode:i,anchorOffset:s}=t.domSelectionRange(),l=a.caretBidiLevel;a.modify("move",n,"character");let c=r.depth?t.docView.domAfterPos(r.before()):t.dom,{focusNode:d,focusOffset:u}=t.domSelectionRange(),h=d&&!c.contains(1==d.nodeType?d:d.parentNode)||e==d&&o==u;try{a.collapse(i,s),e&&(e!=i||o!=s)&&a.extend&&a.extend(e,o)}catch(t){}return null!=l&&(a.caretBidiLevel=l),h})):"left"==n||"backward"==n?i:s}(t,e,n))}(this,e||this.state,t)}pasteHTML(t,e){return Oe(this,"",t,!1,e||new ClipboardEvent("paste"))}pasteText(t,e){return Oe(this,t,null,!0,e||new ClipboardEvent("paste"))}destroy(){this.docView&&(function(t){t.domObserver.stop();for(let e in t.input.eventHandlers)t.dom.removeEventListener(e,t.input.eventHandlers[e]);clearTimeout(t.input.composingTimeout),clearTimeout(t.input.lastIOSEnterFallbackTimeout)}(this),this.destroyPluginViews(),this.mounted?(this.docView.update(this.state.doc,[],Ze(this),this),this.dom.textContent=""):this.dom.parentNode&&this.dom.parentNode.removeChild(this.dom),this.docView.destroy(),this.docView=null,l=null)}get isDestroyed(){return null==this.docView}dispatchEvent(t){return function(t,e){he(t,e)||!se[e.type]||!t.editable&&e.type in ae||se[e.type](t,e)}(this,t)}dispatch(t){let e=this._props.dispatchTransaction;e?e.call(this,t):this.updateState(this.state.apply(t))}domSelectionRange(){let t=this.domSelection();return E&&11===this.root.nodeType&&function(t){let e=t.activeElement;for(;e&&e.shadowRoot;)e=e.shadowRoot.activeElement;return e}(this.dom.ownerDocument)==this.dom&&function(t,e){if(e.getComposedRanges){let n=e.getComposedRanges(t.root)[0];if(n)return nn(t,n)}let n;function r(t){t.preventDefault(),t.stopImmediatePropagation(),n=t.getTargetRanges()[0]}return t.dom.addEventListener("beforeinput",r,!0),document.execCommand("indent"),t.dom.removeEventListener("beforeinput",r,!0),n?nn(t,n):null}(this,t)||t}domSelection(){return this.root.getSelection()}}function un(t){let e=Object.create(null);return e.class="ProseMirror",e.contenteditable=String(t.editable),t.someProp("attributes",(n=>{if("function"==typeof n&&(n=n(t.state)),n)for(let t in n)"class"==t?e.class+=" "+n[t]:"style"==t?e.style=(e.style?e.style+";":"")+n[t]:e[t]||"contenteditable"==t||"nodeName"==t||(e[t]=String(n[t]))})),e.translate||(e.translate="no"),[De.node(0,t.state.doc.content.size,e)]}function hn(t){if(t.markCursor){let e=document.createElement("img");e.className="ProseMirror-separator",e.setAttribute("mark-placeholder","true"),e.setAttribute("alt",""),t.cursorWrapper={dom:e,deco:De.widget(t.state.selection.head,e,{raw:!0,marks:t.markCursor})}}else t.cursorWrapper=null}function pn(t){return!t.someProp("editable",(e=>!1===e(t.state)))}function fn(t){let e=Object.create(null);function n(t){for(let n in t)Object.prototype.hasOwnProperty.call(e,n)||(e[n]=t[n])}return t.someProp("nodeViews",n),t.someProp("markViews",n),e}function mn(t){if(t.spec.state||t.spec.filterTransaction||t.spec.appendTransaction)throw new RangeError("Plugins passed directly to the view must not have a state component")}},1999:()=>{"use strict";"undefined"!=typeof window&&((window.__svelte??={}).v??=new Set).add("5")},921:(t,e,n)=>{"use strict";(0,n(9096).Ny)()},5266:(t,e,n)=>{"use strict";n(9328),n(9953)},5096:(t,e,n)=>{"use strict";n.d(e,{Gb0:()=>Mt,$ps:()=>Ot,sFi:()=>St,rvF:()=>qt,P0p:()=>Dt,VOE:()=>Vt,LJA:()=>Lt,kU:()=>Xt,nuC:()=>Kt,Umr:()=>At,ZQr:()=>Wt,vzu:()=>kt,L8f:()=>Nt,igI:()=>Jt,_mk:()=>Gt,hax:()=>Ht,$vM:()=>xt,Wc_:()=>Ut,G3x:()=>jt,W4S:()=>wt,L8w:()=>Rt,GPR:()=>Ct,xIx:()=>o,KA8:()=>ne,_Le:()=>ee,qmM:()=>se,Zm2:()=>ie,Ebs:()=>ae,m9B:()=>le,G8g:()=>oe,F7R:()=>te,Rle:()=>Qt,fIQ:()=>ce,emL:()=>re,o8B:()=>z,qgA:()=>it,EJS:()=>st,xLC:()=>at,bpZ:()=>lt,ZSL:()=>r});var r={};function o(t,e,n){function r(n,r){var o;Object.defineProperty(n,"_zod",{value:n._zod??{},enumerable:!1}),(o=n._zod).traits??(o.traits=new Set),n._zod.traits.add(t),e(n,r);for(const t in s.prototype)Object.defineProperty(n,t,{value:s.prototype[t].bind(n)});n._zod.constr=s,n._zod.def=r}const o=n?.Parent??Object;class i extends o{}function s(t){var e;const o=n?.Parent?new i:this;r(o,t),(e=o._zod).deferred??(e.deferred=[]);for(const t of o._zod.deferred)t();return o}return Object.defineProperty(i,"name",{value:t}),Object.defineProperty(s,"init",{value:r}),Object.defineProperty(s,Symbol.hasInstance,{value:e=>!!(n?.Parent&&e instanceof n.Parent)||e?._zod?.traits?.has(t)}),Object.defineProperty(s,"name",{value:t}),s}n.r(r),n.d(r,{BIGINT_FORMAT_RANGES:()=>j,Class:()=>et,NUMBER_FORMAT_RANGES:()=>F,aborted:()=>W,allowsEval:()=>T,assert:()=>h,assertEqual:()=>l,assertIs:()=>d,assertNever:()=>u,assertNotEqual:()=>c,assignProp:()=>x,cached:()=>g,cleanEnum:()=>tt,cleanRegex:()=>v,clone:()=>z,createTransparentProxy:()=>D,defineLazy:()=>w,esc:()=>C,escapeRegex:()=>R,extend:()=>V,finalizeIssue:()=>G,floatSafeRemainder:()=>b,getElementAtPath:()=>k,getLengthableOrigin:()=>X,getParsedType:()=>_,getSizableOrigin:()=>Y,getValidEnumValues:()=>p,isObject:()=>A,isPlainObject:()=>O,issue:()=>Q,joinValues:()=>f,jsonStringifyReplacer:()=>m,merge:()=>K,normalizeParams:()=>P,nullish:()=>y,numKeys:()=>E,omit:()=>H,optionalKeys:()=>I,partial:()=>U,pick:()=>B,prefixIssues:()=>q,primitiveTypes:()=>$,promiseAllObject:()=>S,propertyKeyTypes:()=>N,randomString:()=>M,required:()=>J,stringifyPrimitive:()=>L,unwrapMessage:()=>Z}),Symbol("zod_brand");class i extends Error{constructor(){super("Encountered Promise during synchronous parse. Use .parseAsync() instead.")}}const s={};function a(t){return t&&Object.assign(s,t),s}function l(t){return t}function c(t){return t}function d(t){}function u(t){throw new Error}function h(t){}function p(t){const e=Object.keys(t).filter((e=>"number"!=typeof t[t[e]])),n={};for(const r of e)n[r]=t[r];return Object.values(n)}function f(t,e="|"){return t.map((t=>L(t))).join(e)}function m(t,e){return"bigint"==typeof e?e.toString():e}function g(t){return{get value(){{const e=t();return Object.defineProperty(this,"value",{value:e}),e}}}}function y(t){return null==t}function v(t){const e=t.startsWith("^")?1:0,n=t.endsWith("$")?t.length-1:t.length;return t.slice(e,n)}function b(t,e){const n=(t.toString().split(".")[1]||"").length,r=(e.toString().split(".")[1]||"").length,o=n>r?n:r;return Number.parseInt(t.toFixed(o).replace(".",""))%Number.parseInt(e.toFixed(o).replace(".",""))/10**o}function w(t,e,n){Object.defineProperty(t,e,{get(){{const r=n();return t[e]=r,r}},set(n){Object.defineProperty(t,e,{value:n})},configurable:!0})}function x(t,e,n){Object.defineProperty(t,e,{value:n,writable:!0,enumerable:!0,configurable:!0})}function k(t,e){return e?e.reduce(((t,e)=>t?.[e]),t):t}function S(t){const e=Object.keys(t),n=e.map((e=>t[e]));return Promise.all(n).then((t=>{const n={};for(let r=0;r<e.length;r++)n[e[r]]=t[r];return n}))}function M(t=10){let e="";for(let n=0;n<t;n++)e+="abcdefghijklmnopqrstuvwxyz"[Math.floor(26*Math.random())];return e}function C(t){return JSON.stringify(t)}function A(t){return"object"==typeof t&&null!==t&&!Array.isArray(t)}const T=g((()=>{try{return new Function(""),!0}catch(t){return!1}}));function O(t){return"object"==typeof t&&null!==t&&Object.getPrototypeOf(t)===Object.prototype}function E(t){let e=0;for(const n in t)Object.prototype.hasOwnProperty.call(t,n)&&e++;return e}const _=t=>{const e=typeof t;switch(e){case"undefined":return"undefined";case"string":return"string";case"number":return Number.isNaN(t)?"nan":"number";case"boolean":return"boolean";case"function":return"function";case"bigint":return"bigint";case"symbol":return"symbol";case"object":return Array.isArray(t)?"array":null===t?"null":t.then&&"function"==typeof t.then&&t.catch&&"function"==typeof t.catch?"promise":"undefined"!=typeof Map&&t instanceof Map?"map":"undefined"!=typeof Set&&t instanceof Set?"set":"undefined"!=typeof Date&&t instanceof Date?"date":"undefined"!=typeof File&&t instanceof File?"file":"object";default:throw new Error(`Unknown data type: ${e}`)}},N=new Set(["string","number","symbol"]),$=new Set(["string","number","bigint","boolean","symbol","undefined"]);function R(t){return t.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}function z(t,e,n){const r=new t._zod.constr(e??t._zod.def);return e&&!n?.parent||(r._zod.parent=t),r}function P(t){const e=t;if(!e)return{};if("string"==typeof e)return{error:()=>e};if(void 0!==e?.message){if(void 0!==e?.error)throw new Error("Cannot specify both `message` and `error` params");e.error=e.message}return delete e.message,"string"==typeof e.error?{...e,error:()=>e.error}:e}function D(t){let e;return new Proxy({},{get:(n,r,o)=>(e??(e=t()),Reflect.get(e,r,o)),set:(n,r,o,i)=>(e??(e=t()),Reflect.set(e,r,o,i)),has:(n,r)=>(e??(e=t()),Reflect.has(e,r)),deleteProperty:(n,r)=>(e??(e=t()),Reflect.deleteProperty(e,r)),ownKeys:n=>(e??(e=t()),Reflect.ownKeys(e)),getOwnPropertyDescriptor:(n,r)=>(e??(e=t()),Reflect.getOwnPropertyDescriptor(e,r)),defineProperty:(n,r,o)=>(e??(e=t()),Reflect.defineProperty(e,r,o))})}function L(t){return"bigint"==typeof t?t.toString()+"n":"string"==typeof t?`"${t}"`:`${t}`}function I(t){return Object.keys(t).filter((e=>"optional"===t[e]._zod.optin&&"optional"===t[e]._zod.optout))}const F={safeint:[Number.MIN_SAFE_INTEGER,Number.MAX_SAFE_INTEGER],int32:[-2147483648,2147483647],uint32:[0,4294967295],float32:[-34028234663852886e22,34028234663852886e22],float64:[-Number.MAX_VALUE,Number.MAX_VALUE]},j={int64:[BigInt("-9223372036854775808"),BigInt("9223372036854775807")],uint64:[BigInt(0),BigInt("18446744073709551615")]};function B(t,e){const n={},r=t._zod.def;for(const t in e){if(!(t in r.shape))throw new Error(`Unrecognized key: "${t}"`);e[t]&&(n[t]=r.shape[t])}return z(t,{...t._zod.def,shape:n,checks:[]})}function H(t,e){const n={...t._zod.def.shape},r=t._zod.def;for(const t in e){if(!(t in r.shape))throw new Error(`Unrecognized key: "${t}"`);e[t]&&delete n[t]}return z(t,{...t._zod.def,shape:n,checks:[]})}function V(t,e){const n={...t._zod.def,get shape(){const n={...t._zod.def.shape,...e};return x(this,"shape",n),n},checks:[]};return z(t,n)}function K(t,e){return z(t,{...t._zod.def,get shape(){const n={...t._zod.def.shape,...e._zod.def.shape};return x(this,"shape",n),n},catchall:e._zod.def.catchall,checks:[]})}function U(t,e,n){const r=e._zod.def.shape,o={...r};if(n)for(const e in n){if(!(e in r))throw new Error(`Unrecognized key: "${e}"`);n[e]&&(o[e]=t?new t({type:"optional",innerType:r[e]}):r[e])}else for(const e in r)o[e]=t?new t({type:"optional",innerType:r[e]}):r[e];return z(e,{...e._zod.def,shape:o,checks:[]})}function J(t,e,n){const r=e._zod.def.shape,o={...r};if(n)for(const e in n){if(!(e in o))throw new Error(`Unrecognized key: "${e}"`);n[e]&&(o[e]=new t({type:"nonoptional",innerType:r[e]}))}else for(const e in r)o[e]=new t({type:"nonoptional",innerType:r[e]});return z(e,{...e._zod.def,shape:o,checks:[]})}function W(t,e=0){for(let n=e;n<t.issues.length;n++)if(!0!==t.issues[n].continue)return!0;return!1}function q(t,e){return e.map((e=>{var n;return(n=e).path??(n.path=[]),e.path.unshift(t),e}))}function Z(t){return"string"==typeof t?t:t?.message}function G(t,e,n){const r={...t,path:t.path??[]};if(!t.message){const o=Z(t.inst?._zod.def?.error?.(t))??Z(e?.error?.(t))??Z(n.customError?.(t))??Z(n.localeError?.(t))??"Invalid input";r.message=o}return delete r.inst,delete r.continue,e?.reportInput||delete r.input,r}function Y(t){return t instanceof Set?"set":t instanceof Map?"map":t instanceof File?"file":"unknown"}function X(t){return Array.isArray(t)?"array":"string"==typeof t?"string":"unknown"}function Q(...t){const[e,n,r]=t;return"string"==typeof e?{message:e,code:"custom",input:n,inst:r}:{...e}}function tt(t){return Object.entries(t).filter((([t,e])=>Number.isNaN(Number.parseInt(t,10)))).map((t=>t[1]))}class et{constructor(...t){}}const nt=(t,e)=>{t.name="$ZodError",Object.defineProperty(t,"_zod",{value:t._zod,enumerable:!1}),Object.defineProperty(t,"issues",{value:e,enumerable:!1}),Object.defineProperty(t,"message",{get:()=>JSON.stringify(e,m,2),enumerable:!0})},rt=o("$ZodError",nt),ot=o("$ZodError",nt,{Parent:Error}),it=(t=>(e,n,r,o)=>{const s=r?Object.assign(r,{async:!1}):{async:!1},l=e._zod.run({value:n,issues:[]},s);if(l instanceof Promise)throw new i;if(l.issues.length){const e=new(o?.Err??t)(l.issues.map((t=>G(t,s,a()))));throw Error.captureStackTrace(e,o?.callee),e}return l.value})(ot),st=(t=>async(e,n,r,o)=>{const i=r?Object.assign(r,{async:!0}):{async:!0};let s=e._zod.run({value:n,issues:[]},i);if(s instanceof Promise&&(s=await s),s.issues.length){const e=new(o?.Err??t)(s.issues.map((t=>G(t,i,a()))));throw Error.captureStackTrace(e,o?.callee),e}return s.value})(ot),at=(t=>(e,n,r)=>{const o=r?{...r,async:!1}:{async:!1},s=e._zod.run({value:n,issues:[]},o);if(s instanceof Promise)throw new i;return s.issues.length?{success:!1,error:new(t??rt)(s.issues.map((t=>G(t,o,a()))))}:{success:!0,data:s.value}})(ot),lt=(t=>async(e,n,r)=>{const o=r?Object.assign(r,{async:!0}):{async:!0};let i=e._zod.run({value:n,issues:[]},o);return i instanceof Promise&&(i=await i),i.issues.length?{success:!1,error:new t(i.issues.map((t=>G(t,o,a()))))}:{success:!0,data:i.value}})(ot),ct=t=>t?new RegExp(`^([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-${t}[0-9a-fA-F]{3}-[89abAB][0-9a-fA-F]{3}-[0-9a-fA-F]{12})$`):/^([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[1-8][0-9a-fA-F]{3}-[89abAB][0-9a-fA-F]{3}-[0-9a-fA-F]{12}|00000000-0000-0000-0000-000000000000)$/;ct(4),ct(6),ct(7),new RegExp(`(${/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/.source})|(${/^(([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}|::|([0-9a-fA-F]{1,4})?::([0-9a-fA-F]{1,4}:?){0,6})$/.source})`);new RegExp("^((\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-((0[13578]|1[02])-(0[1-9]|[12]\\d|3[01])|(0[469]|11)-(0[1-9]|[12]\\d|30)|(02)-(0[1-9]|1\\d|2[0-8])))$");const dt=/^-?\d+(?:\.\d+)?/i,ut=/true|false/i,ht=o("$ZodCheck",((t,e)=>{var n;t._zod??(t._zod={}),t._zod.def=e,(n=t._zod).onattach??(n.onattach=[])})),pt={number:"number",bigint:"bigint",object:"date"},ft=o("$ZodCheckLessThan",((t,e)=>{ht.init(t,e);const n=pt[typeof e.value];t._zod.onattach.push((t=>{const n=t._zod.bag,r=(e.inclusive?n.maximum:n.exclusiveMaximum)??Number.POSITIVE_INFINITY;e.value<r&&(e.inclusive?n.maximum=e.value:n.exclusiveMaximum=e.value)})),t._zod.check=r=>{(e.inclusive?r.value<=e.value:r.value<e.value)||r.issues.push({origin:n,code:"too_big",maximum:e.value,input:r.value,inclusive:e.inclusive,inst:t,continue:!e.abort})}})),mt=o("$ZodCheckGreaterThan",((t,e)=>{ht.init(t,e);const n=pt[typeof e.value];t._zod.onattach.push((t=>{const n=t._zod.bag,r=(e.inclusive?n.minimum:n.exclusiveMinimum)??Number.NEGATIVE_INFINITY;e.value>r&&(e.inclusive?n.minimum=e.value:n.exclusiveMinimum=e.value)})),t._zod.check=r=>{(e.inclusive?r.value>=e.value:r.value>e.value)||r.issues.push({origin:n,code:"too_small",minimum:e.value,input:r.value,inclusive:e.inclusive,inst:t,continue:!e.abort})}})),gt=o("$ZodCheckMaxLength",((t,e)=>{ht.init(t,e),t._zod.when=t=>{const e=t.value;return!y(e)&&void 0!==e.length},t._zod.onattach.push((t=>{const n=t._zod.bag.maximum??Number.POSITIVE_INFINITY;e.maximum<n&&(t._zod.bag.maximum=e.maximum)})),t._zod.check=n=>{const r=n.value;if(r.length<=e.maximum)return;const o=X(r);n.issues.push({origin:o,code:"too_big",maximum:e.maximum,input:r,inst:t,continue:!e.abort})}})),yt=o("$ZodCheckMinLength",((t,e)=>{ht.init(t,e),t._zod.when=t=>{const e=t.value;return!y(e)&&void 0!==e.length},t._zod.onattach.push((t=>{const n=t._zod.bag.minimum??Number.NEGATIVE_INFINITY;e.minimum>n&&(t._zod.bag.minimum=e.minimum)})),t._zod.check=n=>{const r=n.value;if(r.length>=e.minimum)return;const o=X(r);n.issues.push({origin:o,code:"too_small",minimum:e.minimum,input:r,inst:t,continue:!e.abort})}}));class vt{constructor(t=[]){this.content=[],this.indent=0,this&&(this.args=t)}indented(t){this.indent+=1,t(this),this.indent-=1}write(t){if("function"==typeof t)return t(this,{execution:"sync"}),void t(this,{execution:"async"});const e=t.split("\n").filter((t=>t)),n=Math.min(...e.map((t=>t.length-t.trimStart().length))),r=e.map((t=>t.slice(n))).map((t=>" ".repeat(2*this.indent)+t));for(const t of r)this.content.push(t)}compile(){const t=Function,e=this?.args;return new t(...e,[...(this?.content??[""]).map((t=>`  ${t}`))].join("\n"))}}const bt={major:4,minor:0,patch:0},wt=o("$ZodType",((t,e)=>{var n;t??(t={}),t._zod.id=e.type+"_"+M(10),t._zod.def=e,t._zod.bag=t._zod.bag||{},t._zod.version=bt;const r=[...t._zod.def.checks??[]];t._zod.traits.has("$ZodCheck")&&r.unshift(t);for(const e of r)for(const n of e._zod.onattach)n(t);if(0===r.length)(n=t._zod).deferred??(n.deferred=[]),t._zod.deferred?.push((()=>{t._zod.run=t._zod.parse}));else{const e=(t,e,n)=>{let r,o=W(t);for(const s of e){if(s._zod.when){if(!s._zod.when(t))continue}else if(o)continue;const e=t.issues.length,a=s._zod.check(t);if(a instanceof Promise&&!1===n?.async)throw new i;if(r||a instanceof Promise)r=(r??Promise.resolve()).then((async()=>{await a,t.issues.length!==e&&(o||(o=W(t,e)))}));else{if(t.issues.length===e)continue;o||(o=W(t,e))}}return r?r.then((()=>t)):t};t._zod.run=(n,o)=>{const s=t._zod.parse(n,o);if(s instanceof Promise){if(!1===o.async)throw new i;return s.then((t=>e(t,r,o)))}return e(s,r,o)}}t["~standard"]={validate:e=>{try{const n=at(t,e);return n.success?{value:n.data}:{issues:n.error?.issues}}catch(n){return lt(t,e).then((t=>t.success?{value:t.data}:{issues:t.error?.issues}))}},vendor:"zod",version:1}})),xt=o("$ZodString",((t,e)=>{var n;wt.init(t,e),t._zod.pattern=[...t?._zod.bag?.patterns??[]].pop()??(n=t._zod.bag,new RegExp(`^${n?`[\\s\\S]{${n?.minimum??0},${n?.maximum??""}}`:"[\\s\\S]*"}$`)),t._zod.parse=(n,r)=>{if(e.coerce)try{n.value=String(n.value)}catch(r){}return"string"==typeof n.value||n.issues.push({expected:"string",code:"invalid_type",input:n.value,inst:t}),n}})),kt=o("$ZodNumber",((t,e)=>{wt.init(t,e),t._zod.pattern=t._zod.bag.pattern??dt,t._zod.parse=(n,r)=>{if(e.coerce)try{n.value=Number(n.value)}catch(t){}const o=n.value;if("number"==typeof o&&!Number.isNaN(o)&&Number.isFinite(o))return n;const i="number"==typeof o?Number.isNaN(o)?"NaN":Number.isFinite(o)?void 0:"Infinity":void 0;return n.issues.push({expected:"number",code:"invalid_type",input:o,inst:t,...i?{received:i}:{}}),n}})),St=o("$ZodBoolean",((t,e)=>{wt.init(t,e),t._zod.pattern=ut,t._zod.parse=(n,r)=>{if(e.coerce)try{n.value=Boolean(n.value)}catch(t){}const o=n.value;return"boolean"==typeof o||n.issues.push({expected:"boolean",code:"invalid_type",input:o,inst:t}),n}})),Mt=o("$ZodAny",((t,e)=>{wt.init(t,e),t._zod.parse=t=>t})),Ct=o("$ZodUnknown",((t,e)=>{wt.init(t,e),t._zod.parse=t=>t})),At=o("$ZodNever",((t,e)=>{wt.init(t,e),t._zod.parse=(e,n)=>(e.issues.push({expected:"never",code:"invalid_type",input:e.value,inst:t}),e)}));function Tt(t,e,n){t.issues.length&&e.issues.push(...q(n,t.issues)),e.value[n]=t.value}const Ot=o("$ZodArray",((t,e)=>{wt.init(t,e),t._zod.parse=(n,r)=>{const o=n.value;if(!Array.isArray(o))return n.issues.push({expected:"array",code:"invalid_type",input:o,inst:t}),n;n.value=Array(o.length);const i=[];for(let t=0;t<o.length;t++){const s=o[t],a=e.element._zod.run({value:s,issues:[]},r);a instanceof Promise?i.push(a.then((e=>Tt(e,n,t)))):Tt(a,n,t)}return i.length?Promise.all(i).then((()=>n)):n}}));function Et(t,e,n){t.issues.length&&e.issues.push(...q(n,t.issues)),e.value[n]=t.value}function _t(t,e,n,r){t.issues.length?void 0===r[n]?e.value[n]=n in r?void 0:t.value:e.issues.push(...q(n,t.issues)):void 0===t.value?n in r&&(e.value[n]=void 0):e.value[n]=t.value}const Nt=o("$ZodObject",((t,e)=>{wt.init(t,e);const n=g((()=>{const t=Object.keys(e.shape),n=I(e.shape);return{shape:e.shape,keys:t,keySet:new Set(t),numKeys:t.length,optionalKeys:new Set(n)}}));let r;w(t._zod,"disc",(()=>{const t=e.shape,n=new Map;let r=!1;for(const e in t){const o=t[e]._zod;if(o.values||o.disc){r=!0;const t={values:new Set(o.values??[]),maps:o.disc?[o.disc]:[]};n.set(e,t)}}if(r)return n}));const o=A,i=!s.jitless,a=i&&T.value,{catchall:l}=e;let c;t._zod.parse=(s,d)=>{c??(c=n.value);const u=s.value;if(!o(u))return s.issues.push({expected:"object",code:"invalid_type",input:u,inst:t}),s;const h=[];if(i&&a&&!1===d?.async&&!0!==d.jitless)r||(r=(t=>{const e=new vt(["shape","payload","ctx"]),{keys:r,optionalKeys:o}=n.value,i=t=>{const e=C(t);return`shape[${e}]._zod.run({ value: input[${e}], issues: [] }, ctx)`};e.write("const input = payload.value;");const s=Object.create(null);for(const t of r)s[t]=M(15);e.write("const newResult = {}");for(const t of r)if(o.has(t)){const n=s[t];e.write(`const ${n} = ${i(t)};`);const r=C(t);e.write(`\n        if (${n}.issues.length) {\n          if (input[${r}] === undefined) {\n            if (${r} in input) {\n              newResult[${r}] = undefined;\n            }\n          } else {\n            payload.issues = payload.issues.concat(\n              ${n}.issues.map((iss) => ({\n                ...iss,\n                path: iss.path ? [${r}, ...iss.path] : [${r}],\n              }))\n            );\n          }\n        } else if (${n}.value === undefined) {\n          if (${r} in input) newResult[${r}] = undefined;\n        } else {\n          newResult[${r}] = ${n}.value;\n        }\n        `)}else{const n=s[t];e.write(`const ${n} = ${i(t)};`),e.write(`\n          if (${n}.issues.length) payload.issues = payload.issues.concat(${n}.issues.map(iss => ({\n            ...iss,\n            path: iss.path ? [${C(t)}, ...iss.path] : [${C(t)}]\n          })));`),e.write(`newResult[${C(t)}] = ${n}.value`)}e.write("payload.value = newResult;"),e.write("return payload;");const a=e.compile();return(e,n)=>a(t,e,n)})(e.shape)),s=r(s,d);else{s.value={};const t=c.shape;for(const e of c.keys){const n=t[e],r=n._zod.run({value:u[e],issues:[]},d),o="optional"===n._zod.optin&&"optional"===n._zod.optout;r instanceof Promise?h.push(r.then((t=>o?_t(t,s,e,u):Et(t,s,e)))):o?_t(r,s,e,u):Et(r,s,e)}}if(!l)return h.length?Promise.all(h).then((()=>s)):s;const p=[],f=c.keySet,m=l._zod,g=m.def.type;for(const t of Object.keys(u)){if(f.has(t))continue;if("never"===g){p.push(t);continue}const e=m.run({value:u[t],issues:[]},d);e instanceof Promise?h.push(e.then((e=>Et(e,s,t)))):Et(e,s,t)}return p.length&&s.issues.push({code:"unrecognized_keys",keys:p,input:u,inst:t}),h.length?Promise.all(h).then((()=>s)):s}}));function $t(t,e,n,r){for(const n of t)if(0===n.issues.length)return e.value=n.value,e;return e.issues.push({code:"invalid_union",input:e.value,inst:n,errors:t.map((t=>t.issues.map((t=>G(t,r,a())))))}),e}const Rt=o("$ZodUnion",((t,e)=>{wt.init(t,e),w(t._zod,"values",(()=>{if(e.options.every((t=>t._zod.values)))return new Set(e.options.flatMap((t=>Array.from(t._zod.values))))})),w(t._zod,"pattern",(()=>{if(e.options.every((t=>t._zod.pattern))){const t=e.options.map((t=>t._zod.pattern));return new RegExp(`^(${t.map((t=>v(t.source))).join("|")})$`)}})),t._zod.parse=(n,r)=>{let o=!1;const i=[];for(const t of e.options){const e=t._zod.run({value:n.value,issues:[]},r);if(e instanceof Promise)i.push(e),o=!0;else{if(0===e.issues.length)return e;i.push(e)}}return o?Promise.all(i).then((e=>$t(e,n,t,r))):$t(i,n,t,r)}}));function zt(t,e,n){let r=!0;const o=t?.[e];if(n.values.size&&!n.values.has(o)&&(r=!1),n.maps.length>0)for(const t of n.maps)Pt(o,t)||(r=!1);return r}function Pt(t,e){let n=!0;for(const[r,o]of e)zt(t,r,o)||(n=!1);return n}const Dt=o("$ZodDiscriminatedUnion",((t,e)=>{Rt.init(t,e);const n=t._zod.parse;w(t._zod,"disc",(()=>{const t=new Map;for(const n of e.options){const r=n._zod.disc;if(!r)throw new Error(`Invalid discriminated union option at index "${e.options.indexOf(n)}"`);for(const[e,n]of r){t.has(e)||t.set(e,{values:new Set,maps:[]});const r=t.get(e);for(const t of n.values)r.values.add(t);for(const t of n.maps)r.maps.push(t)}}return t}));const r=g((()=>{const t=new Map;for(const n of e.options){const r=n._zod.disc?.get(e.discriminator);if(!r)throw new Error("Invalid discriminated union option");t.set(n,r)}return t}));t._zod.parse=(o,i)=>{const s=o.value;if(!A(s))return o.issues.push({code:"invalid_type",expected:"object",input:s,inst:t}),o;const a=[],l=r.value;for(const t of e.options){const n=l.get(t);zt(s,e.discriminator,n)&&a.push(t)}return 1===a.length?a[0]._zod.run(o,i):e.unionFallback?n(o,i):(o.issues.push({code:"invalid_union",errors:[],note:"No matching discriminator",input:s,path:[e.discriminator],inst:t}),o)}})),Lt=o("$ZodIntersection",((t,e)=>{wt.init(t,e),t._zod.parse=(t,n)=>{const{value:r}=t,o=e.left._zod.run({value:r,issues:[]},n),i=e.right._zod.run({value:r,issues:[]},n);return o instanceof Promise||i instanceof Promise?Promise.all([o,i]).then((([e,n])=>Ft(t,e,n))):Ft(t,o,i)}}));function It(t,e){if(t===e)return{valid:!0,data:t};if(t instanceof Date&&e instanceof Date&&+t==+e)return{valid:!0,data:t};if(O(t)&&O(e)){const n=Object.keys(e),r=Object.keys(t).filter((t=>-1!==n.indexOf(t))),o={...t,...e};for(const n of r){const r=It(t[n],e[n]);if(!r.valid)return{valid:!1,mergeErrorPath:[n,...r.mergeErrorPath]};o[n]=r.data}return{valid:!0,data:o}}if(Array.isArray(t)&&Array.isArray(e)){if(t.length!==e.length)return{valid:!1,mergeErrorPath:[]};const n=[];for(let r=0;r<t.length;r++){const o=It(t[r],e[r]);if(!o.valid)return{valid:!1,mergeErrorPath:[r,...o.mergeErrorPath]};n.push(o.data)}return{valid:!0,data:n}}return{valid:!1,mergeErrorPath:[]}}function Ft(t,e,n){if(e.issues.length&&t.issues.push(...e.issues),n.issues.length&&t.issues.push(...n.issues),W(t))return t;const r=It(e.value,n.value);if(!r.valid)throw new Error(`Unmergable intersection. Error path: ${JSON.stringify(r.mergeErrorPath)}`);return t.value=r.data,t}const jt=o("$ZodTuple",((t,e)=>{wt.init(t,e);const n=e.items,r=n.length-[...n].reverse().findIndex((t=>"optional"!==t._zod.optin));t._zod.parse=(o,i)=>{const s=o.value;if(!Array.isArray(s))return o.issues.push({input:s,inst:t,expected:"tuple",code:"invalid_type"}),o;o.value=[];const a=[];if(!e.rest){const e=s.length>n.length,i=s.length<r-1;if(e||i)return o.issues.push({input:s,inst:t,origin:"array",...e?{code:"too_big",maximum:n.length}:{code:"too_small",minimum:n.length}}),o}let l=-1;for(const t of n){if(l++,l>=s.length&&l>=r)continue;const e=t._zod.run({value:s[l],issues:[]},i);e instanceof Promise?a.push(e.then((t=>Bt(t,o,l)))):Bt(e,o,l)}if(e.rest){const t=s.slice(n.length);for(const n of t){l++;const t=e.rest._zod.run({value:n,issues:[]},i);t instanceof Promise?a.push(t.then((t=>Bt(t,o,l)))):Bt(t,o,l)}}return a.length?Promise.all(a).then((()=>o)):o}}));function Bt(t,e,n){t.issues.length&&e.issues.push(...q(n,t.issues)),e.value[n]=t.value}const Ht=o("$ZodRecord",((t,e)=>{wt.init(t,e),t._zod.parse=(n,r)=>{const o=n.value;if(!O(o))return n.issues.push({expected:"record",code:"invalid_type",input:o,inst:t}),n;const i=[];if(e.keyType._zod.values){const s=e.keyType._zod.values;n.value={};for(const t of s)if("string"==typeof t||"number"==typeof t||"symbol"==typeof t){const s=e.valueType._zod.run({value:o[t],issues:[]},r);s instanceof Promise?i.push(s.then((e=>{e.issues.length&&n.issues.push(...q(t,e.issues)),n.value[t]=e.value}))):(s.issues.length&&n.issues.push(...q(t,s.issues)),n.value[t]=s.value)}let a;for(const t in o)s.has(t)||(a=a??[],a.push(t));a&&a.length>0&&n.issues.push({code:"unrecognized_keys",input:o,inst:t,keys:a})}else{n.value={};for(const s of Reflect.ownKeys(o)){if("__proto__"===s)continue;const l=e.keyType._zod.run({value:s,issues:[]},r);if(l instanceof Promise)throw new Error("Async schemas not supported in object keys currently");if(l.issues.length){n.issues.push({origin:"record",code:"invalid_key",issues:l.issues.map((t=>G(t,r,a()))),input:s,path:[s],inst:t}),n.value[l.value]=l.value;continue}const c=e.valueType._zod.run({value:o[s],issues:[]},r);c instanceof Promise?i.push(c.then((t=>{t.issues.length&&n.issues.push(...q(s,t.issues)),n.value[l.value]=t.value}))):(c.issues.length&&n.issues.push(...q(s,c.issues)),n.value[l.value]=c.value)}}return i.length?Promise.all(i).then((()=>n)):n}})),Vt=o("$ZodEnum",((t,e)=>{wt.init(t,e);const n=Object.values(e.entries).filter((t=>"number"==typeof t)),r=Object.entries(e.entries).filter((([t,e])=>-1===n.indexOf(+t))).map((([t,e])=>e));t._zod.values=new Set(r),t._zod.pattern=new RegExp(`^(${r.filter((t=>N.has(typeof t))).map((t=>"string"==typeof t?R(t):t.toString())).join("|")})$`),t._zod.parse=(e,n)=>{const o=e.value;return t._zod.values.has(o)||e.issues.push({code:"invalid_value",values:r,input:o,inst:t}),e}})),Kt=o("$ZodLiteral",((t,e)=>{wt.init(t,e),t._zod.values=new Set(e.values),t._zod.pattern=new RegExp(`^(${e.values.map((t=>"string"==typeof t?R(t):t?t.toString():String(t))).join("|")})$`),t._zod.parse=(n,r)=>{const o=n.value;return t._zod.values.has(o)||n.issues.push({code:"invalid_value",values:e.values,input:o,inst:t}),n}})),Ut=o("$ZodTransform",((t,e)=>{wt.init(t,e),t._zod.parse=(t,n)=>{const r=e.transform(t.value,t);if(n.async)return(r instanceof Promise?r:Promise.resolve(r)).then((e=>(t.value=e,t)));if(r instanceof Promise)throw new i;return t.value=r,t}})),Jt=o("$ZodOptional",((t,e)=>{wt.init(t,e),t._zod.optin="optional",t._zod.optout="optional",w(t._zod,"values",(()=>e.innerType._zod.values?new Set([...e.innerType._zod.values,void 0]):void 0)),w(t._zod,"pattern",(()=>{const t=e.innerType._zod.pattern;return t?new RegExp(`^(${v(t.source)})?$`):void 0})),t._zod.parse=(t,n)=>void 0===t.value?t:e.innerType._zod.run(t,n)})),Wt=o("$ZodNullable",((t,e)=>{wt.init(t,e),w(t._zod,"optin",(()=>e.innerType._zod.optin)),w(t._zod,"optout",(()=>e.innerType._zod.optout)),w(t._zod,"pattern",(()=>{const t=e.innerType._zod.pattern;return t?new RegExp(`^(${v(t.source)}|null)$`):void 0})),w(t._zod,"values",(()=>e.innerType._zod.values?new Set([...e.innerType._zod.values,null]):void 0)),t._zod.parse=(t,n)=>null===t.value?t:e.innerType._zod.run(t,n)})),qt=o("$ZodDefault",((t,e)=>{wt.init(t,e),t._zod.optin="optional",w(t._zod,"values",(()=>e.innerType._zod.values)),t._zod.parse=(t,n)=>{if(void 0===t.value)return t.value=e.defaultValue,t;const r=e.innerType._zod.run(t,n);return r instanceof Promise?r.then((t=>Zt(t,e))):Zt(r,e)}}));function Zt(t,e){return void 0===t.value&&(t.value=e.defaultValue),t}const Gt=o("$ZodPipe",((t,e)=>{wt.init(t,e),w(t._zod,"values",(()=>e.in._zod.values)),w(t._zod,"optin",(()=>e.in._zod.optin)),w(t._zod,"optout",(()=>e.out._zod.optout)),t._zod.parse=(t,n)=>{const r=e.in._zod.run(t,n);return r instanceof Promise?r.then((t=>Yt(t,e,n))):Yt(r,e,n)}}));function Yt(t,e,n){return W(t)?t:e.out._zod.run({value:t.value,issues:t.issues},n)}const Xt=o("$ZodLazy",((t,e)=>{wt.init(t,e),w(t._zod,"innerType",(()=>e.getter())),w(t._zod,"pattern",(()=>t._zod.innerType._zod.pattern)),w(t._zod,"disc",(()=>t._zod.innerType._zod.disc)),w(t._zod,"optin",(()=>t._zod.innerType._zod.optin)),w(t._zod,"optout",(()=>t._zod.innerType._zod.optout)),t._zod.parse=(e,n)=>t._zod.innerType._zod.run(e,n)}));function Qt(t,e){return new t({type:"string",...P(e)})}function te(t,e){return new t({type:"number",checks:[],...P(e)})}function ee(t,e){return new t({type:"boolean",...P(e)})}function ne(t){return new t({type:"any"})}function re(t){return new t({type:"unknown"})}function oe(t,e){return new t({type:"never",...P(e)})}function ie(t,e){return new ft({check:"less_than",...P(e),value:t,inclusive:!0})}function se(t,e){return new mt({check:"greater_than",...P(e),value:t,inclusive:!0})}function ae(t,e){return new gt({check:"max_length",...P(e),maximum:t})}function le(t,e){return new yt({check:"min_length",...P(e),minimum:t})}function ce(t,e){const{case:n,error:r,truthy:o,falsy:i}=P(e),s=new Set(o??["true","1","yes","on","y","enabled"]),a=new Set(i??["false","0","no","off","n","disabled"]),l=t.Pipe??Gt,c=t.Boolean??St,d=new(t.Unknown??Ct)({type:"unknown",checks:[{_zod:{check:t=>{if("string"==typeof t.value){let e=t.value;"sensitive"!==n&&(e=e.toLowerCase()),s.has(e)?t.value=!0:a.has(e)?t.value=!1:t.issues.push({code:"invalid_value",expected:"stringbool",values:[...s,...a],input:t.value,inst:d})}else t.issues.push({code:"invalid_type",expected:"string",input:t.value})},def:{check:"custom"},onattach:[]}}],error:r});return new l({type:"pipe",in:d,out:new c({type:"boolean",error:r}),error:r})}Symbol("ZodOutput"),Symbol("ZodInput")},4580:(t,e,n)=>{"use strict";n.d(e,{Rvf:()=>K,bzn:()=>h,YOg:()=>v,zMY:()=>d,gMt:()=>A,k5n:()=>z,X$i:()=>x,ROM:()=>r.qmM,E$q:()=>O,RZV:()=>q,euz:()=>D,wJL:()=>r.Zm2,Ru6:()=>r.Ebs,BpQ:()=>r.m9B,ZmZ:()=>g,mee:()=>H,aig:()=>l,Ikc:()=>w,lqM:()=>j,OH3:()=>k,FsL:()=>J,g1P:()=>$,YjP:()=>s,pdi:()=>I,PVZ:()=>_,KCZ:()=>M,L5J:()=>f});var r=n(5096);const o=r.xIx("ZodMiniType",((t,e)=>{if(!t._zod)throw new Error("Uninitialized schema in mixin ZodMiniType.");r.W4S.init(t,e),t.def=e,t.parse=(e,n)=>r.qgA(t,e,n,{callee:t.parse}),t.safeParse=(e,n)=>r.xLC(t,e,n),t.parseAsync=async(e,n)=>r.EJS(t,e,n,{callee:t.parseAsync}),t.safeParseAsync=async(e,n)=>r.bpZ(t,e,n),t.check=(...n)=>t.clone({...e,checks:[...e.checks??[],...n.map((t=>"function"==typeof t?{_zod:{check:t,def:{check:"custom"},onattach:[]}}:t))]}),t.clone=(e,n)=>r.o8B(t,e,n),t.brand=()=>t,t.register=(e,n)=>(e.add(t,n),t)})),i=r.xIx("ZodMiniString",((t,e)=>{r.$vM.init(t,e),o.init(t,e)}));function s(t){return r.Rle(i,t)}const a=r.xIx("ZodMiniNumber",((t,e)=>{r.vzu.init(t,e),o.init(t,e)}));function l(t){return r.F7R(a,t)}const c=r.xIx("ZodMiniBoolean",((t,e)=>{r.sFi.init(t,e),o.init(t,e)}));function d(t){return r._Le(c,t)}const u=r.xIx("ZodMiniAny",((t,e)=>{r.Gb0.init(t,e),o.init(t,e)}));function h(){return r.KA8(u)}const p=r.xIx("ZodMiniUnknown",((t,e)=>{r.GPR.init(t,e),o.init(t,e)}));function f(){return r.emL(p)}const m=r.xIx("ZodMiniNever",((t,e)=>{r.Umr.init(t,e),o.init(t,e)}));function g(t){return r.G8g(m,t)}const y=r.xIx("ZodMiniArray",((t,e)=>{r.$ps.init(t,e),o.init(t,e)}));function v(t,e){return new y({type:"array",element:t,...r.ZSL.normalizeParams(e)})}const b=r.xIx("ZodMiniObject",((t,e)=>{r.L8f.init(t,e),o.init(t,e)}));function w(t,e){const n={type:"object",get shape(){return r.ZSL.assignProp(this,"shape",{...t}),this.shape},...r.ZSL.normalizeParams(e)};return new b(n)}function x(t,e){return r.ZSL.extend(t,e)}function k(t,e){return r.ZSL.partial(F,t,e)}const S=r.xIx("ZodMiniUnion",((t,e)=>{r.L8w.init(t,e),o.init(t,e)}));function M(t,e){return new S({type:"union",options:t,...r.ZSL.normalizeParams(e)})}const C=r.xIx("ZodMiniDiscriminatedUnion",((t,e)=>{r.P0p.init(t,e),o.init(t,e)}));function A(t,e,n){return new C({type:"union",options:e,discriminator:t,...r.ZSL.normalizeParams(n)})}const T=r.xIx("ZodMiniIntersection",((t,e)=>{r.LJA.init(t,e),o.init(t,e)}));function O(t,e){return new T({type:"intersection",left:t,right:e})}const E=r.xIx("ZodMiniTuple",((t,e)=>{r.G3x.init(t,e),o.init(t,e)}));function _(t,e,n){const o=e instanceof r.W4S,i=o?n:e;return new E({type:"tuple",items:t,rest:o?e:null,...r.ZSL.normalizeParams(i)})}const N=r.xIx("ZodMiniRecord",((t,e)=>{r.hax.init(t,e),o.init(t,e)}));function $(t,e,n){return new N({type:"record",keyType:t,valueType:e,...r.ZSL.normalizeParams(n)})}const R=r.xIx("ZodMiniEnum",((t,e)=>{r.VOE.init(t,e),o.init(t,e)}));function z(t,e){const n=Array.isArray(t)?Object.fromEntries(t.map((t=>[t,t]))):t;return new R({type:"enum",entries:n,...r.ZSL.normalizeParams(e)})}const P=r.xIx("ZodMiniLiteral",((t,e)=>{r.nuC.init(t,e),o.init(t,e)}));function D(t,e){return new P({type:"literal",values:Array.isArray(t)?t:[t],...r.ZSL.normalizeParams(e)})}const L=r.xIx("ZodMiniTransform",((t,e)=>{r.Wc_.init(t,e),o.init(t,e)}));function I(t){return new L({type:"transform",transform:t})}const F=r.xIx("ZodMiniOptional",((t,e)=>{r.igI.init(t,e),o.init(t,e)}));function j(t){return new F({type:"optional",innerType:t})}const B=r.xIx("ZodMiniNullable",((t,e)=>{r.ZQr.init(t,e),o.init(t,e)}));function H(t){return new B({type:"nullable",innerType:t})}const V=r.xIx("ZodMiniDefault",((t,e)=>{r.rvF.init(t,e),o.init(t,e)}));function K(t,e){return new V({type:"default",innerType:t,get defaultValue(){return"function"==typeof e?e():e}})}const U=r.xIx("ZodMiniPipe",((t,e)=>{r._mk.init(t,e),o.init(t,e)}));function J(t,e){return new U({type:"pipe",in:t,out:e})}const W=r.xIx("ZodMiniLazy",((t,e)=>{r.kU.init(t,e),o.init(t,e)}));function q(t){return new W({type:"lazy",getter:t})}},5608:(t,e,n)=>{"use strict";function r(t,e,n,r){var o,i=arguments.length,s=i<3?e:null===r?r=Object.getOwnPropertyDescriptor(e,n):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,n,r);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,n,s):o(e,n))||s);return i>3&&s&&Object.defineProperty(e,n,s),s}function o(t,e,n,r){if("a"===n&&!r)throw new TypeError("Private accessor was defined without a getter");if("function"==typeof e?t!==e||!r:!e.has(t))throw new TypeError("Cannot read private member from an object whose class did not declare it");return"m"===n?r:"a"===n?r.call(t):r?r.value:e.get(t)}function i(t,e,n,r,o){if("m"===r)throw new TypeError("Private method is not writable");if("a"===r&&!o)throw new TypeError("Private accessor was defined without a setter");if("function"==typeof e?t!==e||!o:!e.has(t))throw new TypeError("Cannot write private member to an object whose class did not declare it");return"a"===r?o.call(t,n):o?o.value=n:e.set(t,n),n}n.d(e,{Cg:()=>r,GG:()=>i,gn:()=>o}),Object.create,Object.create,"function"==typeof SuppressedError&&SuppressedError}}]);