"use strict";(self.wpRiseJsonp=self.wpRiseJsonp||[]).push([["profiler"],{69970:(ue,y,f)=>{f.r(y),f.d(y,{DEFAULT_RUM_PROFILER_CONFIGURATION:()=>E,createRumProfiler:()=>Y});var w=f(37724),A=f(32990),T=f(68040),F=f(28e3),I=f(98295),P=f(55338);function U(e){let t=0;for(const o of e)o.stackId!==void 0&&t++;return t}const v=new Map;let p=!1;function B(){p=performance.now()}function J(){p=!1,v.clear()}function V(e,t){v.set(t,e)}function k(e){if(!(p===!1||e.startTime<p))return v.get(e.startTime)}function G(e){if(!(p===!1||e<p))for(const t of v.keys())t<e&&v.delete(t)}function x({rawRumEvent:e,startTime:t}){if(e.type!=="long_task")return;const o=e.long_task.id;V(o,t)}var z=f(15675);const C=(e,t,o,s)=>{const d=H(e,t,o,s),u=Q(e,d),l=t.build("fetch",u);return(0,z.A2)("Sending profile to public profiling intake",{profilingIntakeURL:l,applicationId:o,sessionId:s}),fetch(l,{body:u.data,method:"POST"})};function H(e,t,o,s){const d=t.tags,u=W(e,o,s),l=K(d),r=new Date(e.timeOrigin+e.startTime),h=new Date(e.timeOrigin+e.endTime);return{...u,attachments:["wall-time.json"],start:r.toISOString(),end:h.toISOString(),family:"chrome",runtime:"chrome",format:"json",version:4,tags_profiler:l.join(",")}}function K(e){return e.concat(["language:javascript","runtime:chrome","family:chrome","host:browser"])}function Q(e,t){const o=new Blob([JSON.stringify(e)],{type:"application/json"}),s=new FormData;return s.append("event",new Blob([JSON.stringify(t)],{type:"application/json"}),"event.json"),s.append("wall-time.json",o,"wall-time.json"),{data:s,bytesCount:0}}function W(e,t,o){const s={application:{id:t}};o&&(s.session={id:o});const d=Array.from(new Set(e.views.map(l=>l.viewId)));d.length&&(s.view={id:d});const u=e.longTasks.map(l=>k(l)).filter(l=>l!==void 0);return u.length&&(s.long_task={id:u}),s}const X={sendProfile:C},E={sampleIntervalMs:10,collectIntervalMs:6e4,minProfileDurationMs:5e3,minNumberOfSamples:50};function Y(e,t,o,s=E){const d=(0,P.s5)(P.do.LONG_ANIMATION_FRAME);let u;const l=[];let r={state:"stopped"};function h(n){r.state!=="running"&&(u=n?{startTime:n.startClocks.relative,viewId:n.id,viewName:n.name}:void 0,l.push((0,w.q)(e,window,"visibilitychange",_).stop,(0,w.q)(e,window,"beforeunload",ee).stop),g())}async function O(){await L("stopped"),J(),l.forEach(n=>n())}function Z(n){if(n.state==="running")return{cleanupTasks:n.cleanupTasks,observer:n.observer};const i=[];let a;if(e.trackLongTasks){a=new PerformanceObserver(q),a.observe({entryTypes:[ne()]});const c=t.subscribe(12,b=>{x(b)});B(),i.push(()=>a?.disconnect()),i.push(c.unsubscribe)}const m=t.subscribe(2,c=>{M({viewId:c.id,viewName:c.name,startTime:c.startClocks.relative})});return i.push(m.unsubscribe),{cleanupTasks:i,observer:a}}function g(){const n=(0,A.V)().Profiler;if(!n)throw new Error("RUM Profiler is not supported in this browser.");S(r).catch(T.Dx);const{cleanupTasks:i,observer:a}=Z(r);let m;try{m=new n({sampleInterval:s.sampleIntervalMs,maxBufferSize:Math.round(s.collectIntervalMs*1.5/s.sampleIntervalMs)})}catch(c){F.Vy.warn("[DD_RUM] Profiler startup failed. Ensure your server includes the `Document-Policy: js-profiling` response header when serving HTML pages.",c);return}r={state:"running",startTime:performance.now(),profiler:m,timeoutId:(0,I.wg)(g,s.collectIntervalMs),longTasks:[],views:[],cleanupTasks:i,observer:a},M(u),m.addEventListener("samplebufferfull",R)}async function S(n){var i,a;if(n.state!=="running")return;D((a=(i=n.observer)===null||i===void 0?void 0:i.takeRecords())!==null&&a!==void 0?a:[]),(0,I.DJ)(n.timeoutId),n.profiler.removeEventListener("samplebufferfull",R);const{startTime:m,longTasks:c,views:b}=n,re=performance.now();await n.profiler.stop().then(N=>{const j=performance.now(),oe=c.length>0,ae=j-m<s.minProfileDurationMs,le=U(N.samples)<s.minNumberOfSamples;!oe&&(ae||le)||($(Object.assign(N,{startTime:m,endTime:j,timeOrigin:performance.timeOrigin,longTasks:c,views:b,sampleInterval:s.sampleIntervalMs})),G(re))}).catch(T.Dx)}async function L(n){r.state==="running"&&(r.cleanupTasks.forEach(i=>i()),await S(r),r={state:n})}function M(n){r.state!=="running"||!n||r.views.push(n)}function $(n){var i;const a=(i=o.findTrackedSession())===null||i===void 0?void 0:i.id;X.sendProfile(n,e.profilingEndpointBuilder,e.applicationId,a).catch(T.Dx)}function R(){g()}function q(n){D(n.getEntries())}function D(n){if(r.state==="running")for(const i of n)i.duration<s.sampleIntervalMs||r.longTasks.push({id:k(i),duration:i.duration,entryType:i.entryType,startTime:i.startTime})}function _(){document.visibilityState==="hidden"&&r.state==="running"?L("paused").catch(T.Dx):document.visibilityState==="visible"&&r.state==="paused"&&g()}function ee(){g()}function ne(){return d?"long-animation-frame":"longtask"}function te(){return r.state==="stopped"}function se(){return r.state==="running"}function ie(){return r.state==="paused"}return{start:h,stop:O,isStopped:te,isRunning:se,isPaused:ie}}}}]);
