(()=>{"use strict";var e={73:function(e,r,t){var n=this&&this.__createBinding||(Object.create?function(e,r,t,n){void 0===n&&(n=t),Object.defineProperty(e,n,{enumerable:!0,get:function(){return r[t]}})}:function(e,r,t,n){void 0===n&&(n=t),e[n]=r[t]}),o=this&&this.__setModuleDefault||(Object.create?function(e,r){Object.defineProperty(e,"default",{enumerable:!0,value:r})}:function(e,r){e.default=r}),i=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var r={};if(null!=e)for(var t in e)"default"!==t&&Object.hasOwnProperty.call(e,t)&&n(r,e,t);return o(r,e),r},a=this&&this.__exportStar||function(e,r){for(var t in e)"default"===t||r.hasOwnProperty(t)||n(r,e,t)};Object.defineProperty(r,"__esModule",{value:!0}),r.format=void 0;var s=i(t(442));r.format=s,a(t(135),r),a(t(382),r);var u=t(574);Object.defineProperty(r,"parse",{enumerable:!0,get:function(){return u.parse}})},135:(e,r)=>{Object.defineProperty(r,"__esModule",{value:!0})},382:function(e,r,t){var n,o=this&&this.__extends||(n=function(e,r){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,r){e.__proto__=r}||function(e,r){for(var t in r)r.hasOwnProperty(t)&&(e[t]=r[t])},n(e,r)},function(e,r){function t(){this.constructor=e}n(e,r),e.prototype=null===r?Object.create(r):(t.prototype=r.prototype,new t)});Object.defineProperty(r,"__esModule",{value:!0}),r.InvalidParameters=r.MethodNotFound=r.InvalidRequest=r.InvalidJson=r.JsonRpcError=void 0;var i=function(e){function r(r,t,n){void 0===r&&(r="unknown error from the peer"),void 0===t&&(t=-32e3);var o=e.call(this,r)||this;return o.code=t,o.data=n,o}return o(r,e),r.prototype.toJsonRpcError=function(){return{code:this.code,data:this.data,message:this.message}},r}(t(712).BaseError);r.JsonRpcError=i;var a=function(e){function r(){return e.call(this,"invalid JSON",-32700)||this}return o(r,e),r}(i);r.InvalidJson=a;var s=function(e){function r(r){return void 0===r&&(r="invalid JSON-RPC request"),e.call(this,r,-32600)||this}return o(r,e),r}(i);r.InvalidRequest=s;var u=function(e){function r(r){var t=r?"method not found: "+r:"method not found";return e.call(this,t,-32601,r)||this}return o(r,e),r}(i);r.MethodNotFound=u;var c=function(e){function r(r){return e.call(this,"invalid parameter(s)",-32602,r)||this}return o(r,e),r}(i);r.InvalidParameters=c},442:(e,r,t)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.response=r.request=r.notification=r.error=void 0;var n=t(382),o=JSON.stringify;r.error=function(e,r){null!=r&&"function"==typeof r.toJsonRpcError||(r=new n.JsonRpcError);var t=r.toJsonRpcError();return o({error:t,id:e,jsonrpc:"2.0"})},r.notification=function(e,r){return o({jsonrpc:"2.0",method:e,params:r})},r.request=function(e,r,t){return o({id:e,jsonrpc:"2.0",method:r,params:t})},r.response=function(e,r){return o({id:e,jsonrpc:"2.0",result:r})}},574:(e,r,t)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.parse=r.isResponsePayload=r.isErrorPayload=r.isRequestPayload=r.isNotificationPayload=void 0;var n=t(904),o=t(382),i=Object.defineProperty,a=function(e,r){return i(e,"type",{configurable:!0,value:r,writable:!0})},s=function(e){return null===e?"null":typeof e},u=function(e){if(!n.isNumber(e)&&!n.isString(e))throw new o.InvalidRequest("invalid identifier: "+s(e)+" instead of number or string")},c=function(e,r){if("2.0"===r){if(void 0!==e&&!Array.isArray(e)&&!n.isObject(e))throw new o.InvalidRequest("invalid params: "+s(e)+" instead of undefined, array or object")}else if(!Array.isArray(e))throw new o.InvalidRequest("invalid params: "+s(e)+" instead of array")},f=function(e,r){return e===("2.0"===r?void 0:null)},l=function(e,r){return e.error!==("2.0"===r?void 0:null)};function d(e){if(n.isString(e))try{e=JSON.parse(e)}catch(e){if(e instanceof SyntaxError)throw new o.InvalidJson;throw e}if(Array.isArray(e))return e.map(d);var t=function(e){var r=e.jsonrpc;if(void 0===r)return"1.0";if("2.0"===r)return"2.0";throw new o.InvalidRequest("invalid version: "+s(r)+" instead of undefined or '2.0'")}(e);if(r.isNotificationPayload(e,t))a(e,"notification");else if(r.isRequestPayload(e,t))a(e,"request");else if(r.isErrorPayload(e,t))a(e,"error");else{if(!r.isResponsePayload(e,t))throw new o.InvalidJson;a(e,"response")}return e}r.isNotificationPayload=function(e,r){if(n.isString(e.method)){var t=e.id;if(f(t,r))return c(e.params,r),!0}return!1},r.isRequestPayload=function(e,r){if(n.isString(e.method)){var t=e.id;if(!f(t,r))return u(t),c(e.params,r),!0}return!1},r.isErrorPayload=function(e,r){if(!n.isString(e.method)&&l(e,r)){var t=e.id;return null!==t&&u(t),function(e,r){if("1.0"===r){if(null==e)throw new o.InvalidRequest("invalid error "+s(e))}else if(null==e||!n.isInteger(e.code)||!n.isString(e.message))throw new o.InvalidRequest("invalid error: "+s(e)+" instead of {code, message}")}(e.error,r),!0}return!1},r.isResponsePayload=function(e,r){return!n.isString(e.method)&&!l(e,r)&&(u(e.id),!0)},r.parse=d,r.default=d,d.result=function(e){var r=d(e);if(!Array.isArray(r)){if("error"===r.type)throw r.error;if("response"===r.type)return r.result}throw new TypeError("message should be response or error JSON-RPC message")}},712:(e,r)=>{var t="undefined"!=typeof Reflect?Reflect.construct:void 0,n=Object.defineProperty,o=Error.captureStackTrace;function i(e){void 0!==e&&n(this,"message",{configurable:!0,value:e,writable:!0});var r=this.constructor.name;void 0!==r&&r!==this.name&&n(this,"name",{configurable:!0,value:r,writable:!0}),o(this,this.constructor)}void 0===o&&(o=function(e){var r=new Error;n(e,"stack",{configurable:!0,get:function(){var e=r.stack;return n(this,"stack",{configurable:!0,value:e,writable:!0}),e},set:function(r){n(e,"stack",{configurable:!0,value:r,writable:!0})}})}),i.prototype=Object.create(Error.prototype,{constructor:{configurable:!0,value:i,writable:!0}});var a=function(){function e(e,r){return n(e,"name",{configurable:!0,value:r})}try{var r=function(){};if(e(r,"foo"),"foo"===r.name)return e}catch(e){}}();(e.exports=function(e,r){if(null==r||r===Error)r=i;else if("function"!=typeof r)throw new TypeError("super_ should be a function");var n;if("string"==typeof e)n=e,e=void 0!==t?function(){return t(r,arguments,this.constructor)}:function(){r.apply(this,arguments)},void 0!==a&&(a(e,n),n=void 0);else if("function"!=typeof e)throw new TypeError("constructor should be either a string or a function");e.super_=e.super=r;var o={constructor:{configurable:!0,value:e,writable:!0}};return void 0!==n&&(o.name={configurable:!0,value:n,writable:!0}),e.prototype=Object.create(r.prototype,o),e}).BaseError=i},904:(e,r)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.isObject=r.isString=r.isInteger=r.isNumber=void 0;var t=Number.NEGATIVE_INFINITY,n=Number.POSITIVE_INFINITY;r.isNumber=function(e){return"number"==typeof e&&e>t&&e<n},r.isInteger=function(e){return r.isNumber(e)&&e%1==0},r.isString=function(e){return"string"==typeof e},r.isObject=function(e){var r=typeof e;return null!==e&&("object"===r||"function"===r)}}},r={};function t(n){var o=r[n];if(void 0!==o)return o.exports;var i=r[n]={exports:{}};return e[n].call(i.exports,i,i.exports,t),i.exports}t.n=e=>{var r=e&&e.__esModule?()=>e.default:()=>e;return t.d(r,{a:r}),r},t.d=(e,r)=>{for(var n in r)t.o(r,n)&&!t.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:r[n]})},t.o=(e,r)=>Object.prototype.hasOwnProperty.call(e,r);var n=t(73);const o=[],i=window.location.href.startsWith("file:"),a=i||1===o.length&&"*"===o[0],s=document.createElement("iframe");let u,c=!1;const f=new URLSearchParams(window.location.hash.substring(1)).get("channel");s.addEventListener("load",(()=>{c&&(h(),v()),g("onload")})),s.addEventListener("error",(e=>{b(e.message)})),document.body.appendChild(s);const l={onupdate:function(e){if(null!=e.srcDoc&&e.srcDoc!==s.srcdoc)s.removeAttribute("src"),s.srcdoc=e.srcDoc;else if(null!=e.src){const r=`../../assets/${e.src}`;s.src!==r&&(s.removeAttribute("srcdoc"),s.src=r)}e.autoResize&&!c?v():!e.autoResize&&c&&h()}};if(i)window.addEventListener("message",(e=>{(a||o.includes(e.origin))&&e.source===window.parent&&d(e)}));else{if(null==f)throw new Error("Missing channel param");u=new BroadcastChannel(f),u.addEventListener("message",d)}function d(e){const r=y(n.parse(e.data));for(const e of r)"notification"===e.type?m(e)&&(0,l[e.method])(e.params):b(`Unsupported RPC type "${e.type}"`)}const p=new ResizeObserver((()=>{s.style.height="0";const e=s.contentDocument?.documentElement.scrollHeight??-1;s.style.height="",function(e){g("onresize",{height:e})}(e)}));function v(){const e=s.contentDocument?.body;null!=e?(s.scrolling="no",p.observe(e),c=!0):b("Could not observe iframe height")}function h(){s.removeAttribute("scrolling"),p.disconnect(),c=!1}function*y(e){if(Array.isArray(e))for(const r of e)yield*y(r);else yield e}function m(e){return e.method in l}function b(e){g("onerror",{message:e})}function g(e,r){const t=n.format.notification(e,r);if(null==u){const e=i?["*"]:o;for(const r of e)window.parent.postMessage(t,r)}else u.postMessage(t)}g("onready")})();