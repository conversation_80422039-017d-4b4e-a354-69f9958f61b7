"use strict";(self.wpRiseJsonp=self.wpRiseJsonp||[]).push([["node_modules_pnpm_react_19_1_0_node_modules_react_jsx-runtime_js"],{19416:(u,_)=>{/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var r=Symbol.for("react.transitional.element"),d=Symbol.for("react.fragment");function n(o,e,s){var t=null;if(s!==void 0&&(t=""+s),e.key!==void 0&&(t=""+e.key),"key"in e){s={};for(var l in e)l!=="key"&&(s[l]=e[l])}else s=e;return e=s.ref,{$$typeof:r,type:o,key:t,ref:e!==void 0?e:null,props:s}}_.Fragment=d,_.jsx=n,_.jsxs=n},62778:(u,_,r)=>{u.exports=r(19416)}}]);
