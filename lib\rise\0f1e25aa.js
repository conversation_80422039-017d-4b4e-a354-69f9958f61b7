"use strict";(self.wpRiseJsonp=self.wpRiseJsonp||[]).push([["recorder"],{84509:(he,F,m)=>{m.r(F),m.d(F,{startRecording:()=>pe});var $=m(15675),ot=m(87961),k=m(34773),st=m(12306),b=m(17954),N=m(69553),M=m(37724),rt=m(61782),l=m(55338);const D=new WeakMap;function v(t){return D.has(t)}function it(t){let e=t;for(;e;){if(!v(e)&&!(0,l.p_)(e))return!1;e=(0,l.$4)(e)}return!0}function S(t){return D.get(t)}function at(t,e){D.set(t,e)}function O(t,e){const n=t.tagName,s=t.value;if((0,l.Ie)(t,e)){const o=t.type;return n==="INPUT"&&(o==="button"||o==="submit"||o==="reset")?s:!s||n==="OPTION"?void 0:l.o}if(n==="OPTION"||n==="SELECT")return t.value;if(!(n!=="INPUT"&&n!=="TEXTAREA"))return s}const ut=/url\((?:(')([^']*)'|(")([^"]*)"|([^)]*))\)/gm,ct=/^[A-Za-z]+:|^\/\//,lt=/^data:.*,/i;function dt(t,e){return t.replace(ut,(n,s,o,r,u,i)=>{const a=o||u||i;if(!e||!a||ct.test(a)||lt.test(a))return n;const f=s||r||"";return`url(${f}${ft(a,e)}${f})`})}function ft(t,e){try{return(0,rt.c$)(t,e).href}catch{return t}}const pt=/[^a-z1-6-_]/;function B(t){const e=t.toLowerCase().trim();return pt.test(e)?"div":e}function H(t,e){return`data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='${t}' height='${e}' style='background-color:silver'%3E%3C/svg%3E`}const T={FullSnapshot:2,IncrementalSnapshot:3,Meta:4,Focus:6,ViewEnd:7,VisualViewport:8,FrustrationRecord:9},w={Document:0,DocumentType:1,Element:2,Text:3,CDATA:4,DocumentFragment:11},g={Mutation:0,MouseMove:1,MouseInteraction:2,Scroll:3,ViewportResize:4,Input:5,TouchMove:6,MediaInteraction:7,StyleSheetRule:8},y={MouseUp:0,MouseDown:1,Click:2,ContextMenu:3,DblClick:4,Focus:5,Blur:6,TouchStart:7,TouchEnd:9},U={Play:0,Pause:1};function G(t){if(!(t===void 0||t.length===0))return t.map(e=>{const n=e.cssRules||e.rules;return{cssRules:Array.from(n,r=>r.cssText),disabled:e.disabled||void 0,media:e.media.length>0?Array.from(e.media):void 0}})}var ht=m(22565);function W(t,e,n,s){if(e===l.$m.HIDDEN)return null;const o=t.getAttribute(n);if(e===l.$m.MASK&&n!==l.NT&&!l.yF.includes(n)&&n!==s.actionNameAttribute){const r=t.tagName;switch(n){case"title":case"alt":case"placeholder":return l.o}if(r==="IMG"&&(n==="src"||n==="srcset")){const u=t;if(u.naturalWidth>0)return H(u.naturalWidth,u.naturalHeight);const{width:i,height:a}=t.getBoundingClientRect();return i>0||a>0?H(i,a):l.eT}if(r==="SOURCE"&&(n==="src"||n==="srcset"))return l.eT;if(r==="A"&&n==="href"||o&&n.startsWith("data-")||r==="IFRAME"&&n==="srcdoc")return l.o}return!o||typeof o!="string"?o:(0,l.YR)(o)?(0,l.jK)(o):o}function mt(t,e,n){if(e===l.$m.HIDDEN)return{};const s={},o=B(t.tagName),r=t.ownerDocument;for(let c=0;c<t.attributes.length;c+=1){const p=t.attributes.item(c).name,h=W(t,e,p,n.configuration);h!==null&&(s[p]=h)}if(t.value&&(o==="textarea"||o==="select"||o==="option"||o==="input")){const c=O(t,e);c!==void 0&&(s.value=c)}if(o==="option"&&e===l.$m.ALLOW){const c=t;c.selected&&(s.selected=c.selected)}if(o==="link"){const c=Array.from(r.styleSheets).find(p=>p.href===t.href),d=A(c);d&&c&&(s._cssText=d)}if(o==="style"&&t.sheet){const c=A(t.sheet);c&&(s._cssText=c)}const u=t;if(o==="input"&&(u.type==="radio"||u.type==="checkbox")&&(e===l.$m.ALLOW?s.checked=!!u.checked:(0,l.Ie)(u,e)&&delete s.checked),o==="audio"||o==="video"){const c=t;s.rr_mediaState=c.paused?"paused":"played"}let i,a;const f=n.serializationContext;switch(f.status){case 0:i=Math.round(t.scrollTop),a=Math.round(t.scrollLeft),(i||a)&&f.elementsScrollPositions.set(t,{scrollTop:i,scrollLeft:a});break;case 1:f.elementsScrollPositions.has(t)&&({scrollTop:i,scrollLeft:a}=f.elementsScrollPositions.get(t));break}return a&&(s.rr_scrollLeft=a),i&&(s.rr_scrollTop=i),s}function A(t){if(!t)return null;let e;try{e=t.rules||t.cssRules}catch{}if(!e)return null;const n=Array.from(e,(0,ht.nr)()?St:J).join("");return dt(n,t.href)}function St(t){if(yt(t)&&t.selectorText.includes(":")){const e=/(\[[\w-]+[^\\])(:[^\]]+\])/g;return t.cssText.replace(e,"$1\\$2")}return J(t)}function J(t){return gt(t)&&A(t.styleSheet)||t.cssText}function gt(t){return"styleSheet"in t}function yt(t){return"selectorText"in t}function P(t,e){const n=Tt(t,e);if(!n)return null;const s=S(t)||vt(),o=n;return o.id=s,at(t,s),e.serializedNodeIds&&e.serializedNodeIds.add(s),o}let Et=1;function vt(){return Et++}function V(t,e){const n=[];return(0,l.wI)(t,s=>{const o=P(s,e);o&&n.push(o)}),n}function Tt(t,e){switch(t.nodeType){case t.DOCUMENT_NODE:return Nt(t,e);case t.DOCUMENT_FRAGMENT_NODE:return It(t,e);case t.DOCUMENT_TYPE_NODE:return Mt(t);case t.ELEMENT_NODE:return wt(t,e);case t.TEXT_NODE:return Ct(t,e);case t.CDATA_SECTION_NODE:return xt()}}function Nt(t,e){return{type:w.Document,childNodes:V(t,e),adoptedStyleSheets:G(t.adoptedStyleSheets)}}function It(t,e){const n=(0,l.p_)(t);return n&&e.serializationContext.shadowRootsController.addShadowRoot(t),{type:w.DocumentFragment,childNodes:V(t,e),isShadowRoot:n,adoptedStyleSheets:n?G(t.adoptedStyleSheets):void 0}}function Mt(t){return{type:w.DocumentType,name:t.name,publicId:t.publicId,systemId:t.systemId}}function wt(t,e){const n=B(t.tagName),s=Rt(t)||void 0,o=(0,l.jR)((0,l.dT)(t),e.parentNodePrivacyLevel);if(o===l.$m.HIDDEN){const{width:i,height:a}=t.getBoundingClientRect();return{type:w.Element,tagName:n,attributes:{rr_width:`${i}px`,rr_height:`${a}px`,[l.NT]:l.Wd},childNodes:[],isSVG:s}}if(o===l.$m.IGNORE)return;const r=mt(t,o,e);let u=[];if((0,l.wR)(t)&&n!=="style"){let i;e.parentNodePrivacyLevel===o&&e.ignoreWhiteSpace===(n==="head")?i=e:i={...e,parentNodePrivacyLevel:o,ignoreWhiteSpace:n==="head"},u=V(t,i)}return{type:w.Element,tagName:n,attributes:r,childNodes:u,isSVG:s}}function Rt(t){return t.tagName==="svg"||t instanceof SVGElement}function Ct(t,e){const n=(0,l.rf)(t,e.ignoreWhiteSpace||!1,e.parentNodePrivacyLevel);if(n!==void 0)return{type:w.Text,textContent:n}}function xt(){return{type:w.CDATA,textContent:""}}function Lt(t,e,n){return P(t,{serializationContext:n,parentNodePrivacyLevel:e.defaultPrivacyLevel,configuration:e})}function Y(t){return!!t.changedTouches}function x(t){return t.composed===!0&&(0,l.XS)(t.target)?t.composedPath()[0]:t.target}const X=25;function bt(t){return Math.abs(t.pageTop-t.offsetTop-window.scrollY)>X||Math.abs(t.pageLeft-t.offsetLeft-window.scrollX)>X}const Dt=(t,e)=>{const n=window.visualViewport,s={layoutViewportX:t,layoutViewportY:e,visualViewportX:t,visualViewportY:e};if(n)bt(n)?(s.layoutViewportX=Math.round(t+n.offsetLeft),s.layoutViewportY=Math.round(e+n.offsetTop)):(s.visualViewportX=Math.round(t-n.offsetLeft),s.visualViewportY=Math.round(e-n.offsetTop));else return s;return s},j=t=>({scale:t.scale,offsetLeft:t.offsetLeft,offsetTop:t.offsetTop,pageLeft:t.pageLeft,pageTop:t.pageTop,height:t.height,width:t.width});var C=m(92006);function E(t,e){return{data:{source:t,...e},type:T.IncrementalSnapshot,timestamp:(0,C.nx)()}}const Ot=50;function At(t,e){const{throttled:n,cancel:s}=(0,N.n)(r=>{const u=x(r);if(v(u)){const i=K(r);if(!i)return;const a={id:S(u),timeOffset:0,x:i.x,y:i.y};e(E(Y(r)?g.TouchMove:g.MouseMove,{positions:[a]}))}},Ot,{trailing:!1}),{stop:o}=(0,M.l)(t,document,["mousemove","touchmove"],n,{capture:!0,passive:!0});return{stop:()=>{o(),s()}}}function K(t){let{clientX:e,clientY:n}=Y(t)?t.changedTouches[0]:t;if(window.visualViewport){const{visualViewportX:s,visualViewportY:o}=Dt(e,n);e=s,n=o}if(!Number.isFinite(e)||!Number.isFinite(n)){t.isTrusted&&(0,$.A2)("mouse/touch event without x/y");return}return{x:e,y:n}}const Z={pointerup:y.MouseUp,mousedown:y.MouseDown,click:y.Click,contextmenu:y.ContextMenu,dblclick:y.DblClick,focus:y.Focus,blur:y.Blur,touchstart:y.TouchStart,touchend:y.TouchEnd};function Pt(t,e,n){const s=o=>{const r=x(o);if((0,l.PJ)(r,t.defaultPrivacyLevel)===l.$m.HIDDEN||!v(r))return;const u=S(r),i=Z[o.type];let a;if(i!==y.Blur&&i!==y.Focus){const c=K(o);if(!c)return;a={id:u,type:i,x:c.x,y:c.y}}else a={id:u,type:i};const f={id:n.getIdForEvent(o),...E(g.MouseInteraction,a)};e(f)};return(0,M.l)(t,document,Object.keys(Z),s,{capture:!0,passive:!0})}const Vt=100;function Q(t,e,n,s=document){const{throttled:o,cancel:r}=(0,N.n)(i=>{const a=x(i);if(!a||(0,l.PJ)(a,t.defaultPrivacyLevel)===l.$m.HIDDEN||!v(a))return;const f=S(a),c=a===document?{scrollTop:(0,l.zL)(),scrollLeft:(0,l.Gn)()}:{scrollTop:Math.round(a.scrollTop),scrollLeft:Math.round(a.scrollLeft)};n.set(a,c),e(E(g.Scroll,{id:f,x:c.scrollLeft,y:c.scrollTop}))},Vt),{stop:u}=(0,M.q)(t,s,"scroll",o,{capture:!0,passive:!0});return{stop:()=>{u(),r()}}}const zt=200;function Ft(t,e){const n=(0,l.g1)(t).subscribe(s=>{e(E(g.ViewportResize,s))});return{stop:()=>{n.unsubscribe()}}}function $t(t,e){const n=window.visualViewport;if(!n)return{stop:N.l};const{throttled:s,cancel:o}=(0,N.n)(()=>{e({data:j(n),type:T.VisualViewport,timestamp:(0,C.nx)()})},zt,{trailing:!1}),{stop:r}=(0,M.l)(t,n,["resize","scroll"],s,{capture:!0,passive:!0});return{stop:()=>{r(),o()}}}function kt(t,e){return(0,M.l)(t,document,["play","pause"],n=>{const s=x(n);!s||(0,l.PJ)(s,t.defaultPrivacyLevel)===l.$m.HIDDEN||!v(s)||e(E(g.MediaInteraction,{id:S(s),type:n.type==="play"?U.Play:U.Pause}))},{capture:!0,passive:!0})}var I=m(63832);function Bt(t){function e(o,r){o&&v(o.ownerNode)&&r(S(o.ownerNode))}const n=[(0,I.H)(CSSStyleSheet.prototype,"insertRule",({target:o,parameters:[r,u]})=>{e(o,i=>t(E(g.StyleSheetRule,{id:i,adds:[{rule:r,index:u}]})))}),(0,I.H)(CSSStyleSheet.prototype,"deleteRule",({target:o,parameters:[r]})=>{e(o,u=>t(E(g.StyleSheetRule,{id:u,removes:[{index:r}]})))})];typeof CSSGroupingRule<"u"?s(CSSGroupingRule):(s(CSSMediaRule),s(CSSSupportsRule));function s(o){n.push((0,I.H)(o.prototype,"insertRule",({target:r,parameters:[u,i]})=>{e(r.parentStyleSheet,a=>{const f=_(r);f&&(f.push(i||0),t(E(g.StyleSheetRule,{id:a,adds:[{rule:u,index:f}]})))})}),(0,I.H)(o.prototype,"deleteRule",({target:r,parameters:[u]})=>{e(r.parentStyleSheet,i=>{const a=_(r);a&&(a.push(u),t(E(g.StyleSheetRule,{id:i,removes:[{index:a}]})))})}))}return{stop:()=>{n.forEach(o=>o.stop())}}}function _(t){const e=[];let n=t;for(;n.parentRule;){const u=Array.from(n.parentRule.cssRules).indexOf(n);e.unshift(u),n=n.parentRule}if(!n.parentStyleSheet)return;const o=Array.from(n.parentStyleSheet.cssRules).indexOf(n);return e.unshift(o),e}function Ht(t,e){return(0,M.l)(t,window,["focus","blur"],()=>{e({data:{has_focus:document.hasFocus()},type:T.Focus,timestamp:(0,C.nx)()})})}function Ut(t,e,n){const s=t.subscribe(12,o=>{var r,u;o.rawRumEvent.type==="action"&&o.rawRumEvent.action.type==="click"&&(!((u=(r=o.rawRumEvent.action.frustration)===null||r===void 0?void 0:r.type)===null||u===void 0)&&u.length)&&"events"in o.domainContext&&o.domainContext.events&&o.domainContext.events.length&&e({timestamp:o.rawRumEvent.date,type:T.FrustrationRecord,data:{frustrationTypes:o.rawRumEvent.action.frustration.type,recordIds:o.domainContext.events.map(i=>n.getIdForEvent(i))}})});return{stop:()=>{s.unsubscribe()}}}function Gt(t,e){const n=t.subscribe(5,()=>{e({timestamp:(0,C.nx)(),type:T.ViewEnd})});return{stop:()=>{n.unsubscribe()}}}function q(t,e,n=document){const s=t.defaultPrivacyLevel,o=new WeakMap,r=n!==document,{stop:u}=(0,M.l)(t,n,r?["change"]:["input","change"],c=>{const d=x(c);(d instanceof HTMLInputElement||d instanceof HTMLTextAreaElement||d instanceof HTMLSelectElement)&&a(d)},{capture:!0,passive:!0});let i;if(r)i=N.l;else{const c=[(0,I.t)(HTMLInputElement.prototype,"value",a),(0,I.t)(HTMLInputElement.prototype,"checked",a),(0,I.t)(HTMLSelectElement.prototype,"value",a),(0,I.t)(HTMLTextAreaElement.prototype,"value",a),(0,I.t)(HTMLSelectElement.prototype,"selectedIndex",a)];i=()=>{c.forEach(d=>d.stop())}}return{stop:()=>{i(),u()}};function a(c){const d=(0,l.PJ)(c,s);if(d===l.$m.HIDDEN)return;const p=c.type;let h;if(p==="radio"||p==="checkbox"){if((0,l.Ie)(c,d))return;h={isChecked:c.checked}}else{const R=O(c,d);if(R===void 0)return;h={text:R}}f(c,h);const L=c.name;p==="radio"&&L&&c.checked&&document.querySelectorAll(`input[type="radio"][name="${CSS.escape(L)}"]`).forEach(R=>{R!==c&&f(R,{isChecked:!1})})}function f(c,d){if(!v(c))return;const p=o.get(c);(!p||p.text!==d.text||p.isChecked!==d.isChecked)&&(o.set(c,d),e(E(g.Input,{id:S(c),...d})))}}var Wt=m(68040),Jt=m(36994);const Yt=100,Xt=16;function jt(t){let e=N.l,n=[];function s(){e(),t(n),n=[]}const{throttled:o,cancel:r}=(0,N.n)(s,Xt,{leading:!1});return{addMutations:u=>{n.length===0&&(e=(0,Jt.BB)(o,{timeout:Yt})),n.push(...u)},flush:s,stop:()=>{e(),r()}}}function tt(t,e,n,s){const o=(0,l.W3)();if(!o)return{stop:N.l,flush:N.l};const r=jt(i=>{Kt(i.concat(u.takeRecords()),t,e,n)}),u=new o((0,Wt.dm)(r.addMutations));return u.observe(s,{attributeOldValue:!0,attributes:!0,characterData:!0,characterDataOldValue:!0,childList:!0,subtree:!0}),{stop:()=>{u.disconnect(),r.stop()},flush:()=>{r.flush()}}}function Kt(t,e,n,s){const o=new Map;t.filter(d=>d.type==="childList").forEach(d=>{d.removedNodes.forEach(p=>{et(p,s.removeShadowRoot)})});const r=t.filter(d=>d.target.isConnected&&it(d.target)&&(0,l.PJ)(d.target,n.defaultPrivacyLevel,o)!==l.$m.HIDDEN),{adds:u,removes:i,hasBeenSerialized:a}=Zt(r.filter(d=>d.type==="childList"),n,s,o),f=Qt(r.filter(d=>d.type==="characterData"&&!a(d.target)),n,o),c=_t(r.filter(d=>d.type==="attributes"&&!a(d.target)),n,o);!f.length&&!c.length&&!i.length&&!u.length||e(E(g.Mutation,{adds:u,removes:i,texts:f,attributes:c}))}function Zt(t,e,n,s){const o=new Set,r=new Map;for(const p of t)p.addedNodes.forEach(h=>{o.add(h)}),p.removedNodes.forEach(h=>{o.has(h)||r.set(h,p.target),o.delete(h)});const u=Array.from(o);qt(u);const i=new Set,a=[];for(const p of u){if(c(p))continue;const h=(0,l.PJ)(p.parentNode,e.defaultPrivacyLevel,s);if(h===l.$m.HIDDEN||h===l.$m.IGNORE)continue;const L=P(p,{serializedNodeIds:i,parentNodePrivacyLevel:h,serializationContext:{status:2,shadowRootsController:n},configuration:e});if(!L)continue;const R=(0,l.$4)(p);a.push({nextId:d(p),parentId:S(R),node:L})}const f=[];return r.forEach((p,h)=>{v(h)&&f.push({parentId:S(p),id:S(h)})}),{adds:a,removes:f,hasBeenSerialized:c};function c(p){return v(p)&&i.has(S(p))}function d(p){let h=p.nextSibling;for(;h;){if(v(h))return S(h);h=h.nextSibling}return null}}function Qt(t,e,n){var s;const o=[],r=new Set,u=t.filter(i=>r.has(i.target)?!1:(r.add(i.target),!0));for(const i of u){if(i.target.textContent===i.oldValue)continue;const f=(0,l.PJ)((0,l.$4)(i.target),e.defaultPrivacyLevel,n);f===l.$m.HIDDEN||f===l.$m.IGNORE||o.push({id:S(i.target),value:(s=(0,l.rf)(i.target,!1,f))!==null&&s!==void 0?s:null})}return o}function _t(t,e,n){const s=[],o=new Map,r=t.filter(i=>{const a=o.get(i.target);return a&&a.has(i.attributeName)?!1:(a?a.add(i.attributeName):o.set(i.target,new Set([i.attributeName])),!0)}),u=new Map;for(const i of r){if(i.target.getAttribute(i.attributeName)===i.oldValue)continue;const f=(0,l.PJ)(i.target,e.defaultPrivacyLevel,n),c=W(i.target,f,i.attributeName,e);let d;if(i.attributeName==="value"){const h=O(i.target,f);if(h===void 0)continue;d=h}else typeof c=="string"?d=c:d=null;let p=u.get(i.target);p||(p={id:S(i.target),attributes:{}},s.push(p),u.set(i.target,p)),p.attributes[i.attributeName]=d}return s}function qt(t){t.sort((e,n)=>{const s=e.compareDocumentPosition(n);return s&Node.DOCUMENT_POSITION_CONTAINED_BY?-1:s&Node.DOCUMENT_POSITION_CONTAINS||s&Node.DOCUMENT_POSITION_FOLLOWING?1:s&Node.DOCUMENT_POSITION_PRECEDING?-1:0})}function et(t,e){(0,l.XS)(t)&&e(t.shadowRoot),(0,l.wI)(t,n=>et(n,e))}function te(){const t=new WeakMap;return{set(e,n){e===document&&!document.scrollingElement||t.set(e===document?document.scrollingElement:e,n)},get(e){return t.get(e)},has(e){return t.has(e)}}}const ee=(t,e,n)=>{const s=new Map,o={addShadowRoot:r=>{if(s.has(r))return;const u=tt(e,t,o,r),i=q(t,e,r),a=Q(t,e,n,r);s.set(r,{flush:()=>u.flush(),stop:()=>{u.stop(),i.stop(),a.stop()}})},removeShadowRoot:r=>{const u=s.get(r);u&&(u.stop(),s.delete(r))},stop:()=>{s.forEach(({stop:r})=>r())},flush:()=>{s.forEach(({flush:r})=>r())}};return o};function ne(t,e,n,s,o,r){const u=(a=(0,C.nx)(),f={status:0,elementsScrollPositions:t,shadowRootsController:e})=>{const{width:c,height:d}=(0,l.pB)(),p=[{data:{height:d,href:window.location.href,width:c},type:T.Meta,timestamp:a},{data:{has_focus:document.hasFocus()},type:T.Focus,timestamp:a},{data:{node:Lt(document,s,f),initialOffset:{left:(0,l.Gn)(),top:(0,l.zL)()}},type:T.FullSnapshot,timestamp:a}];return window.visualViewport&&p.push({data:j(window.visualViewport),type:T.VisualViewport,timestamp:a}),p};r(u());const{unsubscribe:i}=n.subscribe(2,a=>{o(),r(u(a.startClocks.timeStamp,{shadowRootsController:e,status:1,elementsScrollPositions:t}))});return{stop:i}}function oe(){const t=new WeakMap;let e=1;return{getIdForEvent(n){return t.has(n)||t.set(n,e++),t.get(n)}}}function se(t){const{emit:e,configuration:n,lifeCycle:s}=t;if(!e)throw new Error("emit function is required");const o=p=>{e(p),(0,st.b)("record",{record:p});const h=t.viewHistory.findView();b.$1(h.id)},r=te(),u=ee(n,o,r),{stop:i}=ne(r,u,s,n,a,p=>p.forEach(h=>o(h)));function a(){u.flush(),c.flush()}const f=oe(),c=tt(o,n,u,document),d=[c,At(n,o),Pt(n,o,f),Q(n,o,r,document),Ft(n,o),q(n,o),kt(n,o),Bt(o),Ht(n,o),$t(n,o),Ut(s,o,f),Gt(s,p=>{a(),o(p)})];return{stop:()=>{u.stop(),d.forEach(p=>p.stop()),i()},flushMutations:a,shadowRootsController:u}}var re=m(65548),nt=m(98295);function ie(t,e,n){const s=new FormData;s.append("segment",new Blob([t],{type:"application/octet-stream"}),`${e.session.id}-${e.start}`);const o={raw_segment_size:n,compressed_segment_size:t.byteLength,...e},r=JSON.stringify(o);return s.append("event",new Blob([r],{type:"application/json"})),{data:s,bytesCount:t.byteLength}}function ae({context:t,creationReason:e,encoder:n}){let s=0;const o=t.view.id,r={start:1/0,end:-1/0,creation_reason:e,records_count:0,has_full_snapshot:!1,index_in_view:b.K_(o),source:"browser",...t};b.H5(o);function u(a,f){r.start=Math.min(r.start,a.timestamp),r.end=Math.max(r.end,a.timestamp),r.records_count+=1,r.has_full_snapshot||(r.has_full_snapshot=a.type===T.FullSnapshot);const c=n.isEmpty?'{"records":[':",";n.write(c+JSON.stringify(a),d=>{s+=d,f(s)})}function i(a){if(n.isEmpty)throw new Error("Empty segment flushed");n.write(`],${JSON.stringify(r).slice(1)}
`),n.finish(f=>{b.L7(r.view.id,f.rawBytesCount),a(r,f)})}return{addRecord:u,flush:i}}const ue=5*C.OY;let z=6e4;function ce(t,e,n,s,o,r){return le(t,()=>de(e.applicationId,n,s),o,r)}function le(t,e,n,s){let o={status:0,nextSegmentCreationReason:"init"};const{unsubscribe:r}=t.subscribe(2,()=>{i("view_change")}),{unsubscribe:u}=t.subscribe(11,a=>{i(a.reason)});function i(a){o.status===1&&(o.segment.flush((f,c)=>{const d=ie(c.output,f,c.rawBytesCount);(0,re.Kp)(a)?n.sendOnExit(d):n.send(d)}),(0,nt.DJ)(o.expirationTimeoutId)),a!=="stop"?o={status:0,nextSegmentCreationReason:a}:o={status:2}}return{addRecord:a=>{if(o.status!==2){if(o.status===0){const f=e();if(!f)return;o={status:1,segment:ae({encoder:s,context:f,creationReason:o.nextSegmentCreationReason}),expirationTimeoutId:(0,nt.wg)(()=>{i("segment_duration_limit")},ue)}}o.segment.addRecord(a,f=>{f>z&&i("segment_bytes_limit")})}},stop:()=>{i("stop"),r(),u()}}}function de(t,e,n){const s=e.findTrackedSession(),o=n.findView();if(!(!s||!o))return{application:{id:t},session:{id:s.id},view:{id:o.id}}}function me(t=6e4){z=t}function fe(t){const e=(0,k.Y9)();return{addRecord:n=>{const s=t.findView();e.send("record",n,s.id)}}}function pe(t,e,n,s,o,r){const u=[],i=d=>{t.notify(14,{error:d}),(0,$.A2)("Error reported to customer",{"error.message":d.message})},a=r||(0,ot.sA)(e.sessionReplayEndpointBuilder,z,i);let f;if((0,k.d0)())({addRecord:f}=fe(s));else{const d=ce(t,e,n,s,a,o);f=d.addRecord,u.push(d.stop)}const{stop:c}=se({emit:f,configuration:e,lifeCycle:t,viewHistory:s});return u.push(c),{stop:()=>{u.forEach(d=>d())}}}}}]);
