﻿/*! ds-bootstrap - v1.0.0.32618 - 2024-06-13 4:56pm UTC
* Copyright (c) 2024 ; Not Licensed */!function(){"use strict";function e(t){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e(t)}function t(t,n){for(var i=0;i<n.length;i++){var r=n[i];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,(o=r.key,a=void 0,a=function(t,n){if("object"!==e(t)||null===t)return t;var i=t[Symbol.toPrimitive];if(void 0!==i){var r=i.call(t,n||"default");if("object"!==e(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===n?String:Number)(t)}(o,"string"),"symbol"===e(a)?a:String(a)),r)}var o,a}var n=DS,i=n._,r=n.pubSub,o=n.events,a=n.constants,l=n.utils,s=n.translationStore,c=[],u=function(){function e(t,n,l){var s=this;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.frame=t,this.frame.blocked=!1,this.preso=l,this.setupControlOptions(),this.layouts={},this.dockedStates={},this.dockSettings={dockedState:a.docked.NONE,width:0},this.setLayout(this.frame.default_layout,a.refs.FRAME),DS.flagManager.multiLangSupport&&!i.isEmpty(n)&&this.setLocalizationData(n,!0),this.resourceDescription=this.frame.resourceData.description;var c=l.getFirstSlide();for(var u in this.slideWidth=c.get("width"),this.slideHeight=c.get("height"),this.temp=[],this.frame.layouts)this.temp.push(u);this.rtl="rtl"===this.frame.textdirection,this.hasModernText=0!==this.frame.renderingEngineType,this.dir=this.dir.bind(this),i.bindAll(this,"onLayoutChanged"),r.on(o.controlLayout.CHANGED,this.onLayoutChanged),r.on(o.controlLayout.UPDATE,(function(e,t,n){s.frame.controlLayouts[e]=n,s.setLayout(e,t)})),r.on(o.controlOptions.CHANGED,(function(e){var t=s.optionChangesRequireMenuRefresh(s.frame.controlOptions.menuOptions,e.menuOptions);s.frame.controlOptions=e,s.setupControlOptions(),r.trigger(o.controlOptions.RESET),t&&r.trigger(o.navData.REFRESH_VIEW)})),r.on(o.frame.FONT_SCALE,(function(e){s.frame.fontscale=e,r.trigger(o.controlOptions.RESET)})),r.on(o.glossary.UPDATE,(function(e){s.frame.glossaryData=e,r.trigger(o.glossary.REFRESH_VIEW)})),r.on(o.navData.UPDATE,(function(e){s.frame.navData=e,r.trigger(o.navData.REFRESH_VIEW)})),r.on(o.resources.UPDATE,(function(e){s.frame.resourceData.resources=e,r.trigger(o.resources.REFRESH_VIEW)})),r.on(o.resources.UPDATE_DESCRIPTION,(function(e){s.frame.resourceData.description=e,r.trigger(o.resources.REFRESH_VIEW)})),r.on(o.layer.DIALOG_SHOWN,(function(e){var t=DS.windowManager.getCurrentWindow();r.trigger(o.frameModel.BLOCKED_CHANGED,t.frame.id,!0,e)})),r.on(o.layer.DIALOG_HIDDEN,(function(){var e=DS.windowManager.getCurrentWindow();r.trigger(o.frameModel.BLOCKED_CHANGED,e.frame.id,!1)})),r.on(o.localization.LANGUAGE_UPDATED,(function(){s.setLocalizationData(DS.localizationManager.getCurrentFrameData())})),r.on(o.localization.REVIEW_FRAME_UPDATED,(function(){s.updateReviewStrings()}))}var n,u,f;return n=e,u=[{key:"traverseMenu",value:function(e,t){var n=this;t.forEach((function(i){e(i),null!=i.links&&t.length>0&&n.traverseMenu(e,i.links)}))}},{key:"updateMenuStrings",value:function(){var e=l.getPath(this.frame,"navData.outline.links",[]);this.traverseMenu((function(e){e.displaytext=s.getUpdatedTextData("menuItem","".concat(e.translationId,"-Name"),e.displaytext)}),e)}},{key:"updateGlossaryStrings",value:function(){(this.frame.glossaryData||[]).forEach((function(e){var t=e.translationId;e.title=s.getUpdatedTextData("glossaryItem","".concat(t,"-Term"),e.title),e.content=s.getUpdatedTextData("glossaryItem","".concat(t,"-Definition"),e.content)}))}},{key:"updateResourcesStrings",value:function(){l.getPath(this.frame,"resourceData.resources",[]).forEach((function(e){var t=e.translationId;e.title=s.getUpdatedTextData("resourceItem","".concat(t,"-Title"),e.title)}))}},{key:"updateNoteStrings",value:function(){(this.frame.notesData||[]).forEach((function(e){var t=e.slideId,n=i.last(t.split(".")),r=s.getUpdatedTextData("slideNote","".concat(n,"-Note"));null!=r&&null!=DS.renderHtmlString&&(e.content=DS.renderHtmlString(r))}))}},{key:"updateReviewStrings",value:function(){this.updateMenuStrings(),this.updateGlossaryStrings(),this.updateResourcesStrings(),this.updateNoteStrings(),this.title.text=s.getUpdatedTextData("projectTitle","project-Title",this.title.text),this.notifyStringsUpdated()}},{key:"setLocalizationData",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];this.frame.stringTables=e.stringTables,this.frame.navData=e.navData,this.frame.resourceData=e.resourceData,this.frame.glossaryData=e.glossaryData,this.frame.notesData=e.notesData,this.frame.controlStrings=e.controlStrings.reduce((function(e,t){var n=t.id,i=t.value;return e[n]=i,e}),{}),this.title.text=this.frame.controlStrings.title,t||this.notifyStringsUpdated()}},{key:"notifyStringsUpdated",value:function(){r.trigger(o.strings.UPDATE_STRINGS),r.trigger(o.navData.REFRESH_VIEW),r.trigger(o.glossary.REFRESH_VIEW),r.trigger(o.resources.REFRESH_VIEW),r.trigger(o.transcript.REFRESH_VIEW),r.trigger(o.frame.REFLOW)}},{key:"setupControlOptions",value:function(){var e=this.frame.controlOptions.sidebarOptions;this.sidebarOpts=e,this.bottomBarOpts=this.frame.controlOptions.bottomBarOptions,this.topTabs=e.tabs.linkRight||[],this.topTabsLeft=e.tabs.linkLeft||[],this.topTabsRight=e.tabs.linkRight||[],this.sidebarTabs=e.tabs.sidebar||[],this.outlineInSidebar=this.sidebarTabs.some((function(e){return"outline"===e.name})),this.buttonOptions=this.frame.controlOptions.buttonoptions,this.title={enabled:e.titleEnabled,text:e.titleText}}},{key:"optionChangesRequireMenuRefresh",value:function(e,t){return e.wrapListItems!==t.wrapListItems||e.autonumber!==t.autonumber}},{key:"onLayoutChanged",value:function(e,t){this.setLayout(e,t)}},{key:"hasTopLinks",value:function(){return 0!==this.topTabsLeft.length||0!==this.topTabsRight.length}},{key:"getControlString",value:function(e){return s.getUpdatedTextData("frameLink","".concat(e,"-Link"),l.getPath(this.frame,"controlStrings.".concat(e),null))}},{key:"getString",value:function(e){var t=this.currLayout.string_table,n=this.frame.stringTables[t].string[e];return null==n?(c.includes(e)||(c.push(e),console.warn("could not find ".concat(e," in string table ").concat(t))),e.replace("acc_","").replace(/_/g," ")):n}},{key:"setDocked",value:function(e,t,n){t===a.docked.NONE?(this.dockedStates[e]=null,delete this.dockedStates[e]):this.dockedStates[e]={dockedState:t,width:n};var i=Object.keys(this.dockedStates);0===i.length?this.dockSettings={dockedState:a.docked.NONE,width:0}:t!==a.docked.NONE?this.dockSettings={dockedState:t,width:n}:this.dockSettings=this.dockedStates[i[0]]}},{key:"getDockedWidth",value:function(){var e=this.dockSettings,t=e.dockedState,n=e.width;return t!==DS.constants.docked.NONE?n:0}},{key:"setLayout",value:function(e,t){this.currLayout=this.frame.layouts[e],this.currControlLayout=this.frame.controlLayouts[e],this.layouts[t]=this.currControlLayout,r.trigger(o.frameModel.LAYOUT_CHANGED,this.currControlLayout,t)}},{key:"getWndControlLayout",value:function(e){return this.layouts[e]||this.currControlLayout}},{key:"dir",value:function(e){if(null!=e)return this.rtl?e.reverse():e}}],u&&t(n.prototype,u),f&&t(n,f),Object.defineProperty(n,"prototype",{writable:!1}),e}(),f=u;function d(e){return d="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},d(e)}function h(e,t,n){return(t=v(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function p(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var i,r,o,a,l=[],s=!0,c=!1;try{if(o=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;s=!1}else for(;!(s=(i=o.call(n)).done)&&(l.push(i.value),l.length!==t);s=!0);}catch(e){c=!0,r=e}finally{try{if(!s&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(c)throw r}}return l}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return y(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return y(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function y(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,i=new Array(t);n<t;n++)i[n]=e[n];return i}function b(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,v(i.key),i)}}function v(e){var t=function(e,t){if("object"!==d(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var i=n.call(e,t||"default");if("object"!==d(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===d(t)?t:String(t)}var g,m=DS,w=m._,S=m.scaler,k=m.detection,C=m.detection.orientation,O=m.utils,E=O.getPath,L=O.scaleVal,x=O.pxify,P=m.dom,T=P.addClass,j=P.removeClass,D=m.constants.refs.FRAME,I=document.getElementById(DS.constants.els.PRESO),A={x:"w",xl:"w",xp:"w",y:"h",yl:"h",yp:"h",wl:"w",wp:"w",hl:"h",hp:"h"},R=["wrapper","lightBoxWrapper"],B=function(e){null!=e.beforeUpdateHook&&e.beforeUpdateHook()},M=function(e,t,n){B(e),n=e.w>0?n:0,e.y=e.top=0,e.x=e.left=t.l+n,e.update(!0),t.l=e.x+e.w},H=function(e,t,n){B(e),n=e.w>0?n:0,e.y=e.top=0,e.x=e.left=t.r-e.w-n,e.update(!0),t.r=e.x},N=function(e,t,n){B(e),n=e.h>0?n:0,e.x=e.left=0,e.y=e.top=t.t+n,e.update(!0),t.t=e.y+e.h},F=function(e,t,n){B(e),n=e.h>0?n:0,e.x=e.left=0,e.y=e.top=t.b-e.h-n,e.update(!0),t.b=e.y},V={l:M,r:H,t:N,b:F},W=function(){function e(t,n){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.ViewLogic=t,this.params=n,this.nameKey=null!=n&&n.nameKey||t.nameKey,this.enabled=!0}var t,n,i;return t=e,n=[{key:"init",value:function(e){if(this.hasInited)console.warn("has already initialized",this);else{var t;this.hasInited=!0;var n=this.ViewLogic,i=this.params;null==i&&null!=n?i=n:null!=n&&(t=!0),w.isFunction(i)&&(i=i(this.nameSpace||e)),i.w=i.w||i.minW||100,i.h=i.h||i.minH||100,this.orientationProps(i,"x","y","w","h","scale"),(i=Object.assign({position:"absolute",x:0,y:0,minW:0,maxW:Number.MAX_SAFE_INTEGER,minH:0,maxH:Number.MAX_SAFE_INTEGER,wPad:0,scale:1,visibility:"reflow",visual:!0,bgColor:null,overflow:"hidden",origin:"center center",z:null,opacity:null,visible:!0,attrs:{},noContent:!0,calcTextSize:!1},i)).calcTextSize&&(i.noContent=!1),this.noContent=i.noContent,this.lastWidthByText=0,this.lastHeightByText=0,this.padLeft=i.padLeft||0,this.padRight=i.padRight||0,this.childDef=i.childDef,this.childViews=i.childViews,this.updateHook=i.updateHook,this.onCaptionChanged=i.onCaptionChanged,i.childViews=null,i.updateHook=null,i.childDef=null,i.nameKey=null,i.onCaptionChanged=null,Object.assign(this,i.methods),i.methods=null,this.createDynamicGetters(i),i.visual&&(this.el=document.createElement(i.tag||"div"),this.initAttributes(i.attrs),this.initStyles(i),"button"===i.tag&&(this.el.style.cursor="pointer"),this.initContent(i),i.add&&I.appendChild(this.el),this.hasInitialized=!0),this.initVisibility(i),this.initChildRefs(),t&&(this.viewLogic=new n(this))}}},{key:"orientationProp",value:function(e,t){var n="".concat(t,"l"),i="".concat(t,"p");null!=e[n]&&null!=e[i]&&(e[t]=function(){return C.isLandscape?this[n]:this[i]})}},{key:"orientationProps",value:function(e){for(var t=this,n=arguments.length,i=new Array(n>1?n-1:0),r=1;r<n;r++)i[r-1]=arguments[r];i.forEach((function(n){return t.orientationProp(e,n)}))}},{key:"initContent",value:function(e){if(this.noContent){var t=this.html;null!=t&&(this.el.innerHTML=t)}else this.content=document.createElement("div"),this.content.setAttribute("class","view-content"),this.content.setAttribute("tabindex",-1),Object.assign(this.content.style,{position:"relative","text-align":"center",top:0}),Object.assign(this.content.style,e.contentStyle||{}),this.initAttributes(e.contentAttrs,this.content),null!=e.html&&(this.content.innerHTML=this.html),this.el.appendChild(this.content)}},{key:"initAttributes",value:function(e,t){for(var n in e)if(null!=e[n]){var i=e[n];"id"===n?i=w.kebabCase(i):"tabindex"===n&&-1!==i&&this.el.setAttribute("data-".concat(n),i),(t||this.el).setAttribute(n,i)}}},{key:"initStyles",value:function(e){if(Object.assign(this.el.style,{position:e.position,left:0,top:0,backgroundColor:e.bgColor,border:e.border,overflow:e.overflow,transformOrigin:e.origin,opacity:e.opacity,zIndex:e.z}),null!=E(e,"style.display")){var t=e.style.display;"boolean"==typeof t&&(e.style.display=t?"block":"none")}Object.assign(this.el.style,e.style)}},{key:"initVisibility",value:function(e){!1===e.visible&&this.setVisibility(!1)}},{key:"initChildRefs",value:function(){this.children=[],this.childList=[];for(var e=this.el.querySelectorAll("[data-ref]"),t=0;t<e.length;t++)this.children[e[t].dataset.ref]={el:e[t]}}},{key:"updateStrings",value:function(){null!=this.ariaStringId&&this.el.setAttribute("aria-label",Q.model.getString(this.ariaStringId)),null!=this.updateDomStrings&&(this.updateDomStrings(),this.calcTextSize&&(this.doTextCalcs(),this.update()))}},{key:"updateHtml",value:function(){var e=this.html;null!=e&&(this.noContent?this.el.innerHTML=e:this.content.innerHTML=e)}},{key:"createDynamicGetters",value:function(t){for(var n in t)"id"!==n&&null!=t[n]&&e.prop(this,n,t[n])}},{key:"updateSize",value:function(){"button"===this.tag?(this.el.style.width=x(L(Math.max(this.w,24))),this.el.style.height=x(L(Math.max(this.h,24)))):(this.el.style.width=x(L(this.w)),this.el.style.height=x(L(this.h)))}},{key:"updateTrans",value:function(){var e=R.includes(this.nameKey)?w.identity:L,t=["translate(".concat(x(e(this.x)),", ").concat(x(e(this.y)),")")];if(this.xs)for(var n=0;n<this.xs.length;n++)t.push("translateX(".concat(x(e(this.xs[n])),")"));if(this.ys)for(var i=0;i<this.ys.length;i++)t.push("translateY(".concat(x(e(this.ys[i])),")"));k.deviceView.isMobile&&null!=this.scale&&t.push("scale(".concat(this.scale,")")),this.el.style.transform=t.join(" ")}},{key:"calcChildrensWidth",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:function(){return!0},t=0;return null!=this.children&&this.children.forEach((function(n){e(n)&&(n.update(),t+=n.w)})),t}},{key:"calcChildrensHeight",value:function(){var e=0;return null!=this.children&&this.children.forEach((function(t){t.update(),e+=t.h})),e}},{key:"positionChildren",value:function(e){var t=e.vertical,n=e.toTop,i=e.toLeft,r=e.pad,o=e.startPos,a=e.reverse,l=void 0!==a&&a,s=e.hook,c=void 0===s?w.noop:s,u=e.rtl,f=void 0!==u&&u,d=e.sizeToChildren,h=e.alignChild,y=t?n?F:N:i?H:M,b=Object.assign({l:o,r:o,t:o,b:o},e.bounds),v=l!==f?this.children.slice().reverse():this.children,g=p(t?["height","t"]:["width","l"],2),m=g[0],S=g[1],k=C.isLandscape?w.first:w.last;v.forEach((function(e){(e.beforeReflowHook||w.noop)();var t=h&&k(e.parentAlign);!e.visible&&"reflow"===e.visibility||"-"===t||(t?V[t](e,b,r):y(e,b,r),c(e))})),d&&(this[m]=b[S])}},{key:"flowChildren",value:function(e){(e=Object.assign({pad:0,sizeToChildren:!1,startPos:0,toLeft:!1,reverse:!1,fullUpdate:!1,rtl:!1,hook:function(){}},e)).fullUpdate?this.hasAllChildren()&&(this.positionChildren(e),this.updateSize()):this.positionChildren(e)}},{key:"isBlocked",value:function(){if(k.theme.isClassic)return!1;var e=Q.getTopNameSpace(),t=Q.getCurrentNameSpace(),n=Q.getBlocker(t.name);return null!=e&&null!=n&&n.visible}},{key:"setEnabled",value:function(e,t){this.enabled!=e&&(this.enabled=e,e?j(this.el,"cs-disabled"):T(this.el,"cs-disabled"),this.el.setAttribute("aria-disabled",!e));var n=this.el.getAttribute("data-tabindex");null!=n&&this.el.setAttribute("tabindex",t&&!e||this.isBlocked()?-1:n)}},{key:"setVisibility",value:function(e,t){var n=this.visible!==e,i=!1;return"no-reflow"===this.visibility?(this.el.style.visibility=e?"visible":"hidden",this.el.style.pointerEvents=e?"":"none"):"reflow"===this.visibility&&(this.el.style.display=e?"block":"none",i=!0),t&&(this.layoutDefaultVisible=e),this.visible=e,n&&E(this,"viewLogic.didChangeVisibility")&&this.viewLogic.didChangeVisibility(e),i}},{key:"childVisibilityChanged",value:function(){null!=this.childVisibilityChangedHook?this.childVisibilityChangedHook():null!=this.parent&&this.parent.childVisibilityChanged()}},{key:"update",value:function(e){null==this.beforeUpdateHook||e||this.beforeUpdateHook(),this.updateSize(),this.updateTrans(),null!=this.updateHook&&this.updateHook()}},{key:"updateChildren",value:function(e){this.children.forEach((function(t){t.update(),e&&t.updateChildren(e)}))}},{key:"doTextCalcs",value:function(){var e=1/S.getScale(),t=this.content.style.width,n=this.el.style.display;(DS.flagManager.multiLangSupport||DS.flagManager.aiCourseTranslation)&&(this.el.style.display="block",this.content.style.width="fit-content");var i=this.content.clientWidth*e,r=this.content.clientHeight*e;(DS.flagManager.multiLangSupport||DS.flagManager.aiCourseTranslation)&&(this.content.style.width=t,this.el.style.display=n),this.lastWidthByText=i+4+this.padLeft+this.padRight,this.lastHeightByText=r+4}},{key:"wasAppended",value:function(){var e=this;this.calcTextSize&&this.doTextCalcs(),null!=this.childViews&&this.childViews.forEach((function(t){"string"==typeof t&&(t=Q.getOrCreateView(t)),t.init(e.nameSpace),e.append(t)}))}},{key:"setChildNum",value:function(e){this.defaultChildNum=e}},{key:"hasAllChildren",value:function(){return this.hasInitialized&&this.children.length===this.defaultChildNum}},{key:"append",value:function(e,t){if(e.parent=this,this.children[w.camelCase(e.nameKey)]=e,t?this.children.unshift(e):this.children.push(e),this.noContent||e.outsideContent?this.el.appendChild(e.el):this.content.appendChild(e.el),e.wasAppended(),e.update(),null==e.nameSpace){for(var n=this;null!=n&&null==n.nameSpace;)n=n.parent;e.nameSpace=n.nameSpace,Q.hasNamespace(n.nameSpace)?Q.getNamespace(n.nameSpace)[e.nameKey]=e:console.warn("could not find namespace ".concat(n.nameSpace," when appending"))}}},{key:"destroy",value:function(){null!=this.children&&this.children.forEach((function(e){return e.destroy()})),null!=this.viewLogic&&this.viewLogic.teardown(),null!=this.el.parentNode&&this.el.parentNode.removeChild(this.el),this.nameSpace=null}},{key:"startFloat",value:function(){this.floating=!0,this.lastFloatParent=this.el.parentNode,this.shouldReparent&&Q.getNamespace(this.nameSpace).wrapper.el.appendChild(this.el)}},{key:"endFloat",value:function(){this.floating&&(this.floating=!1,this.shouldReparent&&this.lastFloatParent.appendChild(this.el))}},{key:"right",value:function(){return this.floating?0:this.x+this.w}},{key:"bottom",value:function(){return this.floating?0:this.y+this.h}},{key:"getBox",value:function(){if(null==Q.getNamespace(this.nameSpace).wrapper)return null;var e=k.theme.isClassic&&Q.getNamespace(this.nameSpace).wrapper.dimScale||1,t=(Q.getNamespace(this.nameSpace).wrapper.scale||1)*e,n=this.x,i=this.y,r=this.w,o=this.h,a=this.offsets,l=(a=void 0===a?{}:a).l,s=void 0===l?0:l,c=a.t,u=this;for(n=(n+s)*t,i=(i+(void 0===c?0:c))*t;u=u.parent;){var f=null!=u.parent?t:1,d=u.offsets,h=(d=void 0===d?{}:d).l,p=void 0===h?0:h,y=d.t,b=void 0===y?0:y;n+=(u.x+p)*f,i+=(u.y+b)*f}return{x:n,y:i,w:r*=t,h:o*=t}}}],n&&b(t.prototype,n),i&&b(t,i),Object.defineProperty(t,"prototype",{writable:!1}),e}();W.propFns={"fit-to-text-w":function(){return Math.max(this.minW,this.lastWidthByText)+this.wPad},"fit-to-text-h":function(){return Math.max(this.minH,this.lastHeightByText)},"vertical-center":function(){return this.parent.h/2-this.h/2},"horizontal-center":function(){return this.parent.w/2-this.w/2}},W.prop=function(e,t,n){if("number"==typeof n)e[t]=n;else if("string"==typeof n&&null!=W.propFns[n])Object.defineProperties(e,h({},t,{get:W.propFns[n],set:w.noop}));else if("string"==typeof n&&n.endsWith("%")){var i=parseFloat(n)/100,r=null!=A[t]?A[t]:t;Object.defineProperties(e,h({},t,{get:function(){return this.parent[r]?e.parent[r]*i:0},set:w.noop}))}else if(w.isFunction(n)){var o;if("w"===t||"h"===t){var a=n.bind(e),l=t.toUpperCase(),s=e["min".concat(l)],c=e["max".concat(l)];o=function(){var e=a();return e<s?e=s:e>c&&(e=c),e}.bind(e)}else o=n;Object.defineProperties(e,h({},t,{get:o,set:w.noop}))}else e[t]=n;e[t]=n};var U={},z={},K={},Z=function(e){var t,n,i=d(e);return"string"===i?t=e:"object"===i?n=e[t=Object.keys(e)[0]]:console.warn("invalid view definition. ".concat(e," is a ").concat(d(e))),{viewName:t,children:n}},Q={nameSpaces:{},nsStack:[],getNamespace:function(e){return this.nameSpaces[e]},hasNamespace:function(e){return null!=this.nameSpaces[e]},setModel:function(e){return this.model=e,this},resetStates:function(e){var t=this.getNamespace(e);w.forEach(t,(function(e){return e&&e.setEnabled&&e.setEnabled(!0)}))},updateVisibility:function(e,t){var n=!1;for(var i in e){var r=this.nameSpaces[t][i];if(null!=r)r.setVisibility(e[i],!0)&&(n=!0)}return n},def:function(e,t,n){null==n?t.nameKey=e:n.nameKey=e;var i=new W(t,n);return null==U[e]?(U[e]=i,z[e]={ViewLogic:t,p:n},K[e]=0):console.warn("views connot share the same name ".concat(e)),i},addNameSpace:function(e){g=e,this.nameSpaces[g]=this.nameSpaces[g]||{name:e,topLevelElements:[],isAttached:!0,tabReachable:!0,detach:function(){this.isAttached=!1,this.topLevelElements.forEach((function(e){return I.removeChild(e.el)}))},reattach:function(){this.isAttached=!0,this.topLevelElements.forEach((function(e){return I.appendChild(e.el)}))},updateTabIndex:function(){var e=this,t=!(arguments.length>0&&void 0!==arguments[0])||arguments[0],n=arguments.length>1&&void 0!==arguments[1]&&arguments[1],i=function(t){if(!t.dataset.leavealone){var n=t.getAttribute("data-tabindex");null==n&&(n=t.tabIndex,t.setAttribute("data-tabindex",n)),t.tabIndex=e.tabReachable?n:-1}};this.topLevelElements.forEach((function(r){var o=function(e,t){t?e.el.removeAttribute("aria-hidden"):e.el.setAttribute("aria-hidden",!0)};t&&(r.children.some((function(e){return"startoverlay"===e.nameKey}))?r.children.filter((function(e){return"startoverlay"!==e.nameKey})).forEach((function(t){return o(t,e.tabReachable)})):o(r,e.tabReachable)),r.el.querySelectorAll(n?"[tabIndex]:not(.acc-shadow-el):not(.slide-object)":"[tabIndex]").forEach((function(e){return i(e)})),"lightBoxClose"===r.nameKey&&i(r.el)}))}}},update:function(e){function t(t,n){return e.apply(this,arguments)}return t.toString=function(){return e.toString()},t}((function(e,t){e.forEach((function(e){w.isFunction(e)?e():Array.isArray(e)?update(e,t):null==e||!e.update||e.noUpdate&&!t||(e.update(),t&&e.updateHtml())}))})),getViewConfig:function(e){var t=z[e];return{name:e,ViewLogic:t.ViewLogic,p:t.p}},getOrCreateView:function(e){if(++K[e]>1){var t=z[e],n=t.ViewLogic,i=t.p;return new W(n,i)}return U[e]},tree:function(e,t){for(var n=this,i=[],r=function t(i){if(null!==i){var r=Z(i),o=r.viewName,a=r.children,l=n.nameSpaces[e][o]=n.getOrCreateView(o);if(null!=l){if(l.nameSpace=e,E(a,"length")>0){l.setChildNum(a.length),l.hasChildren=!0;for(var s=0;s<a.length;s++)t(a[s])}}else console.warn("could not find view '".concat(o,"'"))}},o=function t(r,o){if(null!==r){var a=Z(r),l=a.viewName,s=a.children,c=n.nameSpaces[e][l];if(null!=c){if(c.init(e),null!=o?o.append(c):n.nameSpaces[e].topLevelElements.push(c),null!=c.childDef&&c.childDef(),i.push(c),c.hasChildren)for(var u=0;u<s.length;u++)t(s[u],c)}else console.warn("could not find view '".concat(l,"'"))}},a=0;a<t.length;a++)r(t[a]);for(var l=0;l<t.length;l++)o(t[l]);return i},getCurrentNameSpace:function(){return this.nameSpaces[g]},getTopNameSpace:function(){return w.last(this.nsStack)},getCurrentNameSpaceString:function(){return g},getFrameNameSpace:function(){return this.nameSpaces[D]},getBlocker:function(e){switch(e){case DS.constants.refs.FRAME:return Q.getNamespace(e).frameBlocker;case"LightboxWnd":return Q.getNamespace(e).lightBoxBlocker;case"LightboxControlsWnd":return Q.getNamespace(e).lightBoxControlsBlocker;default:return null}}};function G(e){return function(e){if(Array.isArray(e))return e}(e)||X(e)||Y(e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function q(e){return function(e){if(Array.isArray(e))return $(e)}(e)||X(e)||Y(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Y(e,t){if(e){if("string"==typeof e)return $(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?$(e,t):void 0}}function X(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}function $(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,i=new Array(t);n<t;n++)i[n]=e[n];return i}var J,ee=DS,te=ee._,ne=ee.pubSub,ie=ee.events,re={},oe=!1,ae={},le=function(e){try{var t=q(document.styleSheets).find(e);J=q(t.rules)}catch(e){}};if(le((function(e){return null!=e.href&&e.href.includes("output.min.css")})),ne.on(ie.scheme.CHANGED,(function(e){le((function(t){return null!=t.ownerNode&&t.ownerNode.id===e})),ae={}})),null!=J){var se=function(e,t){var n,i;if(oe){n="".concat(t,".cs-").concat(Q.model.frame.default_layout);var r=e.split(" ");i=(r=r.map((function(e){return(e.startsWith(".")?".":"")+te.compact(e.split(".")).reverse().join(".")}))).join(" ")}else{Q.model;n=".cs-".concat(Q.model.frame.default_layout).concat(t),i=e}return"".concat(n," ").concat(i)};re.getColor=function(e,t,n){var i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"",r=se(t,i);if(null==ae[r]){var o=J.find((function(e){return e.selectorText===r}));null==o&&(oe=!oe,r=se(t,i),o=J.find((function(e){return e.selectorText===r}))||{}),ae[r]=o.style||{}}return ae[r][n]||""}}else{var ce=function(e,t){var n=document.createElement(e);return n.setAttribute("class",t),n},ue=/^\./,fe=/\./g;re.getColor=function(e,t,n){var i,r,o=(arguments.length>4&&void 0!==arguments[4]&&arguments[4]||t).split(/\s+/),a=Q.getNamespace(e).wrapper.el;o.forEach((function(e,t){if(ue.test(e))s=e.replace(fe," "),i=ce("div",s);else{var n=G(e.split(".")),o=n[0],l=n.slice(1);i=ce(o,l.join(" "))}var s;0===t&&(i.style.position="absolute",i.style.display="none",r=i),a.appendChild(i),a=i}));var l=window.getComputedStyle(i).getPropertyValue(n);return""===l&&"border-color"===n&&(l=window.getComputedStyle(i).getPropertyValue("border-top-color")),r.parentNode.removeChild(r),l||""}}var de=re;function he(e){return he="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},he(e)}function pe(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,(r=i.key,o=void 0,o=function(e,t){if("object"!==he(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var i=n.call(e,t||"default");if("object"!==he(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(r,"string"),"symbol"===he(o)?o:String(o)),i)}var r,o}var ye,be,ve,ge,me=DS,we=me.dom,Se=me._,ke=me.detection,Ce=me.pubSub,Oe=me.events,Ee=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),Se.bindAll(this,"onLoaderShow","onLoaderMute","onLoaderUnmute","onLoaderHide","onRemoveLoaderTitle");var t={};t[Oe.loader.SHOW]=this.onLoaderShow,t[Oe.loader.MUTE]=this.onLoaderMute,t[Oe.loader.UNMUTE]=this.onLoaderUnmute,t[Oe.loader.HIDE]=this.onLoaderHide,t[Oe.loader.REMOVE_TITLE]=this.onRemoveLoaderTitle,t[Oe.startOverlay.READY]=this.onLoaderHide,we.addClass(document.body,"theme-".concat(window.globals.themeName)),document.body.classList.contains("view-tablet")&&we.addClass(document.body,"is-touchable-tablet"),ke.env.is360&&we.addClass(document.body,"is-360"),Ce.on(t),this.setupBrandingColor()}var t,n,i;return t=e,(n=[{key:"setupBrandingColor",value:function(){window.requestAnimationFrame((function(){var e=de.getColor(DS.constants.refs.FRAME,".cs-brandhighlight-bg","background-color",".cs-base.cs-custom-theme");null!=e&&(DS.constants.theme.brandingHighlight=e,Ce.trigger(Oe.app.BRANDING_COLOR,e))}))}},{key:"onRemoveLoaderTitle",value:function(){var e=document.querySelector("body > .mobile-load-title-overlay");null!=e&&e.parentNode.removeChild(e)}},{key:"getSpinLoader",value:function(){return document.querySelector("body > .slide-loader")}},{key:"onLoaderMute",value:function(){var e=this.getSpinLoader();null!=e&&(e.style.opacity=0)}},{key:"showLoaderDelayed",value:function(e){clearTimeout(this.loaderTimeout),this.loaderTimeout=setTimeout(this.onLoaderShow,e)}},{key:"onLoaderHide",value:function(){clearTimeout(this.loaderTimeout),this.getSpinLoader().style.display="none",we.addClass(document.getElementById("preso"),"hide-slide-loader"),Ce.trigger(Oe.app.HIDE_LOADER)}},{key:"onLoaderUnmute",value:function(){var e=this.getSpinLoader();null!=e&&(e.style.opacity=1)}},{key:"onLoaderShow",value:function(e){e>0?this.showLoaderDelayed(e):(this.getSpinLoader().style.display="block",we.removeClass(document.getElementById("preso"),"hide-slide-loader"),Ce.trigger(Oe.app.SHOW_LOADER))}}])&&pe(t.prototype,n),i&&pe(t,i),Object.defineProperty(t,"prototype",{writable:!1}),e}(),Le=DS,xe=Le.globalEventHelper.addWindowListener,Pe=Le.events,Te=Pe.ds,je=Te.FRAME_DATA_LOADED,_e=Te.PRESO_READY,De=Pe.window.STACKING_CHANGED,Ie=Pe.frame,Ae=Ie.MODEL_READY,Re=Ie.SCALE,Be=Pe.resume.SET_DATA,Me=Pe.startOverlay.READY,He=Pe.controlOptions.RESET,Ne=Pe.sidebar.ACTIVE_TAB_SET,Fe=Pe.renderTree.DESTROYED,Ve=Le.constants,We=(Ve.els.PRESO,Ve.refs.FRAME),Ue=Le.detection.theme,ze=Le.pubSub,Ke=Le.focusManager,Ze=(Le.flagManager,Le.playerGlobals),Qe=Le.stringTabler,Ge=Le.shortcutManager,qe=Le.dom,Ye={},Xe=function(e){var t=Q.nsStack.indexOf(e);t>=0&&Q.nsStack.splice(t,1)},$e=function(){var e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];Q.nsStack.forEach((function(t,n,i){t.tabReachable=n===i.length-1&&e,t.updateTabIndex()}))},Je=function(e){ye={createWindow:function(t){var n,i,r=this,o=Q.getNamespace(t),a=function(){return function(e){var t=Q.getNamespace(e),n=t.isAttached;t.reattach(),Xe(t),Q.nsStack.push(t),n||Ye[e].all();var i=Q.nsStack.length;Q.nsStack.forEach((function(e,t){var n=e.slide.el;t===i-1?(qe.addClass(n,"primary-slide"),qe.removeClass(n,"secondary-slide")):(qe.removeClass(n,"primary-slide"),qe.addClass(n,"secondary-slide"))})),$e(),ze.trigger(De,e)}(t)},l=function(e,n,i){if(e===t){var r=i?i.getWndBlockerBackground():"",a=Q.getBlocker(e);null!=a&&(a.setBackgroundColor(r),a.setVisibility(n)),o.tabReachable=!n,o.updateTabIndex(!1,!0)}};return null==o&&(Q.addNameSpace(t),i=e[t](be),n=_.flow(xe("resize",(function(){i.resize(),ze.trigger(Re)})),DS.pubSub.addListener(DS.events.utilityWindow.DOCKED,(function(){i.resize(),ze.trigger(Re)}))),t===We?be.setLayout(be.frame.default_layout,We):i.all(),o=Q.getNamespace(t),Ye[t]=i,o.moveToTop=a,Ue.isUnified&&(ze.on(DS.events.frameModel.BLOCKED_CHANGED,l),ze.on(DS.events.window.MAIN_CHANGED,(function(e,t){return l(t,!1)})))),a(),ze.on(He,(function e(){var t=function(){var e=[],t=Q.getNamespace(We).tabs.viewLogic.getSelectedTab();return t&&e.push(t.nameKey),_.each(Q.getNamespace(We).topTabs.children,(function(t){_.each(_.filter(t.children,"viewLogic.showing",!0),(function(t){return e.push(t.nameKey)}))})),e}();Xe(Q.getNamespace(We)),i.destroy(),DS.pubSub.trigger(Fe),Q.nameSpaces[We]=null,n(),ze.off(He,e),r.createWindow("_frame"),i.rerender(),t.forEach((function(e){ze.trigger(Ne,e)}))})),{id:t,el:o.slide.el,wndEl:o.wrapper.el,captionEl:(o.captionContainer||{}).el,x:function(){return o.wrapper.x},y:function(){return o.wrapper.y},close:function(){o.zoomBounds=null,o.detach(),Xe(o),Q.nsStack[Q.nsStack.length-1].moveToTop(),Ke.reCenter()},moveToTop:a,getWinScale:function(){return o.slide.winScale||1},getPinchZoomBounds:function(){return o.slide.pinchZoomBounds},onPinchZoom:Ye[t].pinchZoom?function(e){o.zoomBounds=e,Ye[t].pinchZoom()}:function(){},onWndBlockedChanged:l}},getSidebarPosition:function(){return be.sidebarOpts.sidebarPos},getDefaultLayout:function(){return ve.default_layout},getFonts:function(){return ve.fonts},getFontScale:function(){return be.frame.fontscale},getCaptionData:function(){var e=be.frame.controlOptions.controls,t={font:e.font,enabled:e.closed_captions},n=be.frame.controlOptions.ccOptions||{};return t.font=n.font,t.size=n.size,t.placement=n.placement,t.backgroundColor=n.backgroundColor,t.color=n.color,t.text=n.text,t},getNavData:function(){return be.frame.navData.outline.links},isReadOnlyOnce:function(){return ve.controlOptions.controls.readonlyOnce},topmostUnreachable:function(){return Q.nsStack},setAllAccVisibility:function(e){_.forEach(Q.nameSpaces,(function(t){var n=t.wrapper;e?n.el.removeAttribute("aria-hidden"):n.el.setAttribute("aria-hidden",!0)}))}},ze.on(Be,$e),ze.on(Me,(function(){$e(!1)})),Ze.player=ye},et=function(){ye.hasData=!0,ze.trigger(je,ye)},tt=DS,nt=tt.MicroScrollBar,it=tt.detection,rt=function(e){return null!=e&&2===e.split(".").length},ot=function(e,t){return e.some((function(e){var n=t[e.name];return null==n||"outline"===e.name&&n.enabled||!0===n}))};function at(e){return at="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},at(e)}function lt(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,(r=i.key,o=void 0,o=function(e,t){if("object"!==at(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var i=n.call(e,t||"default");if("object"!==at(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(r,"string"),"symbol"===at(o)?o:String(o)),i)}var r,o}var st,ct=DS.detection,ut=new(function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.minSize=Math.min(window.innerWidth,window.innerHeight),this.isMobileChrome=ct.os.isIOS&&ct.browser.isChrome}var t,n,i;return t=e,(n=[{key:"shouldUseCache",value:function(){return this.isMobileChrome&&document.activeElement.classList.contains("acc-textinput")}},{key:"height",get:function(){return this.shouldUseCache()?(this.cachedOuterHeight!==window.outerHeight&&(this.cachedHeight=window.outerHeight-(this.cachedOuterHeight-this.cachedHeight),this.cachedOuterHeight=window.outerHeight),this.cachedHeight):(this.cachedOuterHeight=window.outerHeight,this.cachedHeight=window.innerHeight,window.innerHeight)}},{key:"width",get:function(){return window.innerWidth}}])&&lt(t.prototype,n),i&&lt(t,i),Object.defineProperty(t,"prototype",{writable:!1}),e}()),ft="wrapper";Q.def(ft,(function(){var e=Q.model.frame;return{attrs:{id:ft,class:"cs-base cs-".concat(e.default_layout," fn-").concat(e.default_layout," cs-custom-theme")},style:{fontSize:"".concat(e.fontscale,"%")},x:0,y:0,w:function(){return ut.width},h:function(){return ut.height},updateHook:function(){this.el.style.fontSize="".concat(e.fontscale,"%")},add:!0}}));var dt,ht="-20px",pt="-30px";var yt={setTooltipOn:function(e,t,n){var i,r,o=t.left,a=t.top,l=t.width,s=t.height;null==st&&(i=Q.model,r=de.getColor(DS.constants.refs.FRAME,".cs-button .cs-icon","fill"),(st=document.createElement("div")).id="tooltip",st.style.left=ht,st.style.top=pt,st.style.borderColor=r,st.classList.add("cs-base","cs-".concat(i.frame.default_layout)),document.getElementById("app-top").appendChild(st),st.addEventListener("mouseleave",yt.runObjHoverOut)),dt=e,st.style.display="block",st.innerText=n;var c=st.getBoundingClientRect().width,u=window.innerWidth,f=o+(l/2-c/2);f+c>=u-10?f=u-10-c:f<=10&&(f=10);var d=a-(s+10);d<=10&&(d=a+s+10),st.style.left="".concat(f,"px"),st.style.top="".concat(d,"px")},hideTooltip:function(e){null==st||null!=e&&e!==dt||(st.style.display="none",st.innerText="",st.style.left=0,dt=void 0)},updateTooltip:function(e,t){var n=st.getBoundingClientRect().width;st.innerText=t,st.style.left="".concat(e-n/2,"px")},runObjHoverOut:function(){null!=dt?dt.onHoverOut():yt.hideTooltip()}},bt=yt;function vt(e){return vt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},vt(e)}function gt(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,(r=i.key,o=void 0,o=function(e,t){if("object"!==vt(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var i=n.call(e,t||"default");if("object"!==vt(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(r,"string"),"symbol"===vt(o)?o:String(o)),i)}var r,o}var mt=DS,wt=mt.dom,St=mt.pubSub,kt=mt.events,Ct=mt._,Ot=mt.constants,Et=mt.keyManager,Lt=mt.focusManager,xt=(mt.flagManager,mt.stringTabler),Pt=mt.detection,Tt="click",jt=function(){return Pt.deviceView.isUnifiedDesktop},_t=function(){function e(t){var n=this;for(var i in function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.el=t.el,this.view=t,this.model=t.model,t.children)this[i+"El"]=t.children[i].el;DS.flagManager.multiLangSupport||DS.flagManager.aiCourseTranslation?(t.simpleView||(DS._.bindAll(this,"onFocus","onBlur","onLayoutChange"),this.el.addEventListener("focusin",this.onFocus),this.el.addEventListener("focusout",this.onBlur),St.on(kt.frameModel.LAYOUT_CHANGED,this.onLayoutChange)),St.on(kt.strings.UPDATE_STRINGS,(function(){return n.onStringsUpdated()}))):(DS._.bindAll(this,"onFocus","onBlur","onLayoutChange"),this.el.addEventListener("focusin",this.onFocus),this.el.addEventListener("focusout",this.onBlur),St.on(kt.frameModel.LAYOUT_CHANGED,this.onLayoutChange)),jt()&&(Ct.bindAll(this,"onHoverIn","onHoverOut","onMainKeydown"),document.body.addEventListener("mouseenter",this.onHoverOut),this.el.addEventListener("mouseenter",this.onHoverIn),this.el.addEventListener("mouseleave",this.onHoverOut),this.el.addEventListener("mouseup",this.onHoverOut),document.addEventListener("keydown",this.onMainKeydown))}var t,n,i;return t=e,(n=[{key:"onClick",value:function(e){this.el.addEventListener(Tt,e.bind(this))}},{key:"onClickEl",value:function(e,t){e.addEventListener(Tt,t.bind(this))}},{key:"on",value:function(e,t){this.el.addEventListener(e,t.bind(this))}},{key:"getViewBox",value:function(){return this.view.getBox()}},{key:"onFocus",value:function(e){var t=this.getViewBox(),n=t.x,i=t.y,r=t.w,o=t.h;Lt.setFocusRectOn(this.el,{left:n,top:i,width:r,height:o}),this.onHoverIn(e),this.hasFocus=!0}},{key:"onBlur",value:function(e){Lt.takeFocusOff(),this.onHoverOut(e),this.hasFocus=!1}},{key:"getTooltipString",value:function(){return xt.getString(this.tooltipKey)}},{key:"onHoverIn",value:function(e){var t=this;if(jt()&&this.hasTooltip&&(null==this.hideTooltipWhenOpen||!this.isOpen)){var n=this.getTooltipString(e);if(null==n&&"seek"===this.view.nameKey)return void setTimeout((function(){return t.onHoverIn(e)}),200);bt.hideTooltip();var i=this.getViewBox(e),r=i.x,o=i.y,a=i.w,l=i.h;bt.setTooltipOn(this,{left:r,top:o,width:a,height:l},n),this.tooltipMoves&&this.setForFollowMouse(),this.tooltipOn=!0}}},{key:"dismissTooltip",value:function(){bt.hideTooltip(this),this.tooltipMoves&&this.stopFollowMouse(),this.tooltipOn=!1}},{key:"onHoverOut",value:function(){var e=this;jt()&&this.hasTooltip&&this.tooltipOn&&setTimeout((function(){var t=document.elementFromPoint(wt.mouseX,wt.mouseY);null!=t&&(e.el.contains(t)||"tooltip"===t.id||e.dismissTooltip())}),200)}},{key:"onMainKeydown",value:function(e){Et.isKey(e.which,Ot.keys.ESCAPE)&&(e.stopPropagation(),this.dismissTooltip())}},{key:"teardown",value:function(){St.off(kt.frameModel.LAYOUT_CHANGED,this.onLayoutChange),Pt.deviceView.isUnifiedDesktop&&(document.body.removeEventListener("mouseenter",this.onHoverOut),this.el.removeEventListener("mouseenter",this.onHoverIn),this.el.removeEventListener("mouseleave",this.onHoverOut),this.el.removeEventListener("mousedown",this.dismissTooltip),document.removeEventListener("keydown",this.onMainKeydown),this.tooltipOn&&(this.onHoverOut(),this.tooltipMoves&&this.stopFollowMouse()))}},{key:"onLayoutChange",value:function(){}},{key:"onStringsUpdated",value:function(){this.view.updateStrings()}}])&&gt(t.prototype,n),i&&gt(t,i),Object.defineProperty(t,"prototype",{writable:!1}),e}();function Dt(e){return Dt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Dt(e)}function It(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,(r=i.key,o=void 0,o=function(e,t){if("object"!==Dt(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var i=n.call(e,t||"default");if("object"!==Dt(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(r,"string"),"symbol"===Dt(o)?o:String(o)),i)}var r,o}function At(e,t){return At=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},At(e,t)}function Rt(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,i=Bt(e);if(t){var r=Bt(this).constructor;n=Reflect.construct(i,arguments,r)}else n=i.apply(this,arguments);return function(e,t){if(t&&("object"===Dt(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,n)}}function Bt(e){return Bt=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},Bt(e)}var Mt="frame",Ht=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&At(e,t)}(o,e);var t,n,i,r=Rt(o);function o(e){var t;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,o),(t=r.call(this,e)).onScroll=function(e){e.preventDefault(),t.el.scrollTop=0,t.el.scrollLeft=0},t.el.addEventListener("scroll",t.onScroll),t}return t=o,(n=[{key:"teardown",value:function(){this.el.removeEventListener("scroll",this.onScroll)}},{key:"onFocus",value:function(){}},{key:"onBlur",value:function(){}}])&&It(t.prototype,n),i&&It(t,i),Object.defineProperty(t,"prototype",{writable:!1}),o}(_t);function Nt(e){return Nt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Nt(e)}function Ft(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,(r=i.key,o=void 0,o=function(e,t){if("object"!==Nt(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var i=n.call(e,t||"default");if("object"!==Nt(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(r,"string"),"symbol"===Nt(o)?o:String(o)),i)}var r,o}function Vt(e,t){return Vt=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},Vt(e,t)}function Wt(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,i=zt(e);if(t){var r=zt(this).constructor;n=Reflect.construct(i,arguments,r)}else n=i.apply(this,arguments);return function(e,t){if(t&&("object"===Nt(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return Ut(e)}(this,n)}}function Ut(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function zt(e){return zt=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},zt(e)}Q.def(Mt,Ht,(function(){var e=Q.model;return{attrs:{id:Mt,class:"cs-base cs-".concat(e.frame.default_layout," fn-").concat(e.frame.default_layout)},w:"100%",h:"100%",html:'<div class="top-ui-bg"></div><div class="bottom-ui-bg"></div><div class="left-ui-bg"></div><div class="right-ui-bg"></div>'}}));var Kt,Zt,Qt=DS,Gt=Qt.detection,qt=Qt.events,Yt=Qt.pubSub,Xt=function(){return document.getElementById("app-top")||document.body},$t=DS.utils.pxify,Jt=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Vt(e,t)}(o,e);var t,n,i,r=Wt(o);function o(e){var t,n;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,o),(t=r.call(this,e)).visualNode=((n=document.createElement("div")).className="skipnav transparent",n.innerText=Q.model.getString("acc_skipnavigation"),Xt().appendChild(n),window.requestAnimationFrame((function(){Zt=n.clientHeight,Kt=n.clientWidth,n.className="skipnav",null!=n.parentNode&&n.parentNode.removeChild(n)})),n),DS._.bindAll(Ut(t),"onButtonClick"),t.onClick(t.onButtonClick),t}return t=o,n=[{key:"onFocus",value:function(){if(!Gt.device.isMobile){DS.focusManager.takeFocusOff();var e=Q.getTopNameSpace().slide,t=e.getBox(),n=t.x,i=t.y,r=t.w,o=t.h;Object.assign(this.visualNode.style,{top:$t(i+o-Zt-50),left:$t(n+r-Kt-50)}),e.el.style.opacity=.6,Xt().appendChild(this.visualNode),Yt.trigger(qt.skipNav.FOCUSED)}}},{key:"onBlur",value:function(){var e=Q.getTopNameSpace().slide,t=this.visualNode.parentNode;e.el.style.opacity=1,null!=t&&t.removeChild(this.visualNode)}},{key:"onButtonClick",value:function(e){this.el.focus(),setTimeout((function(){DS.focusManager.onSlideStarted(null,DS.windowManager.getCurrentWindowSlide())}),100)}}],n&&Ft(t.prototype,n),i&&Ft(t,i),Object.defineProperty(t,"prototype",{writable:!1}),o}(_t),en=Jt,tn="skipnav";function nn(e){return nn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},nn(e)}function rn(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==nn(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var i=n.call(e,t||"default");if("object"!==nn(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===nn(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}Q.def(tn,en,(function(){var e=Q.model,t=e.getString("acc_skipnavigation");return{tag:"button",ariaStringId:"acc_skipnavigation",attrs:{id:tn,"aria-label":t,tabindex:0},visible:e.sidebarOpts.sidebarEnabled,x:-100,y:-100,w:1,h:1,html:t,methods:{updateDomStrings:function(){this.el.textContent=e.getString("acc_skipnavigation")}}}}));var on=DS,an=on._,ln=on.pubSub,sn=on.events,cn=on.detection,un=on.constants.MOBILE_UI_SIZE,fn="top ".concat(150,"ms ease-in-out");function dn(e){return dn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},dn(e)}function hn(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,(r=i.key,o=void 0,o=function(e,t){if("object"!==dn(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var i=n.call(e,t||"default");if("object"!==dn(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(r,"string"),"symbol"===dn(o)?o:String(o)),i)}var r,o}function pn(e,t){return pn=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},pn(e,t)}function yn(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,i=vn(e);if(t){var r=vn(this).constructor;n=Reflect.construct(i,arguments,r)}else n=i.apply(this,arguments);return function(e,t){if(t&&("object"===dn(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return bn(e)}(this,n)}}function bn(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function vn(e){return vn=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},vn(e)}var gn=DS,mn=gn.pubSub,wn=gn.events,Sn=gn._,kn=gn.utils,Cn=(gn.flagManager,function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&pn(e,t)}(o,e);var t,n,i,r=yn(o);function o(e){var t;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,o),t=r.call(this,e),Sn.bindAll(bn(t),"onSlideChange","onAriaToggle"),mn.on(wn.slide.HAS_MOUNTED,t.onSlideChange),mn.on(wn.slide.ARIA_TOGGLE,t.onAriaToggle),t.teardownPushableSlide=function(e){var t;if(!cn.deviceView.isPhone)return an.noop;function n(){e.view.el.style.transition=null,e.view.el.style.top="0px"}function i(t){e.view.el.style.transition=fn;var n=.45*window.innerHeight-10,i=-(e.view.bottom()-n),r=e.view.y+i,o=0;r<un&&cn.theme.isUnified?o=un-r+10:r<0&&(o=-1*r+10),e.view.el.style.top="".concat(i+o,"px")}function r(){e.view.el.style.transition=fn,e.view.el.style.top="".concat(0,"px")}return ln.on((rn(t={},sn.threeSixtyImage.UN_PUSH_LABEL,n),rn(t,sn.threeSixtyImage.PUSH_UP_BY_LABEL,i),rn(t,sn.threeSixtyImage.PUSH_DOWN_BY_LABEL,r),t)),function(){n(),ln.off(sn.threeSixtyImage.UN_PUSH_LABEL,n),ln.off(sn.threeSixtyImage.PUSH_UP_BY_LABEL,i),ln.off(sn.threeSixtyImage.PUSH_DOWN_BY_LABEL,r)}}(bn(t)),t}return t=o,(n=[{key:"onAriaToggle",value:function(e){e.hidden?(this.el.setAttribute("aria-hidden",!0),this.el.setAttribute("tabindex",0)):(this.el.removeAttribute("aria-hidden"),this.el.removeAttribute("tabindex"))}},{key:"teardown",value:function(){mn.off(wn.slide.HAS_MOUNTED,this.onSlideChange),this.teardownPushableSlide()}},{key:"onSlideChange",value:function(e){null!=this.labelEl&&this.view.nameSpace===e.props.windowId&&(this.labelEl.textContent="".concat(Q.model.getString("slide"),": ").concat(kn.stripTags(e.props.model.title())))}},{key:"onFocus",value:function(){}},{key:"onBlur",value:function(){}}])&&hn(t.prototype,n),i&&hn(t,i),Object.defineProperty(t,"prototype",{writable:!1}),o}(_t)),On=10,En=58,Ln=65,xn=DS,Pn=xn.detection,Tn=Pn.deviceView,jn=Pn.orientation,_n=xn.constants.MOBILE_UI_SIZE,Dn="slide";Q.def(Dn,Cn,(function(e){var t=Q.getNamespace(e),n=t.frame,i=t.sidebar,r=t.topBar,o=t.bottomBar,a=t.fullScreenToggle,l=Q.model;return{attrs:{id:Dn,"aria-live":"assertive",class:"window-slide ".concat(e,"-slide")},origin:"0 0",overflow:"visible",w:function(){return l.slideWidth*this.currWinScale},h:function(){return l.slideHeight*this.currWinScale},z:1,methods:{calcSidebarOffsets:function(){var e=null!=i&&i.visible,t=e&&"left"===i.pos;this.sidebarWidthOffset=t?i.right():e?ut.width-i.x:0,this.sidebarXOffset=t?i.right():0},isFullScreenChromeless:function(){return null!=a&&l.frame.chromeless},topBarExists:function(){var e=null!=r&&!!r.visible&&(l.sidebarOpts.titleEnabled||l.sidebarOpts.sidebarEnabled||l.hasTopLinks()||l.frame.controlOptions.controls.elapsedandtotaltime);return document.body.classList[e?"remove":"add"]("top-bar-hidden"),e},bottomBarExists:function(){var e=!1;null!=o&&o.visible&&o.children&&(e=o.children.some((function(e){return e.visible})));var t=l.bottomBarOpts.bottomBarEnabled&&e;return document.body.classList[t?"remove":"add"]("bottom-bar-hidden"),t},sideBarsExist:function(){var e=!l.frame.chromeless;return document.body.classList[e?"remove":"add"]("side-bars-hidden"),e},calcWinScale:function(){var e=null!=n?30:0,t=0,i=this.topBarExists(),a=this.bottomBarExists();this.isFullScreenChromeless()?t=_n:Tn.isTablet&&(t+=a?o.h:0,t+=i?r.h:0),i||a||(e=0),this.offY=0,this.minY=0;var s=0;(Tn.isTablet||jn.isPortrait)&&(!i&&a?(this.offY=-o.h,s+=15,this.minY=15):i&&!a&&(this.offY=25,s+=15,this.minY=15));var c=Tn.isPhone&&jn.isLandscape&&this.sideBarsExist(),u=l.dockSettings,f=u.dockedState,d=u.width,h=f!==DS.constants.docked.NONE?d:0,p=c?En:0;this.availW=ut.width-this.sidebarWidthOffset-h-2*p;var y=Tn.isPhone&&jn.isPortrait&&!l.frame.chromeless?Ln:0,b=ut.height-((Tn.isPhone?e:0)+t)-s-2*y;this.currWinScale=Math.min((this.availW-e)/l.slideWidth,b/l.slideHeight)},beforeUpdateHook:function(){this.calcSidebarOffsets(),this.calcWinScale()}},winScale:function(){return this.currWinScale},pinchZoomBounds:function(){var e=ut.width,t=ut.height,n=this.topBarExists()?r.h:0,i=this.bottomBarExists()?o.h:0;return Tn.isPhone&&jn.isLandscape?{x:En,y:0,width:e-116,height:t}:{x:this.sidebarXOffset,y:n,width:this.availW,height:t-(i+n)}},x:function(){var e=l.dockSettings,t=e.dockedState,n=e.width,i="left"===t?n:0,r=Tn.isPhone&&jn.isLandscape&&this.sideBarsExist(),o=l.slideWidth*this.currWinScale,a=r?En:0;return i+this.sidebarXOffset+(this.availW-o)/2+a},y:function(){var e=(ut.height-l.slideHeight*this.currWinScale)/2+this.offY;return Math.max(e,this.minY)},add:!0,html:'<div id="slide-label" data-ref="label" aria-live="polite"></div><main class="slide-container" data-ref="container" tabindex="-1"></main>',noContent:!0}}));var In="logo";DS.flagManager;function An(e){return function(e){if(Array.isArray(e))return Rn(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"==typeof e)return Rn(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Rn(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Rn(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,i=new Array(t);n<t;n++)i[n]=e[n];return i}Q.def(In,(function(e){var t,n=Q.model,i=Q.getNamespace(e).sidebar,r=i.w,o=.75*r;return{id:In,attrs:{class:"logo cs-logo",role:"banner"},w:"100%",h:170,html:function(){(t=document.createElement("img")).onload=function(){r=t.naturalWidth,o=t.naturalHeight,t.onload=null,i.updateChildren(!0)},t.src=DS.utils.resolveAssetUrl(n.sidebarOpts.html5_logo_url),n.sidebarOpts.logoAltText&&(t.alt=n.sidebarOpts.logoAltText),this.el.appendChild(t)},visible:n.sidebarOpts.logoEnabled,updateHook:function(){o/170>r/i.data.actualWidth()?(t.style.width="auto",t.style.height="100%"):(t.style.width="100%",t.style.height="auto")}}}));var Bn="sidebar",Mn=DS,Hn=Mn.utils,Nn=Mn.TweenLite,Fn=Mn.dom,Vn=Mn.events,Wn=Mn.pubSub,Un=Mn.detection;Q.def(Bn,(function(e){var t=Q.model,n=de.getColor(e,".cs-brandhighlight-bg","background-color",".cs-base.cs-custom-theme"),i=t.sidebarOpts.sidebarPos;Fn.addClass(document.body,"sidebar-".concat(i));var r=.25,o=function(){var e=t.dockSettings||{},n=e.dockedState,i=e.width,r=n!==DS.constants.docked.NONE,o=window.innerWidth-(r?i:0),a=Un.device.isPhone?.8*window.innerWidth:o/4;return Hn.clamp(245,400,a)},a=function(){return"left"===i?0:window.innerWidth-o()},l={x:a()},s=[];return{tag:"section",attrs:{id:Bn,class:"cs-left area-secondary-wrapper","aria-label":"sidebar"},overflow:"visible",visible:t.sidebarOpts.sidebarEnabled,pos:i,noContent:!1,shouldReparent:!1,xs:[0],data:{actualWidth:o},x:function(){var e=this,t=a();if(this.collapsed&&("left"===this.pos?t-=o():t+=o()),this.animate&&!this.animating){var n="left"===this.pos?-30:30;this.collapsed&&this.floating?(Fn.addClass(document.body,"sidebar-closing"),Nn.to(this.content,r,{opacity:0,x:n,ease:"power4.out",onComplete:function(){Fn.removeClass(document.body,"sidebar-closing"),Fn.addClass(document.body,"sidebar-closed")}})):(Fn[this.collapsed?"addClass":"removeClass"](document.body,"sidebar-closed"),Nn.set(this.content,{x:n,opacity:0}),Nn.to(this.content,r,{x:0,delay:.11,ease:"power2.out"}),Nn.to(this.content,r,{opacity:1,delay:.11,ease:"none",overwrite:!1}));var i=window.innerWidth,s=Nn.to(l,r,{x:t,onComplete:function(){e.animating=!1,e.animate=!1},onUpdate:function(){"right"===e.pos&&window.innerWidth!=i&&(s.kill(),e.animating=!1),Wn.trigger(Vn.frame.REFLOW),Wn.trigger(Vn.frame.SCALE)}});this.animating=!0}return this.animating||(l.x=t),this.floating?"right"===this.pos?(this.xs[0]=l.x-window.innerWidth,window.innerWidth):(this.xs[0]=l.x,0):(this.xs[0]=0,l.x)},w:function(){return this.visible?o():0},h:"100%",html:"\n      <style>\n        .selected-animation-done:after {\n          background: ".concat(n,';\n        }\n      </style>\n      <div class="slider-mask" data-ref="sliderMask">\n        <div class="tab-selected-slider cs-brandhighlight-bg" data-ref="tabSelectedSlider"></div>\n      </div>\n    '),updateHook:function(){this.hasAllChildren()&&this[this.collapsed?"onSidebarHide":"onSidebarShow"]()},methods:{onSidebarHide:function(){this.onSidebarShow(),(s=An(this.el.querySelectorAll('[tabindex="0"]:not(#hamburger)'))).forEach((function(e){e.tabIndex=-1})),this.content.setAttribute("aria-hidden",!0)},onSidebarShow:function(){s.forEach((function(e){e.tabIndex=0})),this.content.removeAttribute("aria-hidden")}}}}));var zn=function(){return"no icon"},Kn={next:function(){return'\n  <svg class="cs-icon next-icon" width="10px" height="18px" viewBox="0 -1 10 18" focusable="false">\n    <path transform="rotate(180, 5, 8)" d="M2.81685219,7.60265083 L9.00528946,1.41421356 L7.5910759,-1.27897692e-13 L1.55431223e-13,7.5910759 L0.0115749356,7.60265083 L1.55431223e-13,7.61422577 L7.5910759,15.2053017 L9.00528946,13.7910881 L2.81685219,7.60265083 Z" stroke="none" fillRule="evenodd"></path>\n  </svg>'},prev:function(){return'\n  <svg class="cs-icon prev-icon" width="10px" height="18px" viewBox="0 -1 10 18" focusable="false">\n    <path transform="translate(0, 1)" d="M2.81685219,7.60265083 L9.00528946,1.41421356 L7.5910759,-1.27897692e-13 L1.55431223e-13,7.5910759 L0.0115749356,7.60265083 L1.55431223e-13,7.61422577 L7.5910759,15.2053017 L9.00528946,13.7910881 L2.81685219,7.60265083 Z" stroke="none" fillRule="evenodd"></path>\n  </svg>\n'},submit:function(){return'\n   <svg class="cs-icon check-icon" width="17px" height="18px" viewBox="0 0 17 16" focusable="false">\n\n  <path stroke="none" transform="translate(0, 1)" d="\n  M 17 1.4\n  L 15.6 0 5.7 9.9 1.4 5.65 0 7.05 5.65 12.75 5.7 12.75 17 1.4 Z"/>\n\n  </svg>'},replay:function(){return'<svg class="cs-icon" x="0px" y="0px" width="16px" height="16px" viewBox="0 0 16 16" focusable="false">\n    <path fill="#FFFFFF" stroke="none" d="\n      M 10.95 8.75\n      Q 11 9 11 9.25 10.95 11.15 9.7 12.4 8.4 13.7 6.5 13.75 4.6 13.7 3.3 12.4 2.05 11.15 2 9.25 2.05 7.3 3.3 6.05 4.398828125 4.998828125 6 4.75\n      L 6 6.9\n      Q 6.05 7.75 6.85 7.35\n      L 11.35 4.3\n      Q 11.7 4.05 11.7 3.75 11.7 3.45 11.35 3.2\n      L 6.85 0.15\n      Q 6.05 -0.3 6 0.6\n      L 6 2.75\n      Q 3.4517578125 3.001171875 1.8 4.75 0.05 6.6 0 9.25 0.05 12 1.9 13.85 3.75 15.65 6.5 15.75 9.25 15.65 11.1 13.85 12.95 12 13 9.25 13 9 13 8.75\n      L 10.95 8.75 Z"/>\n    </svg>'},play:function(){return'<svg id="icon-play" class="cs-icon play-icon" width="11" height="13" viewBox="0 0 11 13" focusable="false">\n    <path fill="#FFFFFF" stroke="none" d="\n      M 0.851 13.011\n      C 0.381 13.295 0 13.068 0 12.526\n      L 0 0.771\n      C 0 0.219 0.378 0 0.854 0.288\n      L 10.507 6.132\n      C 10.979 6.417 10.981 6.878 10.504 7.168\n      L 6.307 9.708\n      L 0.851 13.011 Z" />\n  </svg>'},pause:function(){return'<svg id="icon-pause" class="cs-icon pause-icon" width="9" height="14" viewBox="0 0 9 14" focusable="false">\n    <rect x="0" width="3" height="14"/>\n    <rect x="6" width="3" height="14"/>\n  </svg>'},volume:function(e,t){var n=Math.min(1,e/5),i=Math.min(1,Math.max(0,e/5-.5));return'<svg class="cs-icon volume-icon '.concat(t?"volume-icon-selected":"",'" width="16px" height="14px" viewBox="0 0 16 14" focusable="false">\n      <rect x="0" y="4" width="3" height="6"></rect>\n      <polygon points="4 4 9 0 9 14 4 10"></polygon>\n      <g transform="translate(10, 0)">\n        <mask id="vol-mask" fill="white">\n          <rect id="path-1" x="0" y="0" width="8" height="14" style="fill: white;"></rect>\n        </mask>\n        <circle strokeWidth="1.5" style="opacity: ').concat(i,';" mask="url(#vol-mask)" fill="none" cx="-1" cy="7" r="6.5"></circle>\n        <circle strokeWidth="1.5" style="opacity: ').concat(n,';" mask="url(#vol-mask)" fill="none" cx="-1" cy="7" r="3.5"></circle>\n      </g>\n    </g>\n  </svg>')},captionsOn:function(e){return'<svg class="cs-icon captions-icon" width="19px" height="16px" viewBox="0 0 19 16" focusable="false">\n            <path fill="#FFFFFF" stroke="none" d="M 19 2 Q 19 1.15 18.4 0.6 17.85 0 17 0 L 2 0 Q 1.15 0 0.6 0.6 0 1.15 0 2 L 0 12 Q 0 12.85 0.6 13.4 1.15 14 2 14 L 7.6 14 9.5 15.9 11.4 14 17 14 Q 17.85 14 18.4 13.4 19 12.85 19 12 L 19 2 M 15.7 4.2 L 15.25 4.85 Q 15.15 4.9 15.1 5 15 5.05 14.85 5.05 14.75 5.05 14.6 4.95 14.5 4.9 14.3 4.8 14.15 4.65 13.9 4.6 13.65 4.5 13.3 4.5 12.85 4.5 12.5 4.7 12.15 4.85 11.9 5.15 11.7 5.45 11.6 5.9 11.45 6.35 11.45 6.9 11.5 7.45 11.6 7.9 11.7 8.35 11.95 8.65 12.2 8.95 12.5 9.15 12.85 9.3 13.25 9.3 13.65 9.3 13.9 9.2 14.2 9.1 14.35 8.95 14.5 8.85 14.65 8.75 14.8 8.65 14.95 8.65 15.15 8.65 15.25 8.8 L 15.75 9.4 Q 15.45 9.75 15.15 10 14.8 10.2 14.45 10.35 14.05 10.5 13.7 10.55 13.3 10.6 12.95 10.6 12.25 10.6 11.65 10.35 11.1 10.1 10.65 9.65 10.2 9.15 9.95 8.45 9.7 7.75 9.7 6.9 9.7 6.1 9.95 5.4 10.15 4.75 10.6 4.25 11.05 3.75 11.7 3.5 12.35 3.2 13.2 3.2 14 3.2 14.6 3.45 15.2 3.7 15.7 4.2 M 5.85 4.7 Q 5.5 4.85 5.25 5.15 5.05 5.45 4.95 5.9 4.8 6.35 4.8 6.9 4.85 7.45 4.95 7.9 5.05 8.35 5.3 8.65 5.55 8.95 5.85 9.15 6.2 9.3 6.6 9.3 7 9.3 7.25 9.2 7.55 9.1 7.7 8.95 7.85 8.85 8 8.75 8.15 8.65 8.3 8.65 8.5 8.65 8.6 8.8 L 9.1 9.4 Q 8.8 9.75 8.5 10 8.15 10.2 7.8 10.35 7.4 10.5 7.05 10.55 6.65 10.6 6.3 10.6 5.6 10.6 5 10.35 4.45 10.1 4 9.65 3.55 9.15 3.3 8.45 3.05 7.75 3.05 6.9 3.05 6.1 3.3 5.4 3.5 4.75 3.95 4.25 4.4 3.75 5.05 3.5 5.7 3.2 6.55 3.2 7.35 3.2 7.95 3.45 8.55 3.7 9.05 4.2 L 8.6 4.85 Q 8.5 4.9 8.45 5 8.35 5.05 8.2 5.05 8.1 5.05 7.95 4.95 7.85 4.9 7.65 4.8 7.5 4.65 7.25 4.6 7 4.5 6.65 4.5 6.2 4.5 5.85 4.7 Z"/>\n          </svg>'},captionsOff:function(e){return'<svg class="cs-icon captions-icon" width="19px" height="16px" viewBox="0 0 19 16" focusable="false">\n            <g>\n              <path d="M 11.45 3.5 Q 10.8 3.75 10.35 4.25 9.9 4.75 9.7 5.4 9.45 6.1 9.45 6.9 9.45 7.75 9.7 8.45 9.95 9.15 10.4 9.65 10.85 10.1 11.4 10.35 12 10.6 12.7 10.6 13.05 10.6 13.45 10.55 13.8 10.5 14.2 10.35 14.55 10.2 14.9 10 15.2 9.75 15.5 9.4 L 15 8.8 Q 14.9 8.65 14.7 8.65 14.55 8.65 14.4 8.75 14.25 8.85 14.1 8.95 13.95 9.1 13.65 9.2 13.4 9.3 13 9.3 12.6 9.3 12.25 9.15 11.95 8.95 11.7 8.65 11.45 8.35 11.35 7.9 11.25 7.45 11.2 6.9 11.2 6.35 11.35 5.9 11.45 5.45 11.65 5.15 11.9 4.85 12.25 4.7 12.6 4.5 13.05 4.5 13.4 4.5 13.65 4.6 13.9 4.65 14.05 4.8 14.25 4.9 14.35 4.95 14.5 5.05 14.6 5.05 14.75 5.05 14.85 5 14.9 4.9 15 4.85 L 15.45 4.2 Q 14.95 3.7 14.35 3.45 13.75 3.2 12.95 3.2 12.1 3.2 11.45 3.5 M 5.6 4.7 Q 5.95 4.5 6.4 4.5 6.75 4.5 7 4.6 7.25 4.65 7.4 4.8 7.6 4.9 7.7 4.95 7.85 5.05 7.95 5.05 8.1 5.05 8.2 5 8.25 4.9 8.35 4.85 L 8.8 4.2 Q 8.3 3.7 7.7 3.45 7.1 3.2 6.3 3.2 5.45 3.2 4.8 3.5 4.15 3.75 3.7 4.25 3.25 4.75 3.05 5.4 2.8 6.1 2.8 6.9 2.8 7.75 3.05 8.45 3.3 9.15 3.75 9.65 4.2 10.1 4.75 10.35 5.35 10.6 6.05 10.6 6.4 10.6 6.8 10.55 7.15 10.5 7.55 10.35 7.9 10.2 8.25 10 8.55 9.75 8.85 9.4 L 8.35 8.8 Q 8.25 8.65 8.05 8.65 7.9 8.65 7.75 8.75 7.6 8.85 7.45 8.95 7.3 9.1 7 9.2 6.75 9.3 6.35 9.3 5.95 9.3 5.6 9.15 5.3 8.95 5.05 8.65 4.8 8.35 4.7 7.9 4.6 7.45 4.55 6.9 4.55 6.35 4.7 5.9 4.8 5.45 5 5.15 5.25 4.85 5.6 4.7 Z" />\n              <path class="icon-stroke-only" stroke-width="1.5" stroke-linejoin="round" stroke-linecap="round" fill="none" d="M 9.5 15.2 L 7.8 13.5 2 13.5 Q 1.35 13.5 0.95 13.05 0.5 12.65 0.5 12 L 0.5 2 Q 0.5 1.35 0.95 0.95 1.35 0.5 2 0.5 L 17 0.5 Q 17.65 0.5 18.05 0.95 18.5 1.35 18.5 2 L 18.5 12 Q 18.5 12.65 18.05 13.05 17.65 13.5 17 13.5 L 11.2 13.5 9.5 15.2 Z" />\n            </g>\n          </svg>'},captions:function(){return'\n    <svg class="cs-icon captions-icon" width="19px" height="16px" viewBox="0 0 19 16" focusable="false">\n      <g>\n        <path d="M 11.45 3.5 Q 10.8 3.75 10.35 4.25 9.9 4.75 9.7 5.4 9.45 6.1 9.45 6.9 9.45 7.75 9.7 8.45 9.95 9.15 10.4 9.65 10.85 10.1 11.4 10.35 12 10.6 12.7 10.6 13.05 10.6 13.45 10.55 13.8 10.5 14.2 10.35 14.55 10.2 14.9 10 15.2 9.75 15.5 9.4 L 15 8.8 Q 14.9 8.65 14.7 8.65 14.55 8.65 14.4 8.75 14.25 8.85 14.1 8.95 13.95 9.1 13.65 9.2 13.4 9.3 13 9.3 12.6 9.3 12.25 9.15 11.95 8.95 11.7 8.65 11.45 8.35 11.35 7.9 11.25 7.45 11.2 6.9 11.2 6.35 11.35 5.9 11.45 5.45 11.65 5.15 11.9 4.85 12.25 4.7 12.6 4.5 13.05 4.5 13.4 4.5 13.65 4.6 13.9 4.65 14.05 4.8 14.25 4.9 14.35 4.95 14.5 5.05 14.6 5.05 14.75 5.05 14.85 5 14.9 4.9 15 4.85 L 15.45 4.2 Q 14.95 3.7 14.35 3.45 13.75 3.2 12.95 3.2 12.1 3.2 11.45 3.5 M 5.6 4.7 Q 5.95 4.5 6.4 4.5 6.75 4.5 7 4.6 7.25 4.65 7.4 4.8 7.6 4.9 7.7 4.95 7.85 5.05 7.95 5.05 8.1 5.05 8.2 5 8.25 4.9 8.35 4.85 L 8.8 4.2 Q 8.3 3.7 7.7 3.45 7.1 3.2 6.3 3.2 5.45 3.2 4.8 3.5 4.15 3.75 3.7 4.25 3.25 4.75 3.05 5.4 2.8 6.1 2.8 6.9 2.8 7.75 3.05 8.45 3.3 9.15 3.75 9.65 4.2 10.1 4.75 10.35 5.35 10.6 6.05 10.6 6.4 10.6 6.8 10.55 7.15 10.5 7.55 10.35 7.9 10.2 8.25 10 8.55 9.75 8.85 9.4 L 8.35 8.8 Q 8.25 8.65 8.05 8.65 7.9 8.65 7.75 8.75 7.6 8.85 7.45 8.95 7.3 9.1 7 9.2 6.75 9.3 6.35 9.3 5.95 9.3 5.6 9.15 5.3 8.95 5.05 8.65 4.8 8.35 4.7 7.9 4.6 7.45 4.55 6.9 4.55 6.35 4.7 5.9 4.8 5.45 5 5.15 5.25 4.85 5.6 4.7 Z" />\n        <path class="icon-stroke-only" stroke-width="1.5" stroke-linejoin="round" stroke-linecap="round" fill="none" d="M 9.5 15.2 L 7.8 13.5 2 13.5 Q 1.35 13.5 0.95 13.05 0.5 12.65 0.5 12 L 0.5 2 Q 0.5 1.35 0.95 0.95 1.35 0.5 2 0.5 L 17 0.5 Q 17.65 0.5 18.05 0.95 18.5 1.35 18.5 2 L 18.5 12 Q 18.5 12.65 18.05 13.05 17.65 13.5 17 13.5 L 11.2 13.5 9.5 15.2 Z" />\n      </g>\n    </svg>'},carrot:function(e){return'\n    <svg style="left:calc('.concat(e,');" class="cs-icon cs-icon-carrot carrot"width="30" height="30" viewBox="0 0 30 30" focusable="false">\n      <g transform="translate(8, 8)">\n        <polygon style="fill:currentColor !important" points="1,1.5 5,5 1,8.5"/>\n      </g>\n  </svg>')},search:function(){return'\n    <svg class="search-icon" width="13px" height="15px" viewBox="0 0 13 15" focusable="false"\n      <g fill="none" fill-rule="evenodd">\n        <g stroke-width="2">\n          <circle cx="5.6" cy="5.6" r="4.6"/>\n          <path d="M8 9l4 5"/>\n        </g>\n      </g>\n    </svg>\n    '},searchClear:function(){return'\n    <svg class="cs-icon icon" width="11px" height="11px" viewBox="0 0 11 11">\n    <g id="Desktop-Color-Contrast" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">\n        <g id="Search" transform="translate(-290.000000, -69.000000)" fill="fill:currentColor !important" fill-rule="nonzero">\n            <g id="search" transform="translate(13.000000, 59.000000)">\n                <polygon id="ic_close" points="286.777666 10 282.500215 14.2779053 278.222334 10 277 11.2222382 281.277881 15.5002869 277 19.7779053 278.222334 21 282.500215 16.7222382 286.777666 21 288 19.7779053 283.722119 15.5002869 288 11.2222382"></polygon>\n            </g>\n        </g>\n    </g>\n    </svg>\n    '},filter:function(){return'<svg class="cs-icon icon-gear" width="14" height="14" viewBox="0 0 14 14" focusable="false">\n    <path id="icon-gear" transform="translate(0,3)" d="M11.1,9.8C11.1,9.8,11.1,9.8,11.1,9.8C11.1,9.8,11.1,9.7,11.1,9.8c0-0.1,0.1-0.1,0.1-0.1c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0-0.1c0,0,0,0,0-0.1c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0-0.1c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0-0.1,0.1-0.1c0,0,0,0,0,0c0-0.1,0.1-0.1,0.1-0.2c0,0,0,0,0,0c0-0.1,0-0.1,0.1-0.2c0,0,0,0,0,0c0.1-0.2,0.2-0.5,0.2-0.7l2-0.4V6.4l-2-0.4c0-0.3-0.1-0.5-0.2-0.7c0,0,0,0,0,0c0-0.1,0-0.1-0.1-0.2c0,0,0,0,0,0c0-0.1,0-0.1-0.1-0.2c0,0,0,0,0,0c0,0,0-0.1-0.1-0.1c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0-0.1c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0-0.1c0,0,0,0,0-0.1c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0-0.1c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0l1.2-1.7l-0.9-0.9L9.7,2.8c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0-0.1,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0-0.1,0c0,0,0,0-0.1,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0-0.1,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0-0.1,0-0.1-0.1c0,0,0,0,0,0c-0.1,0-0.1-0.1-0.2-0.1c0,0,0,0,0,0c-0.1,0-0.1,0-0.2-0.1c0,0,0,0,0,0C8.3,2.1,8.1,2.1,7.8,2L7.4,0H6.2L5.9,2c-0.3,0-0.5,0.1-0.7,0.2c0,0,0,0,0,0C5,2.3,5,2.3,4.9,2.3c0,0,0,0,0,0c-0.1,0-0.1,0-0.2,0.1c0,0,0,0,0,0c0,0-0.1,0-0.1,0.1c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0-0.1,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0-0.1,0c0,0,0,0-0.1,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0-0.1,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0L2.3,1.6L1.4,2.5l1.2,1.7c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0.1c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0.1c0,0,0,0,0,0.1c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0.1c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0.1-0.1,0.1c0,0,0,0,0,0C2.2,5,2.2,5,2.2,5.1c0,0,0,0,0,0c0,0.1,0,0.1-0.1,0.2c0,0,0,0,0,0C2,5.5,1.9,5.8,1.9,6l-2,0.4v1.2l2,0.4c0,0.3,0.1,0.5,0.2,0.7c0,0,0,0,0,0c0,0.1,0,0.1,0.1,0.2c0,0,0,0,0,0c0,0.1,0,0.1,0.1,0.2c0,0,0,0,0,0c0,0,0,0.1,0.1,0.1c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0.1c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0.1c0,0,0,0,0,0.1c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0.1c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0l-1.2,1.7l0.9,0.9L4,11.2c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0.1,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0.1,0c0,0,0,0,0.1,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0.1,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0.1,0,0.1,0.1c0,0,0,0,0,0c0.1,0,0.1,0.1,0.2,0.1c0,0,0,0,0,0c0.1,0,0.1,0,0.2,0.1c0,0,0,0,0,0c0.2,0.1,0.5,0.2,0.7,0.2l0.4,2h1.2l0.4-2c0.3,0,0.5-0.1,0.7-0.2c0,0,0,0,0,0c0.1,0,0.1,0,0.2-0.1c0,0,0,0,0,0c0.1,0,0.1,0,0.2-0.1c0,0,0,0,0,0c0,0,0.1,0,0.1-0.1c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0.1,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0.1,0c0,0,0,0,0.1,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0.1,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0l1.7,1.2l0.9-0.9L11.1,9.8C11,9.8,11,9.8,11.1,9.8C11,9.8,11.1,9.8,11.1,9.8z M6.8,9.2c-1.2,0-2.2-1-2.2-2.2c0-1.2,1-2.2,2.2-2.2C8,4.8,9,5.8,9,7C9,8.2,8,9.2,6.8,9.2z"/>\n  </svg>'},close:function(){return'\n    <svg class="cs-icon icon-close" width="20" height="20" viewBox="0 0 36 36" focusable="false">\n      <polygon points="36,2.826 33.174,0 18,15.174 2.826,0 0,2.826 15.174,18 0,33.174 2.826,36 18,20.826 33.174,36 36,33.174 20.826,18" />\n    </svg>'},clear:function(){return'<svg class="cs-icon icon-clear" width="13" height="14" viewBox="0 0 13 14" focusable="false">\n    <use xlink:href="#icon-clear" fill="rgba(240, 240, 240, 1)" transform="translate(0, 1)" />\n    <path id="icon-clear" transform="translate(3,3)" d="M6.5,0C2.9,0,0,2.9,0,6.5C0,10.1,2.9,13,6.5,13c3.6,0,6.5-2.9,6.5-6.5C13,2.9,10.1,0,6.5,0z M1.5,6.5c0-2.8,2.2-5,5-5c1.2,0,2.4,0.5,3.2,1.2L2.2,9.1C1.8,8.3,1.5,7.5,1.5,6.5z M6.5,11.5c-1.2,0-2.3-0.5-3.2-1.2L10.8,4c0.4,0.7,0.7,1.6,0.7,2.5C11.5,9.3,9.3,11.5,6.5,11.5z"/>\n  </svg>'},hamburger:function(){return'\n    <svg class="cs-icon" width="30px" height="12px" viewBox="0 10 30 12" focusable="false">\n      <path transform="translate(0, 1)" d="M0,15 L17,15 L17,17 L0,17 L0,15 Z M0,11 L17,11 L17,13 L0,13 L0,11 Z M0,19 L17,19 L17,21 L0,21 L0,19 Z" ></path>\n    </svg>\n  '},file:function(){return'\n    <svg width="20px" height="27px" viewBox="0 0 40 50" role="presentation" focusable="false">\n      <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">\n        <g>\n          <path class="file-icon-bg" d="M2.00804893,0 C0.899034128,0 0,0.889064278 0,1.99091407 L0,48.0090859 C0,49.1086374 0.892756032,50 1.99862555,50 L37.2170607,50 C38.3208711,50 39.2156863,49.1011186 39.2156863,47.993136 L39.2156863,13.6363636 L26.1437908,0 L2.00804893,0 Z"></path>\n          <path class="file-icon-fold" d="M26.1437908,0 L26.1437908,11.7296861 C26.1437908,12.8319383 27.0422752,13.7254902 28.1433598,13.7254902 L39.2156863,13.7254902"></path>\n        </g>\n      </g>\n    </svg>'},link:function(e){return'\n  <svg class="link-icon" preserveAspectRatio="none" x="0px" y="0px" width="18px" height="18px" viewBox="0 0 18 18" role="presentation" focusable="false">\n      <path fill="'.concat(e,'" stroke="none" d="\n            M 1.45 8.5\n            Q 0.0453125 10.0015625 0 11.9\n            L 0 12.15\n            Q 0.0453125 14.0484375 1.45 15.55\n            L 1.5 15.6\n            Q 3.0015625 17.0046875 4.85 17.05\n            L 5.1 17.05\n            Q 7.0150390625 17.0046875 8.5 15.6\n            L 10.65 13.45\n            Q 10.95 13.15 10.95 12.75 10.95 12.35 10.65 12.05 10.3689453125 11.7689453125 10 11.75\n            L 9.95 11.75\n            Q 9.55 11.75 9.2 12.05\n            L 7.1 14.15\n            Q 6.2 15.05 5 15.05 3.8 15.05 2.9 14.15 2 13.25 2 12.05 2 10.8826171875 2.85 9.95\n            L 5 7.8\n            Q 5.3 7.5 5.3 7.1\n            L 5.3 7.05\n            Q 5.2810546875 6.6810546875 5 6.4 4.7 6.1 4.3 6.1 3.9 6.1 3.55 6.4\n            L 1.45 8.5\n            M 12.05 5\n            Q 11.75 4.7 11.35 4.7 10.95 4.7 10.65 5\n            L 5 10.65\n            Q 4.7 10.95 4.7 11.35 4.7 11.75 5 12.05 5.3 12.35 5.7 12.35 6.1 12.35 6.4 12.05\n            L 12.05 6.4\n            Q 12.35 6.1 12.35 5.7 12.35 5.3 12.05 5\n            M 15.6 1.5\n            L 15.55 1.45\n            Q 14 0 12.05 0\n            L 12 0\n            Q 10.05 0 8.5 1.45\n            L 6.4 3.55\n            Q 6.1 3.9 6.1 4.3 6.1 4.7 6.4 5 6.7 5.3 7.1 5.3 7.5 5.3 7.8 5\n            L 9.95 2.85\n            Q 10.8826171875 2 12.05 2 13.25 2 14.15 2.9 15.05 3.8 15.05 5 15.05 6.2 14.15 7.1\n            L 12.05 9.2\n            Q 11.75 9.55 11.75 9.95 11.75 10.35 12.05 10.65 12.35 10.95 12.75 10.95 13.15 10.95 13.45 10.65\n            L 15.6 8.5\n            Q 17.05 6.96875 17.05 5 17.05 3.05 15.6 1.5 Z"/>\n          </svg>')},settings:function(){return'\n    <svg class="cs-icon" data-ref="settings" width="16px" height="16px" viewBox="0 0 16 16" focusable="false">\n      <path d="M8.94,0 C9.82,0 10.55,0.62 10.63,1.45 L10.73,2.36 C11.1,2.52 11.45,2.71 11.78,2.94 L12.66,2.56 C13.46,2.22 14.39,2.5 14.83,3.23 L15.77,4.77 C16.21,5.5 16,6.4 15.29,6.9 L14.51,7.42 C14.54,8.19 14.53,8.38 14.51,8.58 L15.29,9.11 C16,9.6 16.21,10.51 15.77,11.23 L14.83,12.77 C14.39,13.49 13.46,13.78 12.66,13.44 L11.78,13.06 C11.45,13.29 11.1,13.48 10.73,13.64 L10.63,14.55 C10.55,15.38 9.82,16 8.94,16 L7.06,16 C6.18,16 5.45,15.38 5.37,14.55 L5.27,13.64 C4.9,13.48 4.55,13.29 4.22,13.06 L3.34,13.44 C2.54,13.78 1.61,13.5 1.17,12.77 L0.23,11.23 C-0.21,10.51 0,9.6 0.71,9.11 L1.49,8.58 C1.46,7.81 1.47,7.62 1.49,7.42 L0.71,6.89 C0,6.40 -0.21,5.49 0.23,4.77 L1.17,3.23 C1.61,2.51 2.54,2.22 3.34,2.56 L4.22,2.94 C4.55,2.71 4.9,2.52 5.27,2.36 L5.37,1.45 C5.45,0.62 6.18,0 7.06,0 Z M7.96,4.53 C5.91,4.53 4.25,6.11 4.25,8.06 C4.25,10.01 5.91,11.59 7.96,11.59 C10.02,11.59 11.68,10.01 11.68,8.06 C11.68,6.11 10.02,4.53 7.96,4.53 Z"></path>\n    </svg>\n    '},playbackSpeed:function(){return'\n    <svg class="cs-icon" width="15" height="15" viewBox="0 0 15 15" focusable="false">\n      <path d="M5.9 4.0L10.4 7.4L5.9 10.8V4.0ZM1.5 8.2H0.0C0.1 9.6 0.6 10.9 1.5 12.0L2.6 11.0C2.0 10.1 1.6 9.2 1.5 8.2H1.5ZM15 7.4H14.9C14.9 5.6 14.3 3.8 13.0 2.4C11.8 1.0 10.0 0.1 8.2 0.0V1.5C10.1 1.7 11.8 2.9 12.8 4.7C13.7 6.4 13.7 8.5 12.8 10.2C11.8 12.0 10.1 13.1 8.2 13.4V14.9C10.0 14.7 11.8 13.8 13.0 12.5C14.3 11.1 14.9 9.3 14.9 7.4L15 7.4ZM3.6 12.1L2.5 13.1C3.7 14.1 5.1 14.8 6.7 14.9V13.4V13.4C5.5 13.3 4.5 12.8 3.6 12.1V12.1ZM2.6 3.9L1.5 2.8C0.6 3.9 0.1 5.3 0 6.7H1.5H1.5C1.6 5.7 2.0 4.7 2.6 3.9H2.6ZM6.7 1.5V0.0C5.1 0.1 3.7 0.7 2.5 1.7L3.6 2.8C4.5 2.1 5.5 1.6 6.7 1.5V1.5Z" stroke="none" />\n    </svg>\n  '},track:function(e,t){return'\n    <svg xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="16px" viewBox="0 0 24 16" focusable="false">\n      <defs>\n        <rect id="'.concat(t,'-track" x="2" y="3.5" width="20" height="9" rx="4.5"></rect>\n        <filter x="-12.5%" y="-27.8%" width="125.0%" height="155.6%" filterUnits="objectBoundingBox" id="').concat(t,'-trackFilter">\n          <feGaussianBlur stdDeviation="1.5" in="SourceAlpha" result="shadowBlurInner1"></feGaussianBlur>\n          <feOffset dx="0" dy="1" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>\n          <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>\n          <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.5 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>\n        </filter>\n      </defs>\n      <g class="thumb-off" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">\n        <g>\n          <use class="track" fill-rule="evenodd" xlink:href="#').concat(t,'-track"></use>\n          <use fill="black" fill-opacity="1" filter="url(#').concat(t,'-trackFilter)" xlink:href="#').concat(t,'-track"></use>\n          <use class="border" stroke-width="1" xlink:href="#').concat(t,'-track"></use>\n          <circle class="thumb" stroke-width="0" cx="8" cy="8" r="6"></circle>\n        </g>\n      </g>\n      <g class="thumb-on" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">\n        <g>\n          <use class="track" fill-rule="evenodd" xlink:href="#').concat(t,'-track"></use>\n          <use fill="black" fill-opacity="1" filter="url(#').concat(t,'-trackFilter)" xlink:href="#').concat(t,'-track"></use>\n          <use class="border" stroke-width="1" xlink:href="#').concat(t,'-track"></use>\n          <circle fill="').concat(e,'" stroke-width="0" cx="16" cy="8" r="6"></circle>\n        </g>\n      </g>\n    </svg>\n  ')},downArrow:function(e,t){return'\n    <div style="height: 100%; width: 100%; background-color: '.concat(e,"; border-right: 1px solid; border-bottom: 1px solid; border-color: ").concat(t,'; border-bottom-right-radius: 3px; transform: rotate(45deg);" />\n    ')},checkmark:function(){return'<svg  class="cs-icon check-icon" focusable="false" width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">\n      <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">\n          <polygon style="fill:currentColor !important" points="12.04 4 13.45 5.41 6.25 12.62 2 8.36 3.41 6.95 6.25 9.79"></polygon>\n      </g>\n    </svg>'},lock:function(){return'<svg width="16px" height="12px" viewBox="0 0 9 12" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">\n    <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">\n        <g fill="#FFFFFF" fill-rule="nonzero">\n            <path style="fill:currentColor !important" d="M7.875,4 L7.3125,4 L7.3125,2.85714286 C7.3125,1.28 6.0525,0 4.5,0 C2.9475,0 1.6875,1.28 1.6875,2.85714286 L1.6875,4 L1.125,4 C0.50625,4 0,4.51428571 0,5.14285714 L0,10.8571429 C0,11.4857143 0.50625,12 1.125,12 L7.875,12 C8.49375,12 9,11.4857143 9,10.8571429 L9,5.14285714 C9,4.51428571 8.49375,4 7.875,4 Z M6.24375,4 L2.75625,4 L2.75625,2.85714286 C2.75625,1.88 3.538125,1.08571429 4.5,1.08571429 C5.461875,1.08571429 6.24375,1.88 6.24375,2.85714286 L6.24375,4 Z"></path>\n        </g>\n    </g>\n</svg>'},lockedViewed:function(){return'<svg width="16px" height="12px" viewBox="0 0 9 12" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">\n    <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">\n        <g fill="#FFFFFF" fill-rule="nonzero">\n            <path style="fill:currentColor !important" d="M7.875,4 L7.3125,4 L7.3125,2.85714286 C7.3125,1.28 6.0525,0 4.5,0 C2.9475,0 1.6875,1.28 1.6875,2.85714286 L1.6875,4 L1.125,4 C0.50625,4 0,4.51428571 0,5.14285714 L0,10.8571429 C0,11.4857143 0.50625,12 1.125,12 L7.875,12 C8.49375,12 9,11.4857143 9,10.8571429 L9,5.14285714 C9,4.51428571 8.49375,4 7.875,4 Z M7.11248548,6.17405922 C7.38175483,6.47268397 7.38175483,6.92847965 7.0966461,7.21138731 L4.53066757,9.75755627 C4.38811321,9.8990101 4.19804072,9.96187847 4.02380761,9.96187847 C3.83373513,9.96187847 3.65950202,9.8990101 3.51694766,9.75755627 L2.34544071,8.45018243 C2.06033199,8.16727477 2.06033199,7.71147909 2.34544071,7.42857143 C2.63054944,7.14566377 3.08989127,7.14566377 3.375,7.42857143 L4.02380761,8.21728122 L6.08292619,6.17405922 C6.36803491,5.89115155 6.82737675,5.89115155 7.11248548,6.17405922 Z M4.5,1.08571429 C5.461875,1.08571429 6.24375,1.88 6.24375,2.85714286 L6.24375,4 L2.75625,4 L2.75625,2.85714286 C2.75625,1.88 3.538125,1.08571429 4.5,1.08571429 Z"></path>\n        </g>\n    </g>\n</svg>'},languagePicker:function(){return'\n<svg class="cs-icon" viewBox="0 0 48 48" width="16" height="16" xmlns="http://www.w3.org/2000/svg">\n  <path d="M23.99 4c-11.05 0-19.99 8.95-19.99 20s8.94 20 19.99 20c11.05 0 20.01-8.95 20.01-20s-8.96-20-20.01-20zm13.85 12h-5.9c-.65-2.5-1.56-4.9-2.76-7.12 3.68 1.26 6.74 3.81 8.66 7.12zm-13.84-7.93c1.67 2.4 2.97 5.07 3.82 7.93h-7.64c.85-2.86 2.15-5.53 3.82-7.93zm-15.48 19.93c-.33-1.28-.52-2.62-.52-4s.19-2.72.52-4h6.75c-.16 1.31-.27 2.64-.27 4 0 1.36.11 2.69.28 4h-6.76zm1.63 4h5.9c.65 2.5 1.56 4.9 2.76 7.13-3.68-1.26-6.74-3.82-8.66-7.13zm5.9-16h-5.9c1.92-3.31 4.98-5.87 8.66-7.13-1.2 2.23-2.11 4.63-2.76 7.13zm7.95 23.93c-1.66-2.4-2.96-5.07-3.82-7.93h7.64c-.86 2.86-2.16 5.53-3.82 7.93zm4.68-11.93h-9.36c-.19-1.31-.32-2.64-.32-4 0-1.36.13-2.69.32-4h9.36c.19 1.31.32 2.64.32 4 0 1.36-.13 2.69-.32 4zm.51 11.12c1.2-2.23 2.11-4.62 2.76-7.12h5.9c-1.93 3.31-4.99 5.86-8.66 7.12zm3.53-11.12c.16-1.31.28-2.64.28-4 0-1.36-.11-2.69-.28-4h6.75c.33 1.28.53 2.62.53 4s-.19 2.72-.53 4h-6.75z"/>\n</svg>\n  '}},Zn={play:function(){return'<svg id="icon-play" class="cs-icon play-icon" width="14" height="16" viewBox="0 0 14 16" focusable="false">\n    <path d="M1.4 15.4C0.8 15.8 0 15.3 0 14.5L0 1.4C0 0.6 0.8 0.1 1.4 0.5L12.9 7.1C13.5 7.5 13.5 8.4 12.9 8.8L8.0 11.6L1.4 15.4Z" stroke="none" />\n  </svg>'}},Qn=function(e){return DS.detection.env.isPerpetual?Kn[e]||zn:Zn[e]||Kn[e]||zn},Gn=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;return window.innerWidth-e<900};function qn(e){return qn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},qn(e)}function Yn(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,(r=i.key,o=void 0,o=function(e,t){if("object"!==qn(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var i=n.call(e,t||"default");if("object"!==qn(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(r,"string"),"symbol"===qn(o)?o:String(o)),i)}var r,o}function Xn(e,t){return Xn=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},Xn(e,t)}function $n(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,i=ei(e);if(t){var r=ei(this).constructor;n=Reflect.construct(i,arguments,r)}else n=i.apply(this,arguments);return function(e,t){if(t&&("object"===qn(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return Jn(e)}(this,n)}}function Jn(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function ei(e){return ei=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},ei(e)}var ti=DS,ni=ti.pubSub,ii=ti.events,ri=ti.constants,oi=ti.focusManager,ai=ti.globalEventHelper.addWindowListener,li=ti.dom,si=li.addClass,ci=li.removeClass,ui=!1,fi=!0,di="floating-sidebar",hi=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Xn(e,t)}(o,e);var t,n,i,r=$n(o);function o(e){var t;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,o),t=r.call(this,e),_.bindAll(Jn(t),"onToggle","onResize","onOver","onOut"),t.nameSpace=Q.getNamespace(t.view.nameSpace),t.sidebar=t.nameSpace.sidebar,t.removeCollapseSidebarListener=null,t.toggle=!1,t.onClick(t.onToggle),(Q.model.sidebarOpts.closed&&fi||ui)&&(si(document.body,"sidebar-closed"),t.sidebar.collapsed=!0),t.onClickEl(t.nameSpace.sidebarOverlay.el,t.onToggle),ni.on(ii.sidebar.CLOSE,(function(){t.sidebar.collapsed||t.onToggle()})),ni.on(ii.sidebar.OPEN,(function(){t.sidebar.collapsed&&t.onToggle()})),ni.on(ii.frame.RESIZE,t.onResize),ai("resize",t.onResize),t.onResize(null,!0),t.wrapperEl.addEventListener("mouseenter",t.onOver),t.wrapperEl.addEventListener("mouseleave",t.onOut),fi=!1,t}return t=o,n=[{key:"keyUpCollapseSidebar",value:function(e){e.keyCode!==ri.keys.ESCAPE||this.sidebar.collapsed||("hamburger"===oi.lastFocusedElement().id?this.onToggle():this.onToggle(!1))}},{key:"getViewBox",value:function(){var e=this.el.getBoundingClientRect();return{x:e.left+10,y:e.top+10,w:e.width-5,h:e.height}}},{key:"onOver",value:function(e){si(this.wrapperEl,"menu-icon-wrapper-hover")}},{key:"onOut",value:function(e){ci(this.wrapperEl,"menu-icon-wrapper-hover")}},{key:"setExpandedAttr",value:function(){return this.el.setAttribute("aria-expanded",!this.sidebar.collapsed),this}},{key:"animateSidebar",value:function(){this.animate||(this.sidebar.animate=!0,this.sidebar.update())}},{key:"onResize",value:function(e,t){var n=Q.model.dockSettings,i=n.dockedState,r=n.width,o=i!==ri.docked.NONE?r:0;Gn(o)&&this.sidebar.visible?(this.sidebar.startFloat(),si(document.body,di),null==this.removeCollapseSidebarListener&&(this.removeCollapseSidebarListener=ai("keyup",this.keyUpCollapseSidebar.bind(this)))):(this.sidebar.endFloat(),ci(document.body,di),null!=this.removeCollapseSidebarListener&&(this.removeCollapseSidebarListener(),this.removeCollapseSidebarListener=null));var a=window.innerWidth-o;!this.sidebar.collapsed&&Gn(o)&&(this.prevWidth>a||t)?(this.sidebar.autoCollapsed=!0,this.sidebar.collapsed=!0,this.setExpandedAttr().animateSidebar()):this.sidebar.autoCollapsed&&!this.sidebar.floating&&this.prevWidth<a&&(this.sidebar.collapsed=!1,this.sidebar.autoCollapsed=!1,this.setExpandedAttr().animateSidebar()),this.prevWidth=a}},{key:"onToggle",value:function(){var e=this,t=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];this.sidebar.collapsed=!this.sidebar.collapsed,this.sidebar.collapsed||(this.sidebar.autoCollapsed=!1),t?(this.onBlur(),this.setExpandedAttr().animateSidebar(),setTimeout((function(){e.sidebar.collapsed?e.onFocus():e.sidebar.el.querySelector('[tabIndex="0"]').focus()}),250)):this.setExpandedAttr().animateSidebar(),ni.trigger(ii.hamburger.TOGGLE,this.sidebar.collapsed)}},{key:"teardown",value:function(){ui=this.sidebar.collapsed,ni.off(ii.sidebar.CLOSE),ni.off(ii.sidebar.OPEN)}}],n&&Yn(t.prototype,n),i&&Yn(t,i),Object.defineProperty(t,"prototype",{writable:!1}),o}(_t),pi=hi,yi="hamburger";Q.def(yi,pi,(function(e){var t=Q.model,n=Q.getNamespace(e).sidebar;return{tag:"button",ariaStringId:"sidebar_toggle",attrs:{id:yi,"aria-expanded":!t.sidebarOpts.closed,"aria-controls":"sidebar","aria-label":"".concat(t.getString("sidebar_toggle")),tabIndex:0},x:function(){return"left"===n.pos?n.data.actualWidth():-65},w:50,h:65,outsideContent:!0,html:'\n      <div class="menu-icon-wrapper" data-ref="wrapper">\n        '.concat(Qn("hamburger")(),"\n      </div>\n    ")}}));var bi=10,vi=20,gi=1e3,mi=DS.detection.deviceView,wi="topBar";function Si(e){return Si="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Si(e)}function ki(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var i,r,o,a,l=[],s=!0,c=!1;try{if(o=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;s=!1}else for(;!(s=(i=o.call(n)).done)&&(l.push(i.value),l.length!==t);s=!0);}catch(e){c=!0,r=e}finally{try{if(!s&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(c)throw r}}return l}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return Ci(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Ci(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Ci(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,i=new Array(t);n<t;n++)i[n]=e[n];return i}function Oi(e,t,n){return(t=Li(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Ei(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,Li(i.key),i)}}function Li(e){var t=function(e,t){if("object"!==Si(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var i=n.call(e,t||"default");if("object"!==Si(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===Si(t)?t:String(t)}function xi(e,t){return xi=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},xi(e,t)}function Pi(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,i=ji(e);if(t){var r=ji(this).constructor;n=Reflect.construct(i,arguments,r)}else n=i.apply(this,arguments);return function(e,t){if(t&&("object"===Si(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return Ti(e)}(this,n)}}function Ti(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function ji(e){return ji=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},ji(e)}Q.def(wi,(function(e){var t=Q.getNamespace(e),n=(t.sidebar,t.title,t.frame),i=t.slide,r=Q.model;return{tag:"section",noTabIndex:!0,attrs:{id:wi,"aria-label":"top bar"},overflow:"visible",x:function(){var e=r.dockSettings,t=e.dockedState,n=e.width,o="left"===t?n:0;return i.sidebarXOffset+o},y:0,w:function(){var e=r.dockSettings,t=e.dockedState,o=e.width,a="none"!==t?o:0;return n.w-(i.sidebarWidthOffset+a)},h:function(){return mi.isMobile||i.topBarExists()?65:0}}}));var _i=DS,Di=_i.detection,Ii=_i.events,Ai=_i.pubSub,Ri=_i.svgUtils,Bi=_i._.bindAll,Mi={remaining:function(e,t){return DS.utils.formatSecondsAsTime(t-e,!0)},totalelapsed:function(e,t){return[e,t].map((function(e){return DS.utils.formatSecondsAsTime(e,!0)})).join(" / ")},elapsed:function(e){return DS.utils.formatSecondsAsTime(e,!0)},none:function(){return""}},Hi=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&xi(e,t)}(o,e);var t,n,i,r=Pi(o);function o(e){var t,n;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,o),n=r.call(this,e),Bi(Ti(n),"onShow","onHide","onTick"),Ai.on((Oi(t={},Ii.timer.SHOW,n.onShow),Oi(t,Ii.timer.HIDE,n.onHide),t)),n.timeFormat=n.getTimeFormat(),n.isPieProgress=Di.theme.isUnified,n}return t=o,(n=[{key:"getTimeFormat",value:function(){if(!Di.deviceView.isClassicMobile){var e=Q.model.sidebarOpts.timeFormat;if(e&&null!=Mi[e.toLowerCase()])return e.toLowerCase();if(!Q.model.sidebarOpts.timeEnabled)return"none"}return"remaining"}},{key:"teardown",value:function(){var e;Ai.off((Oi(e={},Ii.timer.SHOW,this.onShow),Oi(e,Ii.timer.HIDE,this.onHide),e))}},{key:"onTick",value:function(e,t,n){if(this.view.children.timerText.el.innerHTML=Mi[this.timeFormat](t,n),!Di.deviceView.isClassicMobile){var i=Ri.wheelPath(9,9,9,0,360*(1-e),this.isPieProgress);this.view.children.timerPath.el.setAttributeNS(null,"d",i)}}},{key:"onShow",value:function(e){null!=this.currentTimer&&this.onHide(),this.currentTimer=e,this.currentTimer.on("tick",this.onTick),this.toggleVisibility(!0),window.requestAnimationFrame(DS.pubSub.trigger.bind(DS.pubSub,DS.events.timer.SHOWN))}},{key:"onHide",value:function(){null!=this.currentTimer&&(this.currentTimer.off("tick",this.onTick),this.currentTimer=null,window.requestAnimationFrame(DS.pubSub.trigger.bind(DS.pubSub,DS.events.timer.HIDDEN))),this.toggleVisibility(!1)}},{key:"toggleVisibility",value:function(e){var t=ki(e?["add","remove"]:["remove","add"],2),n=t[0],i=t[1];document.body.classList[n]("timer-shown"),this.el.classList[i]("hidden"),this.el.classList[n]("shown"),this.view.setVisibility(e),this.view.parent.updateChildren(!0)}},{key:"onFocus",value:function(){var e=this.view.children.timerText.el.getBoundingClientRect(),t=e.left,n=e.top,i=e.width,r=e.height;t-=8,n-=8,i+=37,r+=12,DS.focusManager.setFocusRectOn(this.el,{left:t,top:n,width:i,height:r})}}])&&Ei(t.prototype,n),i&&Ei(t,i),Object.defineProperty(t,"prototype",{writable:!1}),o}(_t),Ni=DS.detection,Fi="timer";Q.def(Fi,Hi,(function(e){var t=Q.getNamespace(e),n=t.topBar,i=t.title,r=t.sidebar,o=t.hamburger,a=t.topEllipsis;return{noTabIndex:!0,attrs:{id:Fi,"aria-label":"timer",class:"timer cs-timer"},overflow:"visible",visible:!1,methods:{hasUpperRightElement:function(){var e="right"===r.pos&&o.visible,t="left"===r.pos&&a.visible;return e||t}},x:function(){var e=Q.model.rtl?n.w-240-(i.visible?i.w:0):n.w-230;return"right"===r.pos&&o.visible&&(e-=o.w),Ni.deviceView.isPhone&&(e+=4,Ni.orientation.isLandscape?e=window.innerWidth-this.w-5:this.hasUpperRightElement()?e-=58:e-=bi),e},y:function(){var e=vi;return Ni.deviceView.isPhone&&Ni.orientation.isLandscape&&this.hasUpperRightElement()&&(e+=vi),e},w:220,h:22,html:function(){return'\n        <div class="timer-wheel cs-pie">\n          '.concat('\n          <svg\n            style="width: 18px; height: 18px; overflow: visible;"\n            width="18"\n            height="18"\n            xmlns:xlink="http://www.w3.org/1999/xlink"\n            focusable="false"\n            >\n\n            <circle class="circle-progress-pie"\n              cx="9" cy="9" r="'.concat(8,'"\n              stroke-width="').concat(2,'"\n              fill="transparent" />\n\n            <path data-ref="timerPath"\n              d=""\n              class="cs-brandhighlight-fill"\n              transform="rotate(-90 9 9)"\n              stroke="none" />\n\n          </svg>\n          '),"\n        </div>\n        <div class=\"timer-text\" data-ref='timerText' tabindex='0' data-tabindex='0'></div>\n        ")}}}));var Vi=function(e,t){return'\n    <div class="panel-arrow" data-ref="arrow" style="height: 17px; width: 17px; position: absolute;">\n      <div class="panel-arrow-path"\n        style="\n          height: 100%;\n          width: 100%;\n          background-color: '.concat(e,";\n          border-left: 1px solid;\n          border-top: 1px solid;\n          border-color: ").concat(t,';\n          border-top-left-radius: 3px;\n          transform: rotate(45deg);\n          "\n        />\n    </div>')};function Wi(e){return Wi="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Wi(e)}function Ui(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,qi(i.key),i)}}function zi(e,t){return zi=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},zi(e,t)}function Ki(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,i=Qi(e);if(t){var r=Qi(this).constructor;n=Reflect.construct(i,arguments,r)}else n=i.apply(this,arguments);return function(e,t){if(t&&("object"===Wi(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return Zi(e)}(this,n)}}function Zi(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Qi(e){return Qi=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},Qi(e)}function Gi(e,t,n){return(t=qi(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function qi(e){var t=function(e,t){if("object"!==Wi(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var i=n.call(e,t||"default");if("object"!==Wi(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===Wi(t)?t:String(t)}var Yi=DS,Xi=(Yi.detection,Yi.events),$i=Yi.pubSub,Ji=Yi.utils.pxify,er=Object.freeze({unknown:0,tabLinkPanel:1,topEllipsesPanel:2}),tr=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&zi(e,t)}(o,e);var t,n,i,r=Ki(o);function o(e){var t;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,o),Gi(Zi(t=r.call(this,e)),"onTabLinkPanelShown",(function(e){var n=e.view.panel.children.arrow;t.showArrowShadow(n),t.lastWidget=er.tabLinkPanel})),Gi(Zi(t),"onTabLinkHidePanel",(function(){t.lastWidget===er.tabLinkPanel&&t.hideArrowShadow()})),Gi(Zi(t),"onTopEllipsesPanelShown",(function(e){e.view.children.topEllipsisPanel;var n=e.view.children.arrow;t.showArrowShadow(n),t.lastWidget=er.topEllipsesPanel})),Gi(Zi(t),"onTopEllipsesHidePanel",(function(){t.lastWidget===er.topEllipsesPanel&&t.hideArrowShadow()})),Gi(Zi(t),"showArrowShadow",(function(e){var n=e.el;if(t.isVisible(n)){var i=DS.views.getTopNameSpace().sidebar.el.getBoundingClientRect(),r=n.getBoundingClientRect(),o=i.x>=0,a=i.x<r.x&&o?i.width:0,l=r.x-a,s=r.y+t.view.h/2+1;t.el.style.transform="translate(".concat(Ji(l),", ").concat(Ji(s),")"),t.el.style.display="block",t.el.getBoundingClientRect().x!==r.x&&(t.el.style.display="none")}})),Gi(Zi(t),"hideArrowShadow",(function(){t.el.style.display="none"})),Gi(Zi(t),"isVisible",(function(e){return"block"===window.getComputedStyle(e).display})),Gi(Zi(t),"applyPanelShadowForIE11",(function(e){e.el.style.boxShadow="0 0 40px rgba(0, 0, 0, 0.20)"})),t.lastWidget=er.unknown,$i.on(Xi.tabLink.PANEL_SHOWN,t.onTabLinkPanelShown),$i.on(Xi.tabLink.HIDE_PANEL,t.onTabLinkHidePanel),$i.on(Xi.topEllipsesPanel.PANEL_SHOWN,t.onTopEllipsesPanelShown),$i.on(Xi.topEllipsesPanel.HIDE_PANEL,t.onTopEllipsesHidePanel),t}return t=o,n&&Ui(t.prototype,n),i&&Ui(t,i),Object.defineProperty(t,"prototype",{writable:!1}),t}(_t),nr="arrowShadow";function ir(e){return ir="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},ir(e)}function rr(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var i,r,o,a,l=[],s=!0,c=!1;try{if(o=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;s=!1}else for(;!(s=(i=o.call(n)).done)&&(l.push(i.value),l.length!==t);s=!0);}catch(e){c=!0,r=e}finally{try{if(!s&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(c)throw r}}return l}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return or(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return or(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function or(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,i=new Array(t);n<t;n++)i[n]=e[n];return i}function ar(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,(r=i.key,o=void 0,o=function(e,t){if("object"!==ir(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var i=n.call(e,t||"default");if("object"!==ir(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(r,"string"),"symbol"===ir(o)?o:String(o)),i)}var r,o}function lr(){return lr="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(e,t,n){var i=function(e,t){for(;!Object.prototype.hasOwnProperty.call(e,t)&&null!==(e=fr(e)););return e}(e,t);if(i){var r=Object.getOwnPropertyDescriptor(i,t);return r.get?r.get.call(arguments.length<3?e:n):r.value}},lr.apply(this,arguments)}function sr(e,t){return sr=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},sr(e,t)}function cr(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,i=fr(e);if(t){var r=fr(this).constructor;n=Reflect.construct(i,arguments,r)}else n=i.apply(this,arguments);return function(e,t){if(t&&("object"===ir(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return ur(e)}(this,n)}}function ur(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function fr(e){return fr=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},fr(e)}Q.def(nr,tr,(function(e){var t=de.getColor(e,".cs-topmenu-item.active .cs-panel","background-color");return{attrs:{id:nr},w:17,overflow:"visible",h:17,html:Vi(t,t),style:{display:"none"}}}));var dr=DS,hr=dr._,pr=dr.keyManager,yr=dr.focusManager,br=dr.windowManager,vr=dr.pubSub,gr=dr.events,mr=dr.detection,wr=dr.globalEventHelper,Sr=wr.addBodyListener,kr=wr.removeBodyListener,Cr=wr.addWindowListener,Or=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&sr(e,t)}(o,e);var t,n,i,r=cr(o);function o(e){var t;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,o),t=r.call(this,e),hr.bindAll(ur(t),"onKeydown","onResizeRefresh"),t.active=[],vr.on(gr.sidebar.ACTIVE_TAB_SET,t.onActiveTabSet.bind(ur(t))),DS.flagManager.aiCourseTranslation&&vr.on(gr.frame.SHOW_TAB_ITEM,t.onShowTabItem.bind(ur(t))),mr.theme.isUnified&&(t.removeResize=Cr("resize",t.onResizeRefresh)),t}return t=o,(n=[{key:"onResizeRefresh",value:function(){var e=this;clearTimeout(this.forceRefresh),this.forceRefresh=setTimeout((function(){e.view.update()}),200)}},{key:"onShowTabItem",value:function(e,t){var n=this.view.children.find((function(t){return t.nameKey.includes(e)}));null!=n?(n.viewLogic.showPanel(),vr.trigger(gr.sidebar.OPEN)):document.body.classList.contains("floating-sidebar")&&vr.trigger(gr.sidebar.CLOSE)}},{key:"onActiveTabSet",value:function(e){var t=this.view.children.find((function(t){return t.nameKey.includes(e)}));null!=t&&t.viewLogic.showPanel()}},{key:"onLayoutChange",value:function(e){var t=this;if(null!=Q.model&&0!==this.view.children.length){var n=(Q.model.sidebarTabs||[]).reduce((function(n,i){var r=i.name,o=e[r],a="".concat(r,"Tab");return(hr.isObject(o)?!0===o.enabled:!0===o)&&null!=t.view.children[a]&&n.push(t.view.children[a]),n}),[]);if(this.view.children.forEach((function(e){e.el.setAttribute("aria-selected",!1),e.el.tabIndex=-1})),0===this.active.length&&n.length>0||!n.includes(this.currentTab)){var i=rr(n,1)[0];this.currentTab=i}if(null!=this.currentTab){this.currentTab.el.setAttribute("aria-selected",!0);var r=br.getCurrentWindow();null!=r&&null!=r.getId&&"_frame"!==r.getId()||(this.currentTab.el.tabIndex=0)}this.active=n}}},{key:"onFocus",value:function(e){this.isFocused||(this.isFocused=!0,this.getInitialActive().setCurrent(),Sr("keydown",this.onKeydown))}},{key:"onBlur",value:function(e){this.el.contains(e.relatedTarget)||(this.isFocused=!1,lr(fr(o.prototype),"onBlur",this).call(this),kr("keydown",this.onKeydown))}},{key:"onKeydown",value:function(e){var t=this.currentTab;pr.isActionKey(e.which)?(this.activeTab=this.currentTab,this.activateTab()):pr.isRightKey(e.which)?this.currentTab=this.getNextTab(this.currentTab.model.idx):pr.isLeftKey(e.which)?this.currentTab=this.getPrevTab(this.currentTab.model.idx):pr.isHomeKey(e.which)?this.currentTab=this.getFirstTab():pr.isEndKey(e.which)&&(this.currentTab=this.getLastTab()),t!==this.currentTab&&(null!=t&&(t.el.tabIndex=-1),this.setCurrent())}},{key:"activateTab",value:function(){null!=this.activeTab&&(hr.forEach(this.view.children,(function(e){return e.el.setAttribute("aria-selected",!1)})),this.activeTab.el.setAttribute("aria-selected",!0),this.activeTab.viewLogic.showPanel())}},{key:"setAsActive",value:function(e){this.activeTab=this.currentTab=e}},{key:"tabDidChangeVisibility",value:function(){this.view.visible&&(null!=this.activeTab&&this.activeTab.visible||(this.currentTab=this.activeTab=hr.first(this.getLiveChildren())),this.activateTab())}},{key:"setCurrent",value:function(){var e=this.currentTab;if(null!=e){e.el.tabIndex=0,e.el.focus();var t=e.getBox(),n=t.x,i=t.y,r=t.w,o=t.h;yr.setFocusRectOn(e.el,{left:n,top:i,width:r,height:o})}}},{key:"getInitialActive",value:function(){return this.currentTab=this.activeTab=this.getLiveChildren().find((function(e){return e.el===document.activeElement})),this}},{key:"getNextTab",value:function(e){var t=this.getLiveChildren(),n=t.findIndex((function(t){return t.model.idx===e}))+1;return n===t.length&&(n=0),t[n]}},{key:"getPrevTab",value:function(e){var t=this.getLiveChildren(),n=t.findIndex((function(t){return t.model.idx===e}))-1;return-1===n&&(n=t.length-1),t[n]}},{key:"getFirstTab",value:function(){return this.view.children.find((function(e){return e.viewLogic.isLive()}))}},{key:"getLastTab",value:function(){return this.view.children.slice().reverse().find((function(e){return e.viewLogic.isLive()}))}},{key:"getLiveChildren",value:function(){return this.view.children.filter((function(e){return e.viewLogic.isLive()}))}},{key:"getSelectedTab",value:function(){return this.view.children.find((function(e){return!0===e.viewLogic.isActive}))}},{key:"teardown",value:function(){vr.off(gr.sidebar.ACTIVE_TAB_SET),o.lastSelectedTab=this.getSelectedTab(),null!=this.removeResize&&this.removeResize()}}])&&ar(t.prototype,n),i&&ar(t,i),Object.defineProperty(t,"prototype",{writable:!1}),o}(_t);function Er(e){return Er="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Er(e)}function Lr(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,(r=i.key,o=void 0,o=function(e,t){if("object"!==Er(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var i=n.call(e,t||"default");if("object"!==Er(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(r,"string"),"symbol"===Er(o)?o:String(o)),i)}var r,o}function xr(e,t){return xr=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},xr(e,t)}function Pr(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,i=Tr(e);if(t){var r=Tr(this).constructor;n=Reflect.construct(i,arguments,r)}else n=i.apply(this,arguments);return function(e,t){if(t&&("object"===Er(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,n)}}function Tr(e){return Tr=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},Tr(e)}var jr=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&xr(e,t)}(o,e);var t,n,i,r=Pr(o);function o(e){var t;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,o),(t=r.call(this,e)).searchEnabled=Q.model.frame.controlOptions.controls.search,t.onClick(t.showPanel),t}return t=o,(n=[{key:"didChangeVisibility",value:function(e){this.view.parent.viewLogic.tabDidChangeVisibility()}},{key:"showPanel",value:function(e){if(!this.isActive){var t=this.view.parent,n=t.children;for(var i in n)n[i].viewLogic.hidePanel();this.el.classList.add("cs-selected"),this.el.setAttribute("aria-selected","true"),this.isActive=!0,t.viewLogic.setAsActive(this.view),DS.pubSub.trigger(DS.events.tab.SHOW,this.model.name)}}},{key:"hidePanel",value:function(){this.el.classList.remove("cs-selected"),this.el.setAttribute("aria-selected","false"),this.isActive=!1}},{key:"isLive",value:function(){var e=Q.model.currControlLayout[this.model.name];return _.isObject(e)?e.enabled:e}}])&&Lr(t.prototype,n),i&&Lr(t,i),Object.defineProperty(t,"prototype",{writable:!1}),o}(_t);function _r(e){return _r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},_r(e)}function Dr(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,Hr(i.key),i)}}function Ir(){return Ir="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(e,t,n){var i=function(e,t){for(;!Object.prototype.hasOwnProperty.call(e,t)&&null!==(e=Mr(e)););return e}(e,t);if(i){var r=Object.getOwnPropertyDescriptor(i,t);return r.get?r.get.call(arguments.length<3?e:n):r.value}},Ir.apply(this,arguments)}function Ar(e,t){return Ar=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},Ar(e,t)}function Rr(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,i=Mr(e);if(t){var r=Mr(this).constructor;n=Reflect.construct(i,arguments,r)}else n=i.apply(this,arguments);return function(e,t){if(t&&("object"===_r(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return Br(e)}(this,n)}}function Br(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Mr(e){return Mr=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},Mr(e)}function Hr(e){var t=function(e,t){if("object"!==_r(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var i=n.call(e,t||"default");if("object"!==_r(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===_r(t)?t:String(t)}var Nr=DS,Fr=Nr.TweenLite,Vr=Nr.dom,Wr=Nr._,Ur=Nr.events,zr=Nr.pubSub,Kr=Nr.utils.pxify,Zr=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Ar(e,t)}(o,e);var t,n,i,r=Rr(o);function o(e){var t,n,i,a;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,o),t=r.call(this,e),n=Br(t),a="search_toggle",(i=Hr(i="tooltipKey"))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a,Wr.bindAll(Br(t),"onToggleSearchInput","hideSlider"),t.nameSpace=Q.getCurrentNameSpace();var l=t.nameSpace.sidebar.children;return t.tabSelectedSlider=l.tabSelectedSlider.el,t.sliderMask=l.sliderMask.el,t.updateSliderLayout(),t}return t=o,n=[{key:"hasTooltip",get:function(){return"searchTab"===this.view.nameKey}},{key:"updateSliderLayout",value:function(){var e=this;if(Vr.hasClass(this.el,"cs-selected")){var t=this.nameSpace.tabs,n=Math.floor(t.y+t.h);this.sliderMask.style.height=Kr(n),window.requestAnimationFrame((function(){Fr.set(e.tabSelectedSlider,{x:e.view.x,y:n-1,width:e.view.w||0}),e.hideSlider()}))}}},{key:"onToggleSearchInput",value:function(e){o.showingSearch||(zr.trigger(Ur.sidebar.SHOW_SEARCH),this.view.parent.children.outlineTab.viewLogic.showPanel()),this.view.el.setAttribute("aria-expanded",!o.showingSearch)}},{key:"didChangeVisibility",value:function(e){Ir(Mr(o.prototype),"didChangeVisibility",this).call(this,e),this.tabSelectedSlider.style.opacity=0}},{key:"hideSlider",value:function(){this.tabSelectedSlider.style.display="none",Vr.addClass(this.el,"selected-animation-done"),this.tabSelectedSlider.style.opacity=1}},{key:"showSlider",value:function(){var e=document.querySelector(".selected-animation-done");null!=e&&Vr.removeClass(e,"selected-animation-done"),Vr.removeClass(this.el,"selected-animation-done"),this.tabSelectedSlider.style.display="block"}},{key:"animateSlider",value:function(){this.showSlider(),Fr.to(this.tabSelectedSlider,.25,{x:this.view.x,width:this.view.w||0,onComplete:this.hideSlider})}},{key:"showPanel",value:function(e){"searchTab"!==this.view.nameKey?(Ir(Mr(o.prototype),"showPanel",this).call(this,e),this.animateSlider(),o.showingSearch=!1):(this.onToggleSearchInput(),o.showingSearch=!0)}},{key:"isLive",value:function(){return Ir(Mr(o.prototype),"isLive",this).call(this)||"searchTab"===this.view.nameKey}},{key:"getViewBox",value:function(){var e=this.view.getBox();return e.h-=20,e}}],n&&Dr(t.prototype,n),i&&Dr(t,i),Object.defineProperty(t,"prototype",{writable:!1}),o}(jr),Qr=Zr,Gr=DS,qr=Gr._,Yr=Gr.scaler,Xr=Gr.utils.getPath,$r=Gr.dom,Jr=Gr.constants.refs.FRAME,eo="tabs",to=(Q.def(eo,Or,(function(e){var t=Q.model,n=Q.model.rtl,i=Q.getNamespace(e),r=i.sidebar,o=i.logo,a=t.frame.controlOptions.controls.search&&t.outlineInSidebar;return{ariaStringId:"sidebar-tabs",attrs:{id:eo,role:"tablist",tabindex:-1,"aria-label":"sidebar-tabs",class:"tabs"},overflow:"visible",x:0,y:function(){return o?o.bottom():0},w:function(){return r.data.actualWidth()},h:65,updateHook:function(){var e=this.w-13,t=this.children.length,i=e/t,r={startPos:13,pad:7};n&&(r.toLeft=!0,r.startPos=e),this.flowChildren(r);for(var o=0,l=0;l<t;l++)o+=this.children[l].lastWidthByText;a?n||(o+=33):e-=50;var s,c=o>e;c?$r.addClass(this.el,"overflow-tabs"):$r.removeClass(this.el,"overflow-tabs");for(var u=0;u<t;u++)s=this.children[u],a&&u===t-1?(s.x=n?bi:e-s.lastWidthByText,s.update()):s.width=c?Math.min(s.lastWidthByText,i-3):s.lastWidthByText},childDef:function(){to(Q.model,a)},methods:{updateDomStrings:function(){var e=this;requestAnimationFrame((function(){e.updateHook()}))}}}})),function(e,t){var n=!1,i=Xr(e,"sidebarTabs",[]);e.outlineInSidebar&&t&&!i.some((function(e){return"search"===e.name}))&&i.push({name:"search",idx:i.length-1,isIcon:!0,properties:{}}),i.forEach((function(t,i){var r=function(e,t,n,i){var r=t.name,o=e.getString("search"===r?"search_toggle":r),a=e.currControlLayout[r],l=!i&&(qr.isObject(a)?a.enabled:a),s=r+"Tab",c=null!=Or.lastSelectedTab?Or.lastSelectedTab.nameKey===s:l,u=Q.def(s,Qr,{model:Object.assign(t,{idx:n}),tag:"button",attrs:{id:s,class:"tab cs-tabs ".concat(c?"cs-selected":""),role:"tab","aria-selected":c?"true":"false","aria-label":o,"aria-controls":"".concat(r,"-panel"),tabindex:0===n?0:-1,"aria-expanded":"search"!==r&&null},calcTextSize:!0,w:function(){return this.width||this.lastWidthByText},noUpdate:!0,h:65,html:function(){return t.isIcon?Qn(r)():o},updateHook:function(){u.viewLogic.updateSliderLayout(),this.lastWidthByText<=10&&this.doTextCalcs()},methods:{doTextCalcs:function(){var e=1/Yr.getScale(),t=this.content.style.width;(DS.flagManager.multiLangSupport||DS.flagManager.aiCourseTranslation)&&(this.content.style.width="fit-content");var n=this.content.scrollWidth*e,i=this.content.clientHeight*e;(DS.flagManager.multiLangSupport||DS.flagManager.aiCourseTranslation)&&(this.content.style.width=t),this.lastWidthByText=n+4+this.padLeft+this.padRight,this.lastHeightByText=i+4},updateStrings:function(){var n=t.name,i="search"===n?"search_toggle":n;this.el.setAttribute("aria-label",e.getString(i)),t.isIcon||(this.content.textContent=e.getString(i)),this.doTextCalcs()}}});return u.init(),Q.getNamespace(Jr).tabs.append(u),c}(e,t,i,n);n=r||n}))});function no(e){return no="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},no(e)}function io(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,(r=i.key,o=void 0,o=function(e,t){if("object"!==no(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var i=n.call(e,t||"default");if("object"!==no(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(r,"string"),"symbol"===no(o)?o:String(o)),i)}var r,o}function ro(){return ro="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(e,t,n){var i=function(e,t){for(;!Object.prototype.hasOwnProperty.call(e,t)&&null!==(e=so(e)););return e}(e,t);if(i){var r=Object.getOwnPropertyDescriptor(i,t);return r.get?r.get.call(arguments.length<3?e:n):r.value}},ro.apply(this,arguments)}function oo(e,t){return oo=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},oo(e,t)}function ao(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,i=so(e);if(t){var r=so(this).constructor;n=Reflect.construct(i,arguments,r)}else n=i.apply(this,arguments);return function(e,t){if(t&&("object"===no(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return lo(e)}(this,n)}}function lo(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function so(e){return so=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},so(e)}var co=DS,uo=co._,fo=co.pubSub,ho=co.detection,po=co.globalEventHelper,yo=(po.addDocumentListener,po.removeDocumentListener,co.keyManager.isTabKey,co.playerGlobals),bo=co.utils,vo=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&oo(e,t)}(o,e);var t,n,i,r=ao(o);function o(e){var t;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,o),t=r.call(this,e),uo.bindAll(lo(t),"onShow"),fo.on(DS.events.tab.SHOW,t.onShow),t}return t=o,(n=[{key:"getFirstChild",value:function(){return uo.first(this.view.children)}},{key:"focusChild",value:function(){this.getFirstChild().viewLogic.focusSelf()}},{key:"onFocus",value:function(){}},{key:"onBlur",value:function(e){if(!yo.presentation.isPreview()||window.globals.HAS_SLIDE)if(DS.flagManager.aiCourseTranslation){var t=bo.getPath(this,this.view.lnk);ho.deviceView.isDesktop&&null!=t&&!t.translationShown&&this.isTargetOutsidePanel(e.relatedTarget)&&(ro(so(o.prototype),"onBlur",this).call(this,e),t.viewLogic.hidePanel())}else ho.deviceView.isDesktop&&null!=this.view.lnk&&this.isTargetOutsidePanel(e.relatedTarget)&&(ro(so(o.prototype),"onBlur",this).call(this,e),this.view.lnk.viewLogic.hidePanel())}},{key:"isTargetOutsidePanel",value:function(e){return null==e||!this.el.contains(e)&&uo.camelCase(e.id)!==this.view.lnk.nameKey&&uo.camelCase(e.parentElement.id)!==this.view.lnk.nameKey}},{key:"onShow",value:function(e){var t="".concat(e,"Panel");if(this.view.nameKey===t){var n=this.view.parent.children,i=this.getFirstChild();for(var r in n)r.indexOf("Panel")>-1&&n[r].viewLogic.onHide();this.el.style.display="block",null!=i.onPanelVisible&&i.onPanelVisible()}}},{key:"onHide",value:function(){this.el.style.display="none"}},{key:"isTopLink",value:function(){return null!=this.view.tabsId}},{key:"getTopTabsView",value:function(){return this.view.lnk.parent.parent}},{key:"teardown",value:function(){fo.off(DS.events.tab.SHOW,this.onShow)}}])&&io(t.prototype,n),i&&io(t,i),Object.defineProperty(t,"prototype",{writable:!1}),o}(_t);function go(e){return go="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},go(e)}function mo(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,(r=i.key,o=void 0,o=function(e,t){if("object"!==go(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var i=n.call(e,t||"default");if("object"!==go(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(r,"string"),"symbol"===go(o)?o:String(o)),i)}var r,o}function wo(e,t){return wo=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},wo(e,t)}function So(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,i=ko(e);if(t){var r=ko(this).constructor;n=Reflect.construct(i,arguments,r)}else n=i.apply(this,arguments);return function(e,t){if(t&&("object"===go(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,n)}}function ko(e){return ko=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},ko(e)}var Co=DS.detection,Oo=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&wo(e,t)}(o,e);var t,n,i,r=So(o);function o(){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,o),r.apply(this,arguments)}return t=o,n=[{key:"focusSelf",value:function(){var e=this.view.children,t=e.search,n=e.outline;if(null!=t){var i=t.viewLogic.searchInputEl;Co.deviceView.isUnifiedMobile?(i.style.pointerEvents="none",setTimeout((function(){i.style.pointerEvents="all"}),100)):i.focus()}else n.viewLogic.focusSelf()}},{key:"onFocus",value:function(){}}],n&&mo(t.prototype,n),i&&mo(t,i),Object.defineProperty(t,"prototype",{writable:!1}),o}(_t),Eo=Oo;function Lo(e){return Lo="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Lo(e)}function xo(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,(r=i.key,o=void 0,o=function(e,t){if("object"!==Lo(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var i=n.call(e,t||"default");if("object"!==Lo(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(r,"string"),"symbol"===Lo(o)?o:String(o)),i)}var r,o}function Po(e,t){return Po=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},Po(e,t)}function To(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,i=_o(e);if(t){var r=_o(this).constructor;n=Reflect.construct(i,arguments,r)}else n=i.apply(this,arguments);return function(e,t){if(t&&("object"===Lo(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return jo(e)}(this,n)}}function jo(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function _o(e){return _o=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},_o(e)}var Do=DS,Io=Do.pubSub,Ao=Do.events,Ro=Do.TweenLite,Bo=Do.focusManager,Mo=Do._.bindAll,Ho=Do.dom.addClass,No=Do.detection,Fo=!1,Vo=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Po(e,t)}(o,e);var t,n,i,r=To(o);function o(e){var t;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,o);var n=(t=r.call(this,e)).view.children.searchForm.el,i=t.view.children.searchInput.el;return n.addEventListener("submit",t.onSearch.bind(jo(t))),No.deviceView.isMobile&&(Mo(jo(t),"onMobileOutlineShown"),i.addEventListener("input",t.onSearch.bind(jo(t))),Io.on(Ao.mobile.OUTLINE_SHOWN,t.onMobileOutlineShown)),t.ensureEventSubscriptions(),t}return t=o,n=[{key:"teardown",value:function(){Io.off(Ao.mobile.OUTLINE_SHOWN,this.onMobileOutlineShown),Io.off(Ao.controlOptions.RESET,this.onControlOptionsReset,this),Fo=!1}},{key:"ensureEventSubscriptions",value:function(){Fo||(Io.on(Ao.controlOptions.RESET,this.onControlOptionsReset,this),Fo=!0)}},{key:"onControlOptionsReset",value:function(){this.view.updateHtml(),this.view.update()}},{key:"onMobileOutlineShown",value:function(){document.getElementById("outline-panel").appendChild(this.el),Ho(document.body,"tab-shown")}},{key:"onFocus",value:function(e){Bo.setFocusRectOn(this.view.el)}},{key:"onSearch",value:function(e){e.preventDefault();var t=this.view.parent.children.searchResults,n=this.view.children.bottomDiv,i=document.querySelector("#outline-content ul");Ro.to(i,.2,{alpha:0,onComplete:function(){t.el.style.opacity=0,Ro.to(t.el,.2,{alpha:1,onComplete:function(){return t.viewLogic.onAfterVisible()}}),n.el.style.opacity=0,t.setVisibility(!0)}}),t.viewLogic.performSearch(this.searchInputEl.value),Ho(document.body,"search-results-active")}},{key:"updateVisibility",value:function(e){var t=this.view,n=t.searchResults,i=t.searchResults.visible,r=t.visible;this.view.setVisibility(e),n.setVisibility(e&&this.searchResultsOpen),r&&(this.searchResultsOpen=i)}}],n&&xo(t.prototype,n),i&&xo(t,i),Object.defineProperty(t,"prototype",{writable:!1}),o}(_t),Wo=Vo;function Uo(e){return Uo="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Uo(e)}function zo(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,(r=i.key,o=void 0,o=function(e,t){if("object"!==Uo(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var i=n.call(e,t||"default");if("object"!==Uo(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(r,"string"),"symbol"===Uo(o)?o:String(o)),i)}var r,o}function Ko(){return Ko="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(e,t,n){var i=function(e,t){for(;!Object.prototype.hasOwnProperty.call(e,t)&&null!==(e=qo(e)););return e}(e,t);if(i){var r=Object.getOwnPropertyDescriptor(i,t);return r.get?r.get.call(arguments.length<3?e:n):r.value}},Ko.apply(this,arguments)}function Zo(e,t){return Zo=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},Zo(e,t)}function Qo(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,i=qo(e);if(t){var r=qo(this).constructor;n=Reflect.construct(i,arguments,r)}else n=i.apply(this,arguments);return function(e,t){if(t&&("object"===Uo(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return Go(e)}(this,n)}}function Go(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function qo(e){return qo=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},qo(e)}var Yo=DS,Xo=Yo.pubSub,$o=Yo.events,Jo=Yo.TweenLite,ea=Yo._.bindAll,ta=Yo.dom,na=ta.addClass,ia=ta.removeClass,ra=Yo.focusManager,oa=Yo.detection,aa=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Zo(e,t)}(o,e);var t,n,i,r=Qo(o);function o(e){var t;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,o),t=r.call(this,e),ea(Go(t),"onShowSearch","onHideSearch","onFocus"),t.nameSpace=Q.getCurrentNameSpace(),t.toggle=!1,Xo.on($o.sidebar.SHOW_SEARCH,t.onShowSearch),Xo.on($o.sidebar.HIDE_SEARCH,t.onHideSearch),t.searchInput=t.view.children.searchForm.el,t.searchInput.style.display="none",t.searchInput.style.opacity=0,Q.model.outlineInSidebar||(t.toggle=!0,t.searchInput.style.opacity=1,t.searchInput.style.display="block");return t.searchInputField=t.view.children.searchInput.el,t.searchInputField.addEventListener("input",t.onSearchTermChanged.bind(Go(t))),oa.browser.isWebKit||t.searchInputField.addEventListener("keydown",(function(e){27===e.keyCode&&t.clearSearch()})),t.onClickEl(t.clearEl,(function(e){t.onFocus(e),t.clearSearch(),t.searchInputField.focus()})),t}return t=o,n=[{key:"onShowSearch",value:function(e){var t=this;this.toggle||(this.toggle=!0,this.view.update(),this.view.parent.children.outline.update(),this.searchInput.style.display="block",Jo.to(this.searchInput,.2,{opacity:1,delay:.2,onComplete:function(){t.searchInputField.focus()}}),na(document.body,"search-input-shown"))}},{key:"onHideSearch",value:function(e){var t=this;this.toggle=!1,this.view.update(),Jo.to(this.searchInput,.2,{opacity:0,delay:0,onComplete:function(){t.view.parent.children.outline.update(),Qr.showingSearch=!1,t.searchInput.style.display="none"}}),ia(document.body,"search-input-shown")}},{key:"onFocus",value:function(e){ra.setFocusRectOn(e.target)}},{key:"onBlur",value:function(e){var t=this.view.parent.children.searchResults;Q.model.outlineInSidebar&&!t.visible&&this.onHideSearch()}},{key:"onSearch",value:function(e){this.isSearchFieldEmtpy()?e.preventDefault():(Ko(qo(o.prototype),"onSearch",this).call(this,e),ia(this.clearEl,"hidden"),Xo.trigger($o.search.UPDATE_PANEL))}},{key:"onSearchTermChanged",value:function(){this.isSearchFieldEmtpy()&&this.clearSearch()}},{key:"clearSearch",value:function(){na(this.clearEl,"hidden"),Xo.trigger($o.search.CLEAR)}},{key:"isSearchFieldEmtpy",value:function(){var e=this.searchInputEl.value;return null==e||""===e}}],n&&zo(t.prototype,n),i&&zo(t,i),Object.defineProperty(t,"prototype",{writable:!1}),o}(Wo),la=aa,sa="search",ca=Q.def(sa,la,(function(){var e=Q.model;return{ariaStringId:"acc_search_input",attrs:{class:sa,"aria-label":e.getString("acc_search_input"),role:"search",tabIndex:-1},z:3,x:0,y:0,w:"100%",h:function(){return this.viewLogic.toggle?60:0},html:function(){return'\n      <div class="search-ui">\n        <div data-ref="bottomDiv">\n        </div>\n        <form id="outline-search" data-ref="searchForm" class="search-input cs-outlinesearch cs-searchinput">\n          <input tabindex="0" class="cs-input " data-ref="searchInput" type="search" placeholder="'.concat(e.getString("search"),'">\n        </form>\n        <div data-ref="clear" class="search-clear hidden">\n          <button class="btn cs-button">\n            <span data-ref="searchClearLabel" class="search-clear-close" aria-label="').concat(e.getString("close"),'">').concat(Qn("searchClear")(),"</span>\n          </button>\n        </div>\n      </div>")},methods:{updateDomStrings:function(){this.viewLogic.searchInputEl.setAttribute("placeholder",e.getString("search")),this.viewLogic.searchClearLabelEl.setAttribute("aria-label",e.getString("close"))}},updateHook:function(){this.viewLogic.ensureEventSubscriptions()}}}));function ua(e){return ua="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},ua(e)}function fa(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,(r=i.key,o=void 0,o=function(e,t){if("object"!==ua(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var i=n.call(e,t||"default");if("object"!==ua(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(r,"string"),"symbol"===ua(o)?o:String(o)),i)}var r,o}function da(){return da="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(e,t,n){var i=function(e,t){for(;!Object.prototype.hasOwnProperty.call(e,t)&&null!==(e=ba(e)););return e}(e,t);if(i){var r=Object.getOwnPropertyDescriptor(i,t);return r.get?r.get.call(arguments.length<3?e:n):r.value}},da.apply(this,arguments)}function ha(e,t){return ha=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},ha(e,t)}function pa(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,i=ba(e);if(t){var r=ba(this).constructor;n=Reflect.construct(i,arguments,r)}else n=i.apply(this,arguments);return function(e,t){if(t&&("object"===ua(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return ya(e)}(this,n)}}function ya(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function ba(e){return ba=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},ba(e)}var va=DS,ga=va.focusManager,ma=va.keyManager,wa=va.detection,Sa=va.globalEventHelper,ka=Sa.addDocumentListener,Ca=Sa.removeDocumentListener,Oa=(va.utils.getPath,va.dom),Ea=Oa.parentNodesOf,La=Oa.addClass,xa=Oa.removeClass,Pa=va._,Ta=Pa.first,ja=Pa.last,_a=Pa.bindAll,Da=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&ha(e,t)}(o,e);var t,n,i,r=pa(o);function o(e){var t;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,o),t=r.call(this,e),_a(ya(t),"onKeydown","addEvents","onClickItem"),t.addEvents(),t}return t=o,(n=[{key:"focusSelf",value:function(){this.hasListItems()&&this.getItems().find((function(e){return 0===e.tabIndex})).focus()}},{key:"addEvents",value:function(){var e=this;wa.device.isMobile?(this.el.addEventListener("touchmove",(function(t){e.moved=!0})),this.onClick((function(t){e.moved||e.onClickItem(t),e.moved=!1}))):this.onClick(this.onClickItem)}},{key:"onClickItem",value:function(){}},{key:"onFocus",value:function(){!this.isFocused&&this.hasListItems()&&(this.isFocused=!0,this.currentItem=this.currentItem||Ta(this.getItems()),La(this.currentItem,"hover"),ma.isShowFocus&&this.centerOnFocused(),ga.setFocusRectOn(this.getFocusRectTarget()),ka("keydown",this.onKeydown))}},{key:"onBlur",value:function(e){this.el.contains(e.relatedTarget)||(da(ba(o.prototype),"onBlur",this).call(this,e),null!=this.currentItem&&(xa(this.currentItem,"hover"),this.currentItem.style.backgroundColor=""),Ca("keydown",this.onKeydown),this.isFocused=!1)}},{key:"onKeydown",value:function(e){var t=this.currentItem;ma.isActionKey(e.which)?(this.activateItem(e),ma.isSpaceKey(e.which)&&e.preventDefault()):ma.isDownishKey(e.which)?this.currentItem=this.getNextItem(this.getItemContent()):ma.isUpishKey(e.which)?this.currentItem=this.getPrevItem(this.getItemContent()):ma.isHomeKey(e.which)?this.currentItem=this.getFirstItem():ma.isEndKey(e.which)&&(this.currentItem=this.getLastItem()),t!==this.currentItem&&(e.preventDefault(),t.tabIndex=-1,xa(t,"hover"),this.focusOnCurrent())}},{key:"hasListItems",value:function(){return!1}},{key:"getFocusRectTarget",value:function(){return this.currentItem}},{key:"focusOnCurrent",value:function(){this.centerOnFocused(),document.activeElement!==this.currentItem&&(this.currentItem.tabIndex=0,La(this.currentItem,"hover"),this.currentItem.focus()),ga.setFocusRectOn(this.getFocusRectTarget())}},{key:"activateItem",value:function(){this.onClickItem({target:this.currentItem.firstElementChild})}},{key:"getNextItem",value:function(e){var t=this,n=this.getItems().findIndex((function(n){return t.findIndexCb(n,e)}))+1;return n===this.getItems().length&&(n=0),this.getItems()[n]}},{key:"getPrevItem",value:function(e){var t=this,n=this.getItems().findIndex((function(n){return t.findIndexCb(n,e)}))-1;return-1===n&&(n=this.getItems().length-1),this.getItems()[n]}},{key:"getLastItem",value:function(){return ja(this.getItems())}},{key:"getFirstItem",value:function(){return Ta(this.getItems())}},{key:"getItemContent",value:function(){return this.currentItem.textContent.trim()}},{key:"findIndexCb",value:function(e,t){return e.textContent.trim()===t}},{key:"getItems",value:function(){return[]}},{key:"getScrollEl",value:function(){return this.el}},{key:"getOffsetEl",value:function(){return this.el}},{key:"getOffsetTop",value:function(e){return function(e){return Ea(e,(function(e){return"li"===e.nodeName.toLowerCase()})).reduce((function(e,t){return e+t.offsetTop}),0)}(e)}},{key:"getOffsetHeight",value:function(e){return e.offsetHeight}},{key:"centerOnFocused",value:function(){if(null!=this.currentItem){var e=this.getOffsetEl(),t=this.getOffsetHeight(e),n=this.getScrollEl().scrollTop,i=this.getOffsetTop(this.currentItem),r=i+this.getOffsetHeight(this.currentItem);r-n>t?e.scrollTop=r-t+10:i<n+10&&(e.scrollTop=i-10)}}}])&&fa(t.prototype,n),i&&fa(t,i),Object.defineProperty(t,"prototype",{writable:!1}),o}(_t);function Ia(e){return Ia="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Ia(e)}function Aa(e){return function(e){if(Array.isArray(e))return Ma(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||Ba(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Ra(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var i,r,o,a,l=[],s=!0,c=!1;try{if(o=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;s=!1}else for(;!(s=(i=o.call(n)).done)&&(l.push(i.value),l.length!==t);s=!0);}catch(e){c=!0,r=e}finally{try{if(!s&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(c)throw r}}return l}}(e,t)||Ba(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Ba(e,t){if(e){if("string"==typeof e)return Ma(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?Ma(e,t):void 0}}function Ma(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,i=new Array(t);n<t;n++)i[n]=e[n];return i}function Ha(e,t){return Ha=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},Ha(e,t)}function Na(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,i=Va(e);if(t){var r=Va(this).constructor;n=Reflect.construct(i,arguments,r)}else n=i.apply(this,arguments);return function(e,t){if(t&&("object"===Ia(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return Fa(e)}(this,n)}}function Fa(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Va(e){return Va=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},Va(e)}function Wa(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,(r=i.key,o=void 0,o=function(e,t){if("object"!==Ia(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var i=n.call(e,t||"default");if("object"!==Ia(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(r,"string"),"symbol"===Ia(o)?o:String(o)),i)}var r,o}function Ua(e,t,n){return t&&Wa(e.prototype,t),n&&Wa(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function za(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}var Ka=function(e){return rt(e.slideid)},Za=DS,Qa=Za.windowManager,Ga=Za.focusManager,qa=Za.TweenLite,Ya=Za.events,Xa=Za.pubSub,$a=Za.keyManager,Ja=Za.resolver,el=Za.dom,tl=(el.hasClass,el.removeClass),nl=el.addClass,il=el.getParentWithClass,rl=Za._,ol=rl.bindAll,al=(rl.first,Za.utils),ll=al.stripTags,sl=(al.stripPlayer,al.prefixWithPlayer),cl=Za.globalEventHelper,ul=cl.addDocumentListener,fl=(cl.removeDocumentListener,Za.detection),dl=fl.theme,hl=fl.os,pl=Ua((function e(t,n,i){za(this,e),this.slideId=t,this.title=n,this.text=i})),yl=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Ha(e,t)}(n,e);var t=Na(n);function n(e){var i;za(this,n),i=t.call(this,e),ol(Fa(i),"traverseLinks","performSearch","onSlideChanged","onClear","onReset","loadSlideBankSearchData"),i.animationDuration=.2;var r=Q.model.frame.notesData||[];return i.notesBySlideId=r.map((function(e){return new pl(sl(e.slideId),DS.utils.getPath(Ja.resolvePath(e.slideId,DS.presentation),"title"),ll(e.content).trim())})),i.slideTextData=Q.model.frame.navData.search||[],i.slideTextBySlideId=i.slideTextData.filter((function(e){return!e.slidebank})).map((function(e){return new pl(sl(e.slideid),DS.utils.getPath(Ja.resolvePath(e.slideid,DS.presentation),"title"),ll(e.Text).trim())})),i.menuOptions=Q.model.frame.controlOptions.menuOptions,i.wrapListItems=i.menuOptions.wrapListItems,i.links=Q.model.frame.navData.outline.links,i.hasSlideBankSlides=i.slideTextData.some((function(e){return e.slidebank})),i.hasSlideBankSlides&&Xa.on(Ya.player.RESET,i.onReset),null!=i.clearEl&&i.onClickEl(i.clearEl,i.onClear),Xa.on(Ya.search.CLEAR,i.onClear),i.onClickEl(i.searchFilterEl,i.onToggleSearchOptions),i.onClick(i.onClickLink),Xa.on(Ya.window.MAIN_CHANGED,i.onSlideChanged),i}return Ua(n,[{key:"loadSlideBankSearchData",value:function(){this.slideBankTextBySlideId=this.slideTextData.filter((function(e){return e.slidebank})).reduce((function(e,t){var n=Ja.getSlideBankSlideInstance(t.slideid);return null!==n&&e.push(new pl(n.absoluteId,n.title(),ll(t.Text))),e}),[])}},{key:"teardown",value:function(){Xa.off(Ya.window.MAIN_CHANGED,this.onSlideChanged),window.globals.HAS_SLIDE&&Xa.off(Ya.search.CLEAR,this.onClear)}},{key:"onSlideChanged",value:function(e){var t=this.resultsEl.querySelector(".cs-selected"),n=this.resultsEl.querySelector('[data-slide-id="'.concat(e.absoluteId,'"]'));null!=t&&tl(t,"cs-selected"),null!=n&&(nl(n,"cs-selected"),nl(n,"cs-viewed"),this.updateAriaLabel(n))}},{key:"onReset",value:function(){this.onClear(),this.loadSlideBankSearchData()}},{key:"onFocus",value:function(e){e.relatedTarget;var t=e.target;if(this.el.contains(t)){if(t===this.searchFilterEl||t===this.clearEl)return Ga.setFocusRectOn(t),this.isFocused=!1,!1;if(t===this.notesCheckEl||t===this.slideCheckEl)return Ga.setFocusRectOn(t.parentElement),this.isFocused=!1,!1;if(!this.isFocused&&(!dl.isUnified||$a.isShowFocus)){var n=Ra(this.getItems(),1)[0];this.isFocused=!0,null!=n&&(this.currentItem=n,this.lastSelected!==this.currentItem&&this.focusOnCurrent(),ul("keydown",this.onKeydown))}}return!1}},{key:"getIsSearchVisible",value:function(){return this.searchOptionsEl.classList.contains("visible")}},{key:"setCheckboxesVisible",value:function(e){this.notesCheckEl.hidden=e,this.slideCheckEl.hidden=e}},{key:"onToggleSearchOptions",value:function(e){var t=this,n=this.getIsSearchVisible(),i=n?"remove":"add";this.searchOptionsEl.classList[i]("visible"),n?(clearTimeout(this.checkBoxTimeout),this.checkBoxTimeout=setTimeout((function(){t.setCheckboxesVisible(n)}),200)):this.setCheckboxesVisible(n)}},{key:"onClickLink",value:function(e){if("locked"!==this.menuOptions.flow){var t=il(e.target,"listitem"),n=t.dataset.slideId;null==n||"restricted"===this.menuOptions.flow&&!e.target.classList.contains("cs-viewed")||(nl(t,"cs-viewed"),this.updateAriaLabel(t),Xa.trigger(Ya.request.NEXT_SLIDE,n))}}},{key:"updateAriaLabel",value:function(e){dl.isUnified&&Aa(e.querySelector(".outline-states").children).forEach((function(t){if("none"===window.getComputedStyle(t).display);else{var n=t.getAttribute("aria-label"),i=e.getAttribute("data-slide-title");e.children[0].textContent=i+" "+n}}))}},{key:"traverseLinks",value:function(e){for(var t=0;t<e.length;t++){var n=e[t];Ka(n)||this.searchResults.has(n.slideid)||(this.noSearchTerm||n.displaytext.toLowerCase().indexOf(this.term)>=0)&&this.searchResults.set(n.slideid,n.displaytext),null!=n.links&&this.traverseLinks(n.links)}}},{key:"getDefaultSate",value:function(e){var t=Qa.getCurrentWindow().getCurrentSlide().absoluteId===e||Ja.resolvePath(e).viewed,n=this.menuOptions.flow;if(t)switch(n){case"free":case"restricted":return Q.model.getString("acc_visited");case"locked":return"".concat(Q.model.getString("acc_visited"),", ").concat(Q.model.getString("acc_locked"))}else switch(n){case"free":return"";case"restricted":case"locked":return Q.model.getString("acc_locked")}}},{key:"performSearch",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";if(window.globals.HAS_SLIDE){this.term=t.toLowerCase(),this.items=null,this.noSearchTerm=""===t,this.searchResults=new Map,this.resultsEl.innerHTML="",this.slideTextEnabled=this.slideCheckEl.checked,this.notesEnabled=this.notesCheckEl.checked;var n=Q.model.frame.navData.links;null!=n&&this.traverseLinks(n),this.hasSlideBankSlides&&void 0===this.slideBankTextBySlideId&&this.loadSlideBankSearchData();var i=function(t){t.forEach((function(t){(t.text.toLowerCase().indexOf(e.term)>=0||t.title.toLowerCase().indexOf(e.term)>=0)&&e.searchResults.set(t.slideId,t.title)}))},r=!this.noSearchTerm;this.slideTextEnabled&&r&&i(this.slideTextBySlideId),this.hasSlideBankSlides&&r&&i(this.slideBankTextBySlideId),this.notesEnabled&&r&&i(this.notesBySlideId);hl.isIOS;var o=this.wrapListItems?"":"no-wrap",a=Qa.getCurrentWindow().getCurrentSlide().absoluteId,l=0;this.searchResults.forEach((function(t,n){0;var i=a===n?"cs-selected":"",r=document.createElement("li"),s=Ja.resolvePath(n).viewed?"cs-viewed":"",c=e.menuOptions.flow,u=e.getDefaultSate(n),f='\n      <div role="menuitem" class="cs-listitem listitem '.concat(i," ").concat(o," ").concat(s," ").concat("free"!==c&&"cs-"+c,'"\n           data-slide-id="').concat(n,'"\n           data-slide-title="').concat(t,'"\n           tabindex="').concat(l,'"\n           role = "treeitem">\n          <span style="position: absolute; opacity: 0;">').concat(t," ").concat(u,"</span>");dl.isUnified?f+='\n           <span class="linkText" aria-hidden="true">\n             '.concat(t,'\n           </span>\n           <span class="outline-states" aria-hidden="true">\n             <span class="visitedIcon" aria-label="').concat(Q.model.getString("acc_visited"),'">\n               ').concat(Qn("checkmark")(),'\n             </span>\n             <span class="lockedIcon" aria-label="').concat(Q.model.getString("acc_locked"),'">\n               ').concat(Qn("lock")(),'\n             </span>\n             <span class="lockedViewedIcon" aria-label="').concat(Q.model.getString("acc_visited"),", ").concat(Q.model.getString("acc_locked"),'">\n               ').concat(Qn("lockedViewed")(),"\n             </span>\n           </span>\n        </div>\n      "):f+="\n             ".concat(t,"\n        </div>"),r.innerHTML=f,e.resultsEl.appendChild(r),l=-1}))}}},{key:"onClear",value:function(){var e=this,t=this.view.parent.children,n=t.outline,i=t.search;this.outlineUl=this.outlineUl||document.querySelector("#outline-content ul"),n.setVisibility(!0),qa.to(this.el,this.animationDuration,{alpha:0,onComplete:function(){qa.to(e.outlineUl,e.animationDuration,{alpha:1}),e.view.setVisibility(!1),i.children.bottomDiv.el.style.opacity=1,$a.isShowFocus&&e.el.contains(document.activeElement)&&n.el.focus()}}),this.term="",i.children.searchInput.el.value="",tl(document.body,"search-results-active"),Xa.trigger(Ya.search.UPDATE_PANEL)}},{key:"hasListItems",value:function(){return!_.isEmpty(this.getItems())}},{key:"getItems",value:function(){return this.items=this.items||Aa(this.el.querySelectorAll(".cs-listitem")),this.items}},{key:"activateItem",value:function(){this["locked"!==this.menuOptions.flow?"onClickLink":"onCarrotClick"]({target:this.currentItem})}},{key:"findIndexCb",value:function(e,t){return e===t}},{key:"getItemContent",value:function(){return this.currentItem}},{key:"onAfterVisible",value:function(){this.view.parent.children.outline.setVisibility(!1)}}]),n}(Da),bl=yl,vl="searchResults",gl=Q.def(vl,bl,(function(e){var t=Q.model,n=t.rtl?"rtl":"";return{attrs:{id:vl+"-content",class:"cs-menu cs-panel panel ".concat(n),tabindex:-1},z:2,x:0,y:68,w:"100%",overflow:"scroll",h:function(){return this.parent.h-58-2-bi},visible:!1,html:'\n      <span class="cs-outline search-content">\n        <div style="display:none">\n          <span class="flex-static-auto">\n            <h4 data-ref="title" class=\'cs-heading search-heading panel-section-heading\'>\n              '.concat(t.getString("search_results"),'\n            </h4>\n            <button class="btn-unstyled search-filter cs-search-filter" tabindex="0" data-ref="searchFilter">\n              <span data-ref="searchFilterLabel">').concat(t.getString("filter"),"</span>\n              ").concat(Qn("filter")(),'\n            </button>\n          </span>\n\n          <div class="search-options flex-static-auto" data-ref="searchOptions">\n            <p data-ref="searchOptionsLabel">').concat(t.getString("search_in"),'</p>\n            <label>\n              <input data-ref="notesCheck" type="checkbox" checked>\n              <span data-ref="notesCheckLabel">').concat(t.getString("transcript_chk"),'</span>\n            </label>\n            <label>\n              <input data-ref="slideCheck" type="checkbox" checked>\n              <span data-ref="slideCheckLabel">').concat(t.getString("slide_text_chk"),'</span>\n            </label>\n          </div>\n        </div>\n        <div class="search-results is-scrollable" tabindex="-1" data-ref="searchResults">\n          <ul data-ref="results"></ul>\n        </div>\n      </span>\n    '),methods:{updateDomStrings:function(){this.viewLogic.titleEl.textContent=t.getString("search_results"),this.viewLogic.searchFilterLabelEl.textContent=t.getString("filter"),this.viewLogic.searchOptionsLabelEl.textContent=t.getString("search_in"),this.viewLogic.notesCheckLabelEl.textContent=t.getString("transcript_chk"),this.viewLogic.slideCheckLabelEl.textContent=t.getString("slide_text_chk")}}}}));function ml(e){return ml="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},ml(e)}function wl(e){return function(e){if(Array.isArray(e))return Sl(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"==typeof e)return Sl(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Sl(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Sl(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,i=new Array(t);n<t;n++)i[n]=e[n];return i}function kl(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,(r=i.key,o=void 0,o=function(e,t){if("object"!==ml(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var i=n.call(e,t||"default");if("object"!==ml(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(r,"string"),"symbol"===ml(o)?o:String(o)),i)}var r,o}function Cl(){return Cl="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(e,t,n){var i=function(e,t){for(;!Object.prototype.hasOwnProperty.call(e,t)&&null!==(e=xl(e)););return e}(e,t);if(i){var r=Object.getOwnPropertyDescriptor(i,t);return r.get?r.get.call(arguments.length<3?e:n):r.value}},Cl.apply(this,arguments)}function Ol(e,t){return Ol=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},Ol(e,t)}function El(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,i=xl(e);if(t){var r=xl(this).constructor;n=Reflect.construct(i,arguments,r)}else n=i.apply(this,arguments);return function(e,t){if(t&&("object"===ml(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return Ll(e)}(this,n)}}function Ll(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function xl(e){return xl=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},xl(e)}var Pl,Tl,jl,_l=DS,Dl=_l.detection,Il=_l.pubSub,Al=_l._,Rl=_l.events,Bl=_l.keyManager,Ml=_l.resolver,Hl=_l.windowManager,Nl=_l.utils.prefixWithPlayer,Fl=_l.focusManager.setFocusRectOn,Vl=_l.globalEventHelper.addDocumentListener,Wl=_l.dom,Ul=Wl.parentNodesOf,zl=Wl.getParentWithClass,Kl=Wl.hasClass,Zl=Wl.addClass,Ql=Wl.removeClass,Gl=function(e){return Ul(e,(function(e){return"li"===e.nodeName.toLowerCase()}))},ql=function(e){return Gl(e).slice(1).some((function(e){return Kl(e,"item-collapsed")}))},Yl=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Ol(e,t)}(o,e);var t,n,i,r=El(o);function o(e){var t;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,o),t=r.call(this,e),Al.bindAll(Ll(t),"updateVisitedSlides","onSlideChanged","onSelectFirstSlideLink","addSlideToVisited","visuallyUpdateLinks","collapseLastItem","onClickLink","onCarrotClick","onClickItem","centerOnFocused","setDrawSlides","mouseDown","mouseUp"),t.visitedSlides=new Set,t.menuOptions=Q.model.frame.controlOptions.menuOptions,Il.once(Rl.resume.SET_DATA,t.updateVisitedSlides),Il.on(Rl.window.MAIN_CHANGED,t.onSlideChanged),Il.on(Rl.mobile.OUTLINE_SHOWN,t.centerOnFocused),Il.on(Rl.navData.SELECT_FIRST_SLIDE_LINK,t.onSelectFirstSlideLink),t.removeDocListeners=Al.flow(Vl("mousedown",t.mouseDown),Vl("mouseup",t.mouseUp)),null!=DS.presentation.getDrawPromise&&DS.presentation.getDrawPromise().then(t.setDrawSlides),t.isTopTabChild=Q.model.topTabs.some((function(e){return"outline"===e.name})),DS.flagManager.aiCourseTranslation&&Il.on(Rl.frame.SHOW_TAB_ITEM,t.onShowTabItem.bind(Ll(t))),Il.on(Rl.navData.REFRESH_VIEW,(function(){if(DS.flagManager.multiLangSupport){var e=DS.utils.getPath(t.el.querySelector(".cs-selected"),"dataset.ref");t.view.updateHtml(),t.view.update(),t.view.initChildRefs(),t.visuallyUpdateLinks();var n=DS.utils.getPath(t.view,["children",e,"el"]);null!=n&&Zl(n,"cs-selected")}else t.view.updateHtml(),t.view.update()})),t}return t=o,n=[{key:"teardown",value:function(){Il.off(Rl.window.MAIN_CHANGED,this.onSlideChanged),Il.off(Rl.mobile.OUTLINE_SHOWN,this.centerOnFocused),this.removeDocListeners()}},{key:"focusSelf",value:function(){this.getItems().find((function(e){return 0===e.parentNode.tabIndex})).parentNode.focus()}},{key:"onFocus",value:function(){var e=this;this.isMouseDown||(null!=this.view.parent.children.search&&Dl.theme.isUnified?setTimeout((function(){e.el.contains(document.activeElement)&&Cl(xl(o.prototype),"onFocus",e).call(e)}),500):Cl(xl(o.prototype),"onFocus",this).call(this))}},{key:"mouseDown",value:function(){this.isMouseDown=!0}},{key:"mouseUp",value:function(){this.isMouseDown=!1}},{key:"onClickItem",value:function(e){var t=this;if(Dl.theme.isUnified){var n=zl(e.target,"listitem");Kl(e.target,"carrot")&&(t.onCarrotClick(n),1)||n&&Kl(n.parentNode,"item-collapsible")&&(t.onCarrotClick(n),!Kl(n,"is-promoted-slide"))||"locked"!==t.menuOptions.flow&&t.onClickLink(n)}else("locked"!==this.menuOptions.flow?this.onClickLink:this.onCarrotClick)(e.target)}},{key:"visuallyUpdateLinks",value:function(){var e=this;this.visitedSlides.forEach((function(t){var n=e.view.children[t];null!=n&&(Dl.theme.isUnified?e.updateViewedState(n.el):Zl(n.el,"cs-viewed"))})),Dl.theme.isUnified&&(this.setComplete("div.is-promoted-slide"),this.setComplete("div.is-scene",1))}},{key:"setComplete",value:function(e){var t=this,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,i=this.el.querySelectorAll(e);Array.from(i).forEach((function(e){Array.from(e.parentNode.querySelectorAll("div")).slice(n).every((function(e){return Kl(e,"cs-viewed")}))&&(Zl(e,"cs-complete"),t.updateAriaLabel(e))}))}},{key:"updateVisitedSlides",value:function(e,t){null!=e&&(e.forEach(this.addSlideToVisited),this.visuallyUpdateLinks())}},{key:"getNextItem",value:function(e){var t,n=this,i=this.getItems(),r=i.findIndex((function(t){return n.findIndexCb(t,e)}));do{(r+=1)===i.length&&(r=0),t=i[r]}while(ql(t));return t}},{key:"getPrevItem",value:function(e){var t,n=this,i=this.getItems(),r=i.findIndex((function(t){return n.findIndexCb(t,e)}));do{-1==(r-=1)&&(r=i.length-1),t=i[r]}while(ql(t));return t}},{key:"addSlideToVisited",value:function(e){var t=e.absoluteId,n=e.getScene().absoluteId;this.visitedSlides.add(n).add(t)}},{key:"isCurrentLinkParent",value:function(e,t){return e.el===this.view.children[t].el&&e.el.getAttribute("data-has-links")&&!e.el.getAttribute("data-is-scene")}},{key:"onSelectFirstSlideLink",value:function(){var e=this.view.el.querySelector("ul > li > div:not(.is-scene)");if(null!=e){var t=e.getAttribute("data-ref");this.selectLink(e,t)}}},{key:"onSlideChanged",value:function(e){if(this.addSlideToVisited(e),!this.currentItem||zl(this.currentItem,"listitem").getAttribute("data-ref")!=e.absoluteId){var t=e.absoluteId,n=this.view.children[t];if(null==n){var i=e.getSlideDraw();null!=i&&(t=i.absoluteId,n=this.view.children[t])}null!=n&&this.selectLink(n.el,t)}}},{key:"selectLink",value:function(e,t){var n=this;this.onClickLink(e,!0),Ul(e,(function(i){i.classList.contains("item-collapsed")&&(n.isCurrentLinkParent(e,t)||(Ql(i,"item-collapsed"),i.setAttribute("aria-expanded",!0)))}),(function(e){return n.el===e}))}},{key:"collapsibleParents",value:function(e){var t=this;return Ul(e,(function(e){return e.classList.contains("item-collapsible")}),(function(e){return t.el===e}))}},{key:"collapseLastItem",value:function(e){var t=this;null!=this.lastExpandedEls&&this.menuOptions.autocollapse&&this.lastExpandedEls.forEach((function(n){n.contains(e)||Kl(n,"item-collapsed")||t.toggleScene(n)}))}},{key:"toggleScene",value:function(e){var t,n=Kl(e,"item-collapsed");if(t=n?"remove":"add",e.classList[t]("item-collapsed"),e.setAttribute("aria-expanded",n),Dl.theme.isUnified){var i=Array.from(e.querySelectorAll("div.listitem")),r=i[0];!function(e,t){if(!n){var i=e.slice(1).filter((function(e){return!Kl(e,"is-promoted-slide")&&!Kl(e,"is-scene")}));i.some((function(e){return Kl(e,"cs-viewed")}))&&Ql(t,"cs-unvisited"),i.every((function(e){return Kl(e,"cs-viewed")}))&&Zl(t,"cs-complete")}}(i,r),this.updateAriaLabel(r)}Il.trigger(Rl.menuLinksListItem.TOGGLE)}},{key:"updateAriaLabel",value:function(e){var t=[].slice.call(e.querySelector(".outline-states").children),n=e.getAttribute("data-slide-title"),i=e.children[0];t.some((function(e){if("none"!==window.getComputedStyle(e).display){var t=e.getAttribute("aria-label");return i.textContent=n+" "+t,!0}}))||(i.textContent=n)}},{key:"onCarrotClick",value:function(e){if(null!==e){var t=zl(e,"item-collapsible");this.toggleScene(t)}}},{key:"onClickLink",value:function(e,t){var n=this;if(e)if(Dl.theme.isUnified){var i=e.getAttribute("data-ref"),r=null!=i;if("restricted"!==this.menuOptions.flow||t||this.visitedSlides.has(i)){if(null!=this.currentItem&&e!==this.currentItem&&(this.currentItem.tabIndex=-1,Ql(this.currentItem,"hover")),this.currentItem=e,null!=i&&(this.collapseLastItem(e),this.lastExpandedEls=this.collapsibleParents(e),this.lastExpandedEls.forEach((function(e){var t=e.firstElementChild;i.includes(t.getAttribute("data-ref"))&&(Zl(t,"cs-viewed"),n.updateAriaLabel(t))}))),Kl(e,"listitem")){this.updateViewedState(e);var o=this.el.querySelector(".cs-selected");Kl(e,"is-scene")||(null!=o&&(Ql(o,"cs-selected"),this.updateAriaLabel(o)),Zl(e,"cs-selected"),window.requestAnimationFrame((function(){return n.updateAriaLabel(e)})))}else this.onCarrotClick(e);if(r&&!t){var a=Hl.getCurrentWindowSlide().absoluteId;i!=a&&(Il.trigger(Rl.request.NEXT_SLIDE,i),Dl.theme.isUnified&&Il.trigger(Rl.topEllipsesPanel.HIDE)),Dl.deviceView.isMobile&&(Il.trigger(Rl.tab.HIDE),(Dl.deviceView.isPhone||Dl.theme.isClassic)&&Il.trigger(Rl.sidebar.CLOSE))}null!=this.currentItem&&null!=this.focusor&&Fl(this.currentItem)}}else this.onClickLinkOld(e,t)}},{key:"onClickLinkOld",value:function(e,t){var n=e.getAttribute("data-ref"),i=null!=n,r=rt(n);if("UL"!==e.nodeName)if("restricted"!==this.menuOptions.flow||t||this.visitedSlides.has(n)){if(null!=this.currentItem&&e!==this.currentItem&&(this.currentItem.tabIndex=-1,Ql(this.currentItem,"hover")),this.currentItem=e,(r||Kl(e.parentNode,"item-collapsible"))&&this.toggleScene(e.parentNode),null!=n&&(this.collapseLastItem(e),this.lastExpandedEls=this.collapsibleParents(e),Array.from(this.lastExpandedEls).forEach((function(e){var t=e.firstElementChild;n.includes(t.getAttribute("data-ref"))&&Zl(t,"cs-viewed")}))),Kl(e,"listitem")){Zl(e,"cs-viewed");var o=this.el.querySelector(".cs-selected");null!=o&&Ql(o,"cs-selected"),Zl(e,"cs-selected")}else this.onCarrotClick(e);if(i&&!t){var a=Hl.getCurrentWindowSlide().absoluteId;r||n==a||(Il.trigger(Rl.request.NEXT_SLIDE,n),Dl.theme.isUnified&&Il.trigger(Rl.topEllipsesPanel.HIDE)),Dl.deviceView.isMobile&&(Il.trigger(Rl.tab.HIDE),(Dl.deviceView.isPhone||Dl.theme.isClassic)&&Il.trigger(Rl.sidebar.CLOSE))}null!=this.currentItem&&null!=this.focusor&&Fl(this.currentItem)}else Kl(e,"carrot")&&this.onCarrotClick(e)}},{key:"updateViewedState",value:function(e){Zl(e,"cs-viewed");var t=Gl(e).pop();if(!Kl(e,"is-scene")&&null!=t){var n=t.querySelector("div.listitem");Ql(n,"cs-unvisited")}this.updateAriaLabel(e)}},{key:"isExpanded",value:function(){return!!this.currentItem.dataset.hasLinks&&Kl(this.parentElement,"item-collapsed")}},{key:"onKeydown",value:function(e){var t=this.currentItem;if(Bl.isActionKey(e.which))this.activateItem(),Bl.isSpaceKey(e.which)&&e.preventDefault();else if(Bl.isRightKey(e.which)&&t.dataset.hasLinks)Kl(this.currentItem.parentNode,"item-collapsed")?this.onCarrotClick(this.currentItem.firstElementChild):this.currentItem=this.getNextItem(this.getItemContent());else if(Bl.isDownKey(e.which))this.currentItem=this.getNextItem(this.getItemContent());else if(Bl.isUpKey(e.which))this.currentItem=this.getPrevItem(this.getItemContent());else if(Bl.isLeftKey(e.which))if(t.dataset.hasLinks&&!Kl(t.parentNode,"item-collapsed"))this.onCarrotClick(this.currentItem.firstElementChild);else{var n=zl(this.currentItem,"item-collapsible");null!=n&&(this.currentItem=n.querySelector(".cs-listitem"))}else Bl.isHomeKey(e.which)?this.currentItem=this.getFirstItem():Bl.isEndKey(e.which)&&(this.currentItem=this.getLastItem());t!==this.currentItem&&(e.preventDefault(),Ql(t,"hover"),t.parentNode.tabIndex=-1,this.focusOnCurrent())}},{key:"focusOnCurrent",value:function(){this.centerOnFocused(),document.activeElement!==this.currentItem.parentNode&&(this.currentItem.parentNode.tabIndex=0,Zl(this.currentItem,"hover"),this.currentItem.parentNode.focus()),Fl(this.currentItem)}},{key:"hasListItems",value:function(){return!Al.isEmpty(Q.model.frame.navData.outline.links)}},{key:"getItems",value:function(){return this.links=this.links||wl(this.el.querySelectorAll(".cs-listitem")),this.links}},{key:"activateItem",value:function(){var e="locked"!==this.menuOptions.flow&&null==this.currentItem.dataset.isScene,t=e?"onClickLink":"onCarrotClick",n=e?this.currentItem:this.currentItem.querySelector(".carrot");this[t](n)}},{key:"findIndexCb",value:function(e,t){return e===t}},{key:"getItemContent",value:function(){return this.currentItem}},{key:"getTreeRootEl",value:function(){return this.el.querySelector("ul")}},{key:"getOffsetEl",value:function(){return Dl.deviceView.isMobile?this.el.parentNode:this.el}},{key:"getOffsetTop",value:function(e){return Dl.deviceView.isMobile?Al.first(Ul(e,(function(e){return"li"===e.nodeName.toLowerCase()}))).offsetTop:Cl(xl(o.prototype),"getOffsetTop",this).call(this,e)}},{key:"onShowTabItem",value:function(e,t){if("outline"===e&&null!=t){var n=this.getItems().find((function(e){return e.dataset.translationId===t}));null!=n&&n.scrollIntoView({block:"nearest",behavior:"smooth"})}}},{key:"scrollToCurrent",value:function(){var e=this;Dl.deviceView.isMobile&&(this.currentItem=this.getItems().find((function(t){return t.getAttribute("data-ref")===e.currentSlideId}))),Cl(xl(o.prototype),"scrollToCurrent",this).call(this)}},{key:"setDrawSlides",value:function(){var e=this;this.view.draws.forEach((function(t){var n=t.link,i=t.links,r=Ml.resolvePath(Nl(n.drawid)),o=function(){var t=n.spliceNum||1,o=r.slides();i.splice.apply(i,[n.index,t].concat(wl(o.map(e.createNewLink)))),n.spliceNum=o.length,e.view.updateHtml(),e.view.initChildRefs()};r.on(Rl.draw.RESET_COMPLETE,o),null!=r.slides()&&o()}))}},{key:"createNewLink",value:function(e){var t={kind:"slidelink",expand:!1,type:"slide"};return t.slideid=e.absoluteId,t.slidetitle=t.displaytext=e.get("title"),t}}],n&&kl(t.prototype,n),i&&kl(t,i),Object.defineProperty(t,"prototype",{writable:!1}),o}(Da),Xl=Yl,$l="rtl",Jl=function(){return Q.model.rtl?$l:""},es=function(e){Q.model.rtl&&!e.contains($l)?e.add($l):!Q.model.rtl&&e.contains($l)&&e.remove($l)},ts=DS.detection,ns="outline",is=function(e){for(var t="";e;)t=e.index+1+"."+t,e=null!=e.parent&&"slidelink"===e.parent.kind&&e.parent;return t+" "},rs=function(){var e=de.getColor(DS.constants.refs.FRAME,".cs-brandhighlight-bg","background-color",".cs-base.cs-custom-theme");if(null==Pl||jl!==!!Q.model.rtl||Tl!==e){var t=de.getColor(DS.constants.refs.FRAME,".cs-tabs.cs-selected","color");Pl=".item-collapsible:not(.item-collapsed) .is-scene {\n      color: ".concat(t," !important;\n    }\n  \n    .is-scene:not(.item-collapsed) .cs-icon-carrot * {\n      fill: ").concat(t," !important;\n    }"),jl=!!Q.model.rtl,Tl=e;var n=window.btoa("\n      <svg xmlns='http://www.w3.org/2000/svg' version='1.1'\n            width='6px' height='40px' viewBox='0 0 6 40' preserveAspectRatio='none'>\n        <g stroke='none' fill='".concat(Tl,"'>\n          <polygon points='0 0 6 0 6 20 6 40 0 40'></polygon>\n        </g>\n      </svg>"));Pl+="\n      .cs-listitem.cs-selected {\n        background-position: ".concat(jl?"right":"left",' top !important;\n        background-repeat: no-repeat !important;\n        background-size: 6px 100% !important;\n        background-image: url("data:image/svg+xml;base64,').concat(n,'") !important;    \n      }')}return Pl},os=Q.def(ns,Xl,(function(){var e=Q.model;return{tag:"nav",attrs:{id:ns+"-content",class:"is-scrollable cs-outline ".concat(Jl()),tabindex:-1,"aria-label":e.getString("outline-container")},y:function(){return null!=this.parent.children.search?this.parent.children.search.h+bi:bi},w:"100%",h:function(){return this.parent.h-(null!=this.parent.children.search?this.parent.children.search.h+bi:bi)},overflow:"auto",draws:[],html:function(){var t=this,n={},i=0;DS.flagManager.multiLangSupport&&this.el.querySelectorAll("[data-collapsible-id]").forEach((function(e){var t=e.dataset.collapsibleId;n[t]=!e.classList.contains("item-collapsed")}));var r=e.frame.navData.outline.links||[],o=e.frame.controlOptions.menuOptions,a=o.wrapListItems,l=o.autonumber,s=o.tooltips,c=o.flow;this.el.depth=0;var u=this.el;return u.innerHTML="\n      <style id='sceneStyle'>\n        ".concat(rs(),"\n      </style>\n      "),function r(o,u,f){var d=document.createElement("ul");d.depth=u.depth+1,d.tabIndex=-1,null!=f?d.setAttribute("role","group"):(d.setAttribute("aria-label",e.getString("outline")),d.setAttribute("role","tree")),u.appendChild(d);for(var h=0;h<o.length;h++){var p=document.createElement("li"),y=o[h];y.parent=f,y.index=h,p.depth=d.depth,p.tabIndex=1===p.depth&&0===h?0:-1,p.setAttribute("role","treeitem"),d.appendChild(p);var b=null!=y.links,v=15*p.depth+19,g=y.slideid;null==g&&t.draws.push({link:y,links:o,i:h});var m=rt(g),w=e.rtl,S="free"===c||b&&y.expand?"":e.getString("acc_locked"),k=l?"".concat(is(y)," "):"",C=DS.flagManager.aiCourseTranslation&&o[h].translationId,O='\n              <div \n                class="cs-listitem listitem '.concat(m?"is-scene cs-unvisited":""," ").concat(a?"":"no-wrap"," ").concat("free"!==c&&"cs-"+c," ").concat(!m&&b?"is-promoted-slide":"",'"\n                style="padding-').concat(w?"right":"left",": ").concat(v,'px;"\n                data-ref="').concat(g,'"\n                ').concat(C?'data-translation-id="'.concat(C,'"'):"",'\n                data-slide-title="').concat(k).concat(o[h].displaytext,'"\n                ').concat(m?'data-is-scene="true"':"","\n                ").concat(b?'data-has-links="true"':"",'\n                title="').concat(s?y.displaytext:"",'"\n                tabIndex="-1"\n                role = "none">\n                <span style="position: absolute; opacity: 0;">').concat(k).concat(y.displaytext," ").concat(S,"</span>\n\n                ").concat(b?Qn("carrot")("".concat(w?-1:1,"px")):"",'\n                <span class="linkText" aria-hidden="true">\n                  ').concat(k).concat(y.displaytext,'\n                </span>\n                <span class="outline-states" aria-hidden="true">\n                  <span class="visitedIcon" aria-label="').concat(e.getString("acc_visited"),'">\n                    ').concat(Qn("checkmark")(),'\n                  </span>\n                  <span class="lockedIcon" aria-label="').concat(e.getString("acc_locked"),'">\n                    ').concat(Qn("lock")(),'\n                  </span>\n                  <span class="lockedViewedIcon" aria-label="').concat(e.getString("acc_visited"),", ").concat(e.getString("acc_locked"),'">\n                    ').concat(Qn("lockedViewed")(),"\n                  </span>\n                </span>\n              </div>\n            ");p.innerHTML=O,b&&(DS.flagManager.multiLangSupport&&p.setAttribute("data-collapsible-id",i),p.setAttribute("aria-expanded",y.expand),p.classList.add("item-collapsible"),null!=n[i]?n[i]||p.classList.add("item-collapsed"):y.expand||p.classList.add("item-collapsed"),r(y.links,p,y),i++)}}(r,u),u.innerHTML},updateHook:function(){var e=document.getElementById("sceneStyle");if(ts.browser.isIE){var t=document.createElement("style");t.setAttribute("id","dynamicStyle"),t.setAttribute("type","text/css"),t.cssText=rs(),document.head.querySelector("#dynamicStyle")||document.head.appendChild(t)}else null!=e&&(e.innerText=rs());es(this.el.classList)}}}));function as(e){return function(e){if(Array.isArray(e))return ls(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"==typeof e)return ls(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return ls(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ls(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,i=new Array(t);n<t;n++)i[n]=e[n];return i}var ss="outlineSearch",cs=Q.def(ss,Eo,(function(){var e=Q.model,t=e.frame,n=e.outlineInSidebar,i=DS.presentation,r=t.controlOptions.controls.search||i.isPreview()&&!window.globals.HAS_SLIDE;return{attrs:{id:"".concat(ss,"-content"),class:"outline-search ".concat(Jl()),tabindex:-1},w:function(){var e=this.parent.childWidth||this.parent.w;return e-2},h:function(){var e=this.parent.h;return e-2},updateHook:function(){es(this.el.classList)},overflow:n?"hidden":"visible",model:t,childViews:[].concat(as(r?[ca,gl]:[]),[os])}}));function us(e){return us="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},us(e)}function fs(e){return function(e){if(Array.isArray(e))return ds(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"==typeof e)return ds(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return ds(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ds(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,i=new Array(t);n<t;n++)i[n]=e[n];return i}function hs(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,gs(i.key),i)}}function ps(e,t){return ps=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},ps(e,t)}function ys(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,i=vs(e);if(t){var r=vs(this).constructor;n=Reflect.construct(i,arguments,r)}else n=i.apply(this,arguments);return function(e,t){if(t&&("object"===us(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return bs(e)}(this,n)}}function bs(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function vs(e){return vs=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},vs(e)}function gs(e){var t=function(e,t){if("object"!==us(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var i=n.call(e,t||"default");if("object"!==us(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===us(t)?t:String(t)}var ms=DS,ws=(ms._,ms.focusManager),Ss=ms.detection,ks=ms.pubSub,Cs=ms.events,Os=ms.dom,Es=Os.addClass,Ls=Os.removeClass,xs=(ms.keyManager.isTabKey,function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&ps(e,t)}(o,e);var t,n,i,r=ys(o);function o(e){var t,n,i,a;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,o),t=r.call(this,e),n=bs(t),a=void 0,(i=gs(i="s"))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a,t.view.isUnified&&(t.getScrollEl=function(){return t.termsEl},t.getOffsetTop=function(e){return e.offsetTop},t.getOffsetHeight=function(e){return e.offsetHeight+e.nextElementSibling.clientHeight}),t.hasDefinition=Ss.deviceView.isDesktop&&!t.view.isUnified,ks.on(Cs.glossary.REFRESH_VIEW,(function(){t.view.updateHtml(),t.view.update()})),DS.flagManager.aiCourseTranslation&&ks.on(Cs.frame.SHOW_TAB_ITEM,t.onShowTabItem.bind(bs(t))),t}return t=o,(n=[{key:"onClickItem",value:function(e){var t=e.target;if(this.hasDefinition&&null!=t&&"p"!==t.nodeName.toLowerCase()){var n=null!=this.activeItem;n&&(this.activeItem.firstElementChild.setAttribute("aria-expanded",!1),this.activeItem.style.backgroundColor="",this.closeItem(this.activeItem)),n&&this.lastSelected==t||(t.setAttribute("aria-expanded",!0),this.openItem(t.parentElement)),this.lastSelected=t}}},{key:"getNextItem",value:function(e){var t,n=this.getItems(),i=this.getItems().indexOf(this.currentItem);do{(i+=1)===n.length&&(i=0),t=n[i]}while("none"===t.parentElement.style.display);return t}},{key:"getPrevItem",value:function(e){var t,n=this.getItems(),i=this.getItems().indexOf(this.currentItem);do{-1==(i-=1)&&(i=n.length-1),t=n[i]}while("none"===t.parentElement.style.display);return t}},{key:"hasListItems",value:function(){return!DS._.isEmpty(this.model.frame.glossaryData)}},{key:"getItems",value:function(){return this.links=this.links||fs(this.el.querySelectorAll(".term")),this.links}},{key:"openItem",value:function(e){Es(e,"cs-selected"),e.nextElementSibling.style.display="block",Es(e.nextElementSibling,"open"),this.activeItem=e}},{key:"closeItem",value:function(e){var t=this;Ls(e,"cs-selected"),Ls(e.nextElementSibling,"open"),this.activeItem=null,TweenLite.to(e,.2,{opacity:1,onComplete:function(){e.nextElementSibling.style.display="none",ws.setFocusRectOn(t.currentItem)}})}},{key:"onShowTabItem",value:function(e,t){if("glossary"===e&&null!=t){var n=fs(this.el.querySelectorAll("[data-translation-id]")).find((function(e){return e.dataset.translationId===t}));null!=n&&n.scrollIntoView({block:"nearest",behavior:"smooth"})}}}])&&hs(t.prototype,n),i&&hs(t,i),Object.defineProperty(t,"prototype",{writable:!1}),o}(Da));function Ps(e){return Ps="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Ps(e)}function Ts(e){return function(e){if(Array.isArray(e))return js(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"==typeof e)return js(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return js(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function js(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,i=new Array(t);n<t;n++)i[n]=e[n];return i}function _s(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,(r=i.key,o=void 0,o=function(e,t){if("object"!==Ps(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var i=n.call(e,t||"default");if("object"!==Ps(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(r,"string"),"symbol"===Ps(o)?o:String(o)),i)}var r,o}function Ds(e,t){return Ds=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},Ds(e,t)}function Is(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,i=Rs(e);if(t){var r=Rs(this).constructor;n=Reflect.construct(i,arguments,r)}else n=i.apply(this,arguments);return function(e,t){if(t&&("object"===Ps(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return As(e)}(this,n)}}function As(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Rs(e){return Rs=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},Rs(e)}var Bs=DS,Ms=(Bs.detection,Bs.pubSub),Hs=Bs.events,Ns=Bs.keyManager.isSpaceKey,Fs=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Ds(e,t)}(o,e);var t,n,i,r=Is(o);function o(e){var t;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,o),t=r.call(this,e),Ms.on(Hs.resources.REFRESH_VIEW,(function(){t.view.updateHtml(),t.view.update()})),DS.flagManager.aiCourseTranslation&&Ms.on(Hs.frame.SHOW_TAB_ITEM,t.onShowTabItem.bind(As(t))),t}return t=o,(n=[{key:"onClickItem",value:function(){}},{key:"hasListItems",value:function(){return(this.model.resourceData.resources||[]).length>0}},{key:"getItems",value:function(){return this.items=this.items||Ts(this.el.querySelectorAll(".resource a")),this.items}},{key:"activateItem",value:function(e){Ns(e.keyCode)&&DS.windowOpen.open({url:this.currentItem.dataset.url})}},{key:"getOffsetTop",value:function(e){return this.currentItem.offsetTop}},{key:"getFocusRectTarget",value:function(){return this.currentItem.parentNode}},{key:"onShowTabItem",value:function(e,t){if("resource"===e&&null!=t){var n=this.getItems().find((function(e){return e.dataset.translationId===t}));null!=n&&n.scrollIntoView({block:"nearest",behavior:"smooth"})}}}])&&_s(t.prototype,n),i&&_s(t,i),Object.defineProperty(t,"prototype",{writable:!1}),o}(xs);function Vs(e){return function(e){if(Array.isArray(e))return Ws(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"==typeof e)return Ws(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Ws(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Ws(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,i=new Array(t);n<t;n++)i[n]=e[n];return i}var Us=DS.utils,zs=Us.pxify,Ks=Us.getPath,Zs="resources",Qs=["xls","pdf","doc","ppt","rtf","zip","link","file"],Gs={docx:"doc",pptx:"ppt",xlsx:"xls"},qs=Q.def(Zs,Fs,(function(e){var t=Q.model.frame,n=de.getColor(e,".cs-brandhighlight-bg","background-color",".cs-base.cs-custom-theme").replace(/\).*/,")");return{attrs:{id:"".concat(Zs,"-content"),class:"cs-resource ".concat(Jl()," resources"),tabindex:-1},w:function(){return this.parent.childWidth||this.parent.w},h:function(){return this.parent.h-10},overflow:"",model:t,html:function(){return'\n      <div class="resources-content scrolling-panel panel-content">\n        <ul class="resources-list" role="menu">\n          '.concat(Ks(t,"resourceData.resources",[]).reduce((function(e,t,i){var r=function(e){var t=e.url,n=t.includes("http")?"link":t.split(".").pop().toLowerCase();return Gs[n]&&(n=Gs[n]),Qs.includes(n)?n:"file"}(t),o="href=\"javascript:DS.windowOpen.open({ url: '".concat(t.url,"' })\""),a=DS.flagManager.aiCourseTranslation&&t.translationId;return"".concat(e,'\n              <li class="cs-listitem resource" tabIndex="-1">\n                <a ').concat(o,' \n                  data-url="').concat(t.url,'" \n                  tabindex="').concat(0===i?0:-1,'" \n                  role="menuitem" \n                  ').concat(a?'data-translation-id="'.concat(a,'"'):"",'\n                >\n                  <div class="file-icon resource-ext-').concat(r,'">\n                    ').concat(Qn("link"===r?"link":"file")(n),'\n                    <div class="file-icon-text">\n                    ').concat(r,'\n                    </div>\n                  </div>\n                  <div class="file-name">').concat(t.title,"</div>\n                </a>\n              </li>")}),""),"\n        </ul>\n      </div>")},updateHook:function(){es(this.el.classList)},methods:{onPanelVisible:function(){Vs(this.el.querySelectorAll(".resource")).forEach((function(e){var t=e.querySelector(".file-name").scrollHeight;e.clientHeight<t&&(e.style.height=zs(t+8))}))}}}})),Ys=DS,Xs=Ys.slideObjectUtils,$s=Ys.utils.getPath,Js=Ys.constants,ec="glossary",tc=Q.def(ec,xs,(function(){var e=Q.model;return Xs.activeMobileMenuItem(e.frame.glossaryData,"no-glossary"),{attrs:{id:"".concat(ec,"-content"),class:"".concat(Jl()," cs-glossary"),tabindex:-1},w:"100%",h:function(){return this.parent.h-bi+2},model:e,isUnified:!0,html:function(){return'\n      <div data-ref="terms" class="glossary-content scrolling-panel">\n        <dl>\n        '.concat($s(e,"frame.glossaryData",[]).map((function(e,t){var n="glossary-def-".concat(t),i=DS.flagManager.aiCourseTranslation&&e.translationId;return'\n              <dt \n                class="cs-heading glossary-item glossary-term term"  \n                tabindex="'.concat(0===t?0:-1,'" \n                aria-describedby="').concat(n,'"\n                ').concat(i?'data-translation-id="'.concat(i,'.Term"'):"","\n              >\n                ").concat(e.title,'\n              </dt>\n              <dd \n                id="').concat(n,'" \n                class="glossary-item glossary-desc" \n                tabindex="-1"\n                ').concat(i?'data-translation-id="'.concat(i,'.Definition"'):"","\n              >\n                ").concat(e.content.toString().split(Js.LINE_BREAK_REGEX).map((function(e){return"<p class=".concat(0===e.length?"empty-p":"",">").concat(e,"</p>")})).join(""),"\n              </dd>\n            ")})).join(""),"\n        </dl>\n      </div>")},updateHook:function(){es(this.el.classList)}}}));function nc(e){return nc="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},nc(e)}function ic(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,(r=i.key,o=void 0,o=function(e,t){if("object"!==nc(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var i=n.call(e,t||"default");if("object"!==nc(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(r,"string"),"symbol"===nc(o)?o:String(o)),i)}var r,o}function rc(e,t){return rc=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},rc(e,t)}function oc(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,i=lc(e);if(t){var r=lc(this).constructor;n=Reflect.construct(i,arguments,r)}else n=i.apply(this,arguments);return function(e,t){if(t&&("object"===nc(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return ac(e)}(this,n)}}function ac(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function lc(e){return lc=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},lc(e)}var sc=/color:#ffffff/gi,cc=DS,uc=cc.detection,fc=cc.events,dc=cc.focusManager,hc=cc.pubSub,pc=(cc.utils,cc.slideObjectUtils),yc=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&rc(e,t)}(o,e);var t,n,i,r=oc(o);function o(e){var t;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,o),t=r.call(this,e),hc.on(fc.window.MAIN_CHANGED,t.onSlideChanged.bind(ac(t))),(DS.flagManager.multiLangSupport||DS.flagManager.aiCourseTranslation)&&hc.on(fc.transcript.REFRESH_VIEW,t.onRefreshView.bind(ac(t))),uc.deviceView.isClassicMobile?hc.on(fc.mobile.NOTES_SHOWN,t.onHamburgerToggle.bind(ac(t),!1)):uc.deviceView.isUnifiedMobile&&hc.on(fc.hamburger.TOGGLE,t.onHamburgerToggle.bind(ac(t))),t.forceScrollToTop=!1,t}return t=o,(n=[{key:"onHamburgerToggle",value:function(e){this.forceScrollToTop&&!e&&(this.getScrollElement().scrollTop=0,this.forceScrollToTop=!1)}},{key:"onFocus",value:function(){this.el.focus(),dc.setFocusRectOn(this.el.parentNode)}},{key:"focusSelf",value:function(){this.onFocus()}},{key:"handleTab",value:function(){return this.el.parentElement.focus(),!0}},{key:"onSlideChanged",value:function(e){var t=(this.model.notesData||[]).find((function(t){return e.absoluteId.includes(t.slideId)}));null!=this.titleEl&&(this.titleEl.innerHTML=e.get("title"));var n=null==t?"":t.content;uc.deviceView.isClassicMobile&&(n=n.replace(sc,"color:#515557")),this.contentEl.innerHTML=n,hc.trigger(fc.transcript.CHANGED),pc.activeMobileMenuItem(n,"no-transcript"),this.scrollToTop()}},{key:"onRefreshView",value:function(){var e=DS.windowManager.getMainWindowSlide();null!=e&&this.onSlideChanged(e)}},{key:"scrollToTop",value:function(){uc.deviceView.isDesktop?this.view.el.scrollTop=0:uc.theme.isClassic?0===this.getScrollElement().scrollTop?this.forceScrollToTop=!0:this.getScrollElement().scrollTop=0:Q.getNamespace(this.view.nameSpace).sidebar.collapsed?this.forceScrollToTop=!0:this.getScrollElement().scrollTop=0}},{key:"getScrollElement",value:function(){return uc.deviceView.isClassicMobile?this.view.parent.el:this.view.el}},{key:"getViewBox",value:function(){return this.view.parent.getBox()}},{key:"teardown",value:function(){hc.off(fc.window.MAIN_CHANGED,this.onSlideChanged.bind(this)),DS.flagManager.multiLangSupport&&hc.off(fc.transcript.REFRESH_VIEW,this.onRefreshView.bind(this)),uc.deviceView.isClassicMobile?hc.off(fc.mobile.NOTES_SHOWN,this.onHamburgerToggle.bind(this,!1)):uc.deviceView.isUnifiedMobile&&hc.off(fc.hamburger.TOGGLE,this.onHamburgerToggle.bind(this))}}])&&ic(t.prototype,n),i&&ic(t,i),Object.defineProperty(t,"prototype",{writable:!1}),o}(_t),bc="transcript",vc={outline:cs,resources:qs,glossary:tc,transcript:Q.def(bc,yc,(function(){var e=Q.model,t=e.rtl?"rtl":"";return{attrs:{id:bc+"-content",class:"cs-transcript ".concat(t),tabindex:0},w:"100%",h:function(){return this.parent.h-20},html:'\n      <div data-ref="content" class="note-content scrolling-panel"></div>\n    ',model:e.frame}}))},gc=DS.utils.getPath,mc=DS.constants.refs.FRAME,wc="sidebarPanels",Sc=(Q.def(wc,(function(e){var t=Q.getNamespace(e),n=t.sidebar,i=t.tabs,r=t.logo;return{attrs:{id:wc},x:0,z:1,y:function(){return i.bottom()},w:function(){return n.data.actualWidth()},h:function(){var e=r?r.h+bi:0;return n.h-i.h-e},childDef:function(){Sc(Q.model,e)}}})),function(e,t){gc(e,"sidebarTabs",[]).forEach((function(e,n){!function(e,t,n,i){var r=t.name,o=r+"Panel",a=0===n,l=Q.def(o,vo,{attrs:{id:o,class:"cs-menu cs-panel is-scrollable panel","aria-labelledby":r+"-tab",role:"tabpanel",tabindex:-1},visibility:"no-reflow",style:{display:a},x:0,y:0,w:"100%",h:"100%",html:""});l.init(),Q.getNamespace(mc).sidebarPanels.append(l);var s=vc[r];null!=s&&(s.nameSpace=i,s.init(),l.append(s))}(0,e,n,t)}))}),kc="topTabs";function Cc(e){return Cc="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Cc(e)}function Oc(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,(r=i.key,o=void 0,o=function(e,t){if("object"!==Cc(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var i=n.call(e,t||"default");if("object"!==Cc(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(r,"string"),"symbol"===Cc(o)?o:String(o)),i)}var r,o}function Ec(e,t){return Ec=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},Ec(e,t)}function Lc(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,i=xc(e);if(t){var r=xc(this).constructor;n=Reflect.construct(i,arguments,r)}else n=i.apply(this,arguments);return function(e,t){if(t&&("object"===Cc(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,n)}}function xc(e){return xc=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},xc(e)}Q.def(kc,(function(){return{attrs:{id:kc,role:"navigation"},w:"100%",overflow:"visible",h:"100%"}}));var Pc=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Ec(e,t)}(o,e);var t,n,i,r=Lc(o);function o(e){var t;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,o),(t=r.call(this,e)).onClick(t.onClickLink),t}return t=o,(n=[{key:"onClickLink",value:function(){var e=this,t=this.model.properties.data;DS.pubSub.trigger(DS.events.customlink.EVENT,t),this.view.parent.children.forEach((function(t){t.viewLogic!==e&&t.viewLogic.showing&&t.viewLogic.hidePanel()}))}},{key:"isLive",value:function(){return!0}}])&&Oc(t.prototype,n),i&&Oc(t,i),Object.defineProperty(t,"prototype",{writable:!1}),o}(_t);function Tc(e){return Tc="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Tc(e)}function jc(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,(r=i.key,o=void 0,o=function(e,t){if("object"!==Tc(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var i=n.call(e,t||"default");if("object"!==Tc(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(r,"string"),"symbol"===Tc(o)?o:String(o)),i)}var r,o}function _c(){return _c="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(e,t,n){var i=function(e,t){for(;!Object.prototype.hasOwnProperty.call(e,t)&&null!==(e=Rc(e)););return e}(e,t);if(i){var r=Object.getOwnPropertyDescriptor(i,t);return r.get?r.get.call(arguments.length<3?e:n):r.value}},_c.apply(this,arguments)}function Dc(e,t){return Dc=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},Dc(e,t)}function Ic(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,i=Rc(e);if(t){var r=Rc(this).constructor;n=Reflect.construct(i,arguments,r)}else n=i.apply(this,arguments);return function(e,t){if(t&&("object"===Tc(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return Ac(e)}(this,n)}}function Ac(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Rc(e){return Rc=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},Rc(e)}var Bc=DS.dom.isWithin,Mc=DS.globalEventHelper,Hc=Mc.addDocumentListener,Nc=Mc.removeDocumentListener,Fc=DS,Vc=Fc.pubSub,Wc=Fc.events,Uc=Fc._,zc=Fc.playerGlobals,Kc=Fc.dom,Zc=Kc.addClass,Qc=Kc.removeClass,Gc=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Dc(e,t)}(o,e);var t,n,i,r=Ic(o);function o(e){var t;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,o),t=r.call(this,e),Uc.bindAll(Ac(t),"hidePanel","onCheckShouldHide"),t.onClick(t.onClickLink),t.showing=!1,Vc.on(Wc.sidebar.ACTIVE_TAB_SET,t.onActiveTabSet.bind(Ac(t))),DS.flagManager.aiCourseTranslation&&Vc.on(Wc.frame.SHOW_TAB_ITEM,t.onShowTabItem.bind(Ac(t))),t}return t=o,n=[{key:"onShowTabItem",value:function(e){this.hidePanel(),this.view.nameKey.includes(e)&&this.showPanel(!0)}},{key:"onActiveTabSet",value:function(e){this.view.parent.children.find((function(t){return t.nameKey.includes(e)}))&&this.hidePanel(),this.view.nameKey.includes(e)&&this.showPanel()}},{key:"onClickLink",value:function(e){this.showing?this.hidePanel():this.showPanel()}},{key:"showPanel",value:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.translationShown=e,this.el.style.border="1px solid ".concat(this.view.selectedBorderColor),this.el.setAttribute("aria-expanded",!0),Zc(this.el,"cs-selected"),Zc(this.el,"active"),this.view.panel.el.style.display="block",this.view.panel.viewLogic.focusChild(),Hc("mouseup",this.onCheckShouldHide),Vc.on(Wc.controlLayout.CHANGED,this.hidePanel),this.showing=!0}},{key:"hidePanel",value:function(){this.translationShown=!1,this.el.style.border="1px solid rgba(0, 0, 0, 0)",this.el.setAttribute("aria-expanded",!1),Qc(this.el,"cs-selected"),Qc(this.el,"active"),this.view.panel.el.style.display="none",Nc("mouseup",this.onCheckShouldHide),Vc.off(Wc.controlLayout.CHANGED,this.hidePanel),this.showing=!1}},{key:"onBlur",value:function(e){zc.presentation.isPreview()&&!window.globals.HAS_SLIDE||this.onCheckShouldHide({target:e.relatedTarget}),_c(Rc(o.prototype),"onBlur",this).call(this,e)}},{key:"onCheckShouldHide",value:function(e){null!=e.target&&(Bc(e.target,"panel")||this.el.contains(e.target))||this.hidePanel()}},{key:"isLive",value:function(){var e=Q.model.currControlLayout[this.model.name];return Uc.isObject(e)?e.enabled:e}},{key:"teardown",value:function(){Vc.off(Wc.controlLayout.CHANGED,this.hidePanel),Vc.off(Wc.sidebar.ACTIVE_TAB_SET)}}],n&&jc(t.prototype,n),i&&jc(t,i),Object.defineProperty(t,"prototype",{writable:!1}),o}(_t),qc=Gc;function Yc(e){return Yc="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Yc(e)}function Xc(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,(r=i.key,o=void 0,o=function(e,t){if("object"!==Yc(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var i=n.call(e,t||"default");if("object"!==Yc(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(r,"string"),"symbol"===Yc(o)?o:String(o)),i)}var r,o}function $c(){return $c="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(e,t,n){var i=function(e,t){for(;!Object.prototype.hasOwnProperty.call(e,t)&&null!==(e=nu(e)););return e}(e,t);if(i){var r=Object.getOwnPropertyDescriptor(i,t);return r.get?r.get.call(arguments.length<3?e:n):r.value}},$c.apply(this,arguments)}function Jc(e,t){return Jc=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},Jc(e,t)}function eu(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,i=nu(e);if(t){var r=nu(this).constructor;n=Reflect.construct(i,arguments,r)}else n=i.apply(this,arguments);return function(e,t){if(t&&("object"===Yc(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return tu(e)}(this,n)}}function tu(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function nu(e){return nu=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},nu(e)}var iu=DS,ru=iu._,ou=iu.events,au=iu.pubSub,lu=iu.detection,su=iu.keyManager,cu=iu.TweenLite,uu=iu.focusManager,fu=iu.constants.ANIMATION_DURATION,du=iu.globalEventHelper,hu=du.addDocumentListener,pu=du.addWindowListener,yu=iu.dom,bu=yu.isInput,vu=yu.hasClass,gu=yu.addClass,mu=yu.removeClass,wu=lu.device.isMobile?"touchend":"mouseup",Su=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Jc(e,t)}(o,e);var t,n,i,r=eu(o);function o(e){var t;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,o),t=r.call(this,e),ru.bindAll(tu(t),"updatePanel","onResize"),t.isOutlinePanel="outlineLink"===e.nameKey,t.isGlossaryPanel="glossaryLink"===e.nameKey,t.isResourcesPanel="resourcesLink"===e.nameKey,t.isOutlinePanel&&(au.on(ou.menuLinksListItem.TOGGLE,t.updatePanel),au.on(ou.search.UPDATE_PANEL,(function(){setTimeout(t.updatePanel,fu)})),t.searchEnabled=Q.model.frame.controlOptions.controls.search),window.globals.HAS_SLIDE||t.subscribeToRefreshEvents(),t.removeResize=pu("resize",t.onResize),t}return t=o,(n=[{key:"subscribeToRefreshEvents",value:function(){this.isOutlinePanel?au.on(ou.navData.REFRESH_VIEW,this.updatePanel):this.isResourcesPanel?au.on(ou.resources.REFRESH_VIEW,this.updatePanel):this.isGlossaryPanel&&au.on(ou.glossary.REFRESH_VIEW,this.updatePanel)}},{key:"teardown",value:function(){au.off(ou.resources.REFRESH_VIEW,this.updatePanel),au.off(ou.glossary.REFRESH_VIEW,this.updatePanel),au.off(ou.navData.REFRESH_VIEW,this.updatePanel),this.removeResize()}},{key:"onResize",value:function(){!this.showing||lu.device.isMobile&&bu(document.activeElement)||this.hidePanel()}},{key:"updatePanel",value:function(){var e=this.view.panel.el.querySelector(".scrolling-panel");if(null!=e)this.view.panel.height=Math.min(window.innerHeight-120,e.clientHeight+40),this.view.panel.update(),this.view.panel.updateChildren();else if(this.isOutlinePanel){var t;t=vu(document.body,"search-results-active")?document.body.querySelector(".search-results-active .search-results ul"):document.body.querySelector("#outline-content ul"),this.view.panel.height=Math.min(window.innerHeight-120,t.clientHeight+40+(this.searchEnabled?58:0)),this.view.panel.update(),this.view.panel.updateChildren(!0)}}},{key:"showPanel",value:function(){var e=this;au.trigger(ou.tabLink.SHOW_PANEL,this),lu.deviceView.isPhone&&cu.from(this.view.panel.el,.12,{left:"left"===Q.model.sidebarOpts.sidebarPos?30:-30}),gu(document.body,"nested-panel-shown"),this.el.setAttribute("aria-expanded",!0),this.el.classList.add("cs-selected","active"),this.view.panel.el.style.display="block",this.updatePanel(),this.view.panel.viewLogic.focusChild(),this.removeShowPanelListeners=ru.flow(hu("keydown",(function(t){return e.handleKeyDown(t)})),hu(wu,this.onCheckShouldHide)),au.on(ou.controlLayout.CHANGED,this.hidePanel),this.showing=!0,au.trigger(ou.tabLink.PANEL_SHOWN,this)}},{key:"handleKeyDown",value:function(e){su.isEscapeKey(e.which)&&(this.hidePanel(),this.el.focus(),uu.setFocusRectOn(this.el))}},{key:"hidePanel",value:function(e){au.trigger(ou.tabLink.HIDE_PANEL,this),!e&&lu.deviceView.isPhone&&this.view.panel.inEllipsis||($c(nu(o.prototype),"hidePanel",this).call(this),null!=this.removeShowPanelListeners&&this.removeShowPanelListeners(),mu(document.body,"nested-panel-shown"))}}])&&Xc(t.prototype,n),i&&Xc(t,i),Object.defineProperty(t,"prototype",{writable:!1}),o}(qc);function ku(e){return function(e){if(Array.isArray(e))return Cu(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"==typeof e)return Cu(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Cu(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Cu(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,i=new Array(t);n<t;n++)i[n]=e[n];return i}var Ou,Eu=DS.detection,Lu=DS.constants.refs.FRAME,xu=Eu.deviceView.isPhone?290:302,Pu=DS,Tu=Pu.pubSub,ju=Pu.events,_u=!0,Du=[],Iu=!0;Tu.on(ju.renderTree.DESTROYED,(function(){Du=[],_u=!0}));var Au=function(e){if(e.length>0){var t=e.shift();return Du.unshift(t),t}},Ru=function(e){return e.reduce((function(e,t){return t.update(),e+(t.isTopLink?t.w:0)}),0)};function Bu(e){return function(e){if(Array.isArray(e))return Mu(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"==typeof e)return Mu(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Mu(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Mu(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,i=new Array(t);n<t;n++)i[n]=e[n];return i}!function(e){var t,n=e.id,i=(Q.def(n,(function(i){var o=Q.getNamespace(i),a=o.title,l=o.hamburger,s=o.sidebar,c=o.timer,u=o.topEllipsis;Q.model.topTabsRight.length;return t=n,{attrs:{id:n,tabindex:-1},style:{left:"-1px"},w:function(){var e=this.parent.w-bi;return a.visible&&(e-=a.w),s.visible&&(e-=l.w),null!=c&&c.visible&&(e-=c.w),u.visible&&(e-=u.w),e-vi},overflow:"visible",x:e.x,y:"vertical-center",h:22,z:5,childDef:function(){r(Q.model,i)},methods:e.methods,updateHook:e.updateHook}})),function(n,i,r,o){var a,l,s,c,u="customlink"===r.name;u?(l=Pc,a=DS.flagManager.multiLangSupport&&i.getControlString(r.properties.data)||r.properties.title,s="link".concat(e.dir).concat(o),c="custom-link"):(l=Su,a=i.getString(r.name),s=r.name+"Link",c="panel-link");var f=de.getColor(n,".cs-topmenu-item.active .cs-tab","border-top-color",".cs-base"),d={id:s,class:"cs-topmenu-item cs-tabs top-tab ".concat(c),tabindex:0};u||(d["aria-controls"]="".concat(r.name,"-panel"),d["aria-expanded"]=!1);var h=Q.def(s,l,{selectedBorderColor:f,attrs:d,model:Object.assign(r,{idx:o}),isTopLink:!0,calcTextSize:!0,noUpdate:!0,contentStyle:{},tag:"button",html:function(){return'<span data-ref="label" class="cs-tab top-tab-text">'.concat(a,"</span>")},methods:{updateDomStrings:function(){var e="customlink"===r.name?i.getControlString(r.properties.data):i.getString(r.name);this.viewLogic.labelEl.textContent=e}},wPad:14,z:2,w:"fit-to-text-w",h:"fit-to-text-h",minH:23});h.init(),Q.getNamespace(Lu)[e.id].append(h,!0),u||function(n,i,r,o){var a=o.name+"Panel",l="linksRight"===e.id,s=Q.getNamespace(n),c=(s.topBar,s.frame,s.sidebar),u=s.slide,f=(s.wrapper,s.topEllipsis,de.getColor(n,".cs-topmenu-item.active .cs-panel","border-top-color",".cs-base")),d=Eu.env.isFileProtocol||Eu.browser.isFF?"background-color":"background",h=de.getColor(n,".cs-topmenu-item.active .cs-panel",d),p=Q.getNamespace(n)[o.name+"Link"],y=Q.getNamespace(n),b=y.topEllipsisPanel,v=y.linksRight,g=Q.model.rtl,m=Q.def(a,vo,{attrs:{id:a,class:"cs-topmenu-item topmenu-item active cs-menu cs-panel panel top-tabs-drop topmenu-panel-align-".concat(l?"right":"left"),tabIndex:-1},style:{borderColor:f,background:h},w:xu,h:function(){return this.height||200},z:u.z+1,visible:!1,lnk:i,tabsId:t,overflow:"visible",x:function(){var e=0;if(p.inEllipsis)return!g||Eu.deviceView.isPhone?"left"===c.pos?e=b.maxWidth-xu+8:Eu.deviceView.isPhone||(e=(b.maxWidth-xu)/2+8):"left"!==c.pos&&(e=-2),e;var t=(e=i.x+(i.w-xu)/2)+xu;return!p.inEllipsis&&t>v.w&&(e=v.w-xu-20),e=Math.max(g?20:0,e),this.children.arrow.el.style.left="".concat(i.x-e+i.w/2-11,"px"),e},y:function(){return p.inEllipsis?0:this.lnk.h+10},html:Vi(h,f)});m.init(n),Q.getNamespace(Lu)[e.id].append(m),i.panel=m;var w=vc[o.name];null!=w&&(w.init(n),m.append(w))}(n,h,0,r)}),r=function(e,t){[].concat(ku(e.topTabsLeft),ku(e.topTabsRight)).forEach((function(n,r){i(t,e,n,r)}))}}({id:"linksRight",dir:"Right",linkListName:"topTabsRight",x:function(){var e=Q.getNamespace(DS.constants.refs.FRAME),t=e.hamburger,n=e.title,i=e.sidebar,r=e.topEllipsis,o=0;return Q.model.rtl?(i.visible&&(o+=t.w),r.visible&&(o+=r.w,"right"===i.pos&&(o=r.w))):n.visible?o=n.right():i.visible&&(o=t.w),o+("right"===i.pos?0:vi)},methods:{tabWidths:function(){return Ru(this.children)},phoneUpdateHook:function(){if(0!==this.children.length||0!==Du.length){var e=Q.getNamespace(this.nameSpace),t=e.topEllipsis,n=e.topEllipsisPanel;this.children.forEach((function(e){var i=n.children.links.el;null!=e.panel&&(e.panel.inEllipsis=!0,i.insertBefore(e.panel.el,i.firstChild),e.panel.update()),e.inEllipsis=!0,i.insertBefore(e.el,i.firstChild),t.setVisibility(!0)})),this.updateHook=DS._.noop}}},updateHook:function(){if(DS.detection.deviceView.isPhone)this.phoneUpdateHook();else{var e=this.children;if(Iu||0!==this.children.length||DS.presentation.isPreview()||this.setVisibility(Du.length>0||this.children.some((function(e){return e.visible}))),Iu=!1,(0!==e.length||0!==Du.length)&&null!=this.nameSpace){var t=Q.getNamespace(this.nameSpace),n=t.linksRight,i=t.topEllipsis,r=t.sidebar,o=t.topEllipsisPanel,a=this.w;if(Ou>a&&(_u=!0),Ou=a,_u&&Ru(e)>this.w)for(;Ru(e)>this.w;){var l=Au(this.children);if(null!=l&&l.isTopLink){if(null!=l.el){var s=o.children.links.el;null!=l.panel&&(l.panel.inEllipsis=!0,s.insertBefore(l.panel.el,s.firstChild),l.panel.update()),l.inEllipsis=!0,s.insertBefore(l.el,s.firstChild)}i.setVisibility(!0)}}else if(Du.length>0){var c=Ru(e),u=Du[0];this.w>c+u.w&&(Du.shift(),this.el.appendChild(u.el),null!=u.panel&&(u.panel.inEllipsis=!1,u.panel.update(),this.el.appendChild(u.panel.el)),this.children.unshift(u),u.inEllipsis=!1)}0===Du.length&&i.visible&&i.setVisibility(!1),_u=!1;var f={toLeft:!0,startPos:n.w-bi,hook:function(e){null!=e.panel&&e.panel.update()}};"right"!==r.pos||Q.model.rtl?Q.model.rtl&&(f.toLeft=!1,f.startPos=0,f.reverse=!1):(f.toLeft=!1,f.startPos=0,f.reverse=!0),this.flowChildren(f)}}}});var Hu=DS,Nu=Hu.detection,Fu=Nu.deviceView,Vu=Nu.orientation,Wu=Hu.dom,Uu="bottomBar";Q.def(Uu,(function(e){var t=Q.getNamespace(e),n=t.frame,i=t.topBar,r=t.fullScreenClose,o=t.topEllipsis,a=t.playbackControls,l=t.playPause,s=t.seek,c=t.reset,u=(t.miscControls,t.captions),f=t.settings,d=t.playbackSpeed,h=t.fullScreenToggle,p=t.navControls,y=t.prev,b=t.next,v=t.submit;return{tag:"section",attrs:{id:Uu},overflow:"visible",parentAlign:"rb",x:function(){return i.x},yl:function(){return Fu.isPhone?0:n.h-Ln},yp:function(){return n.h-Ln},w:function(){return i.w},updateHook:function(){this.hasAllChildren()&&(Fu.isPhone?this.updatePhoneLayout():this.updateTabletLayout())},childVisibilityChangedHook:function(){this.update()},methods:{updatePhoneLayout:function(){var e=Q.model.currControlLayout;s.setVisibility(e.seekbar&&(!Fu.isPhone||Vu.isLandscape)),Vu.isLandscape?this.updatePhoneLandscapeLayout():this.updatePhonePortraitLayout()},updatePhoneLandscapeLayout:function(){var e=this;this.revertOverflowVisibility();var t=20;null!=l&&l.w>0&&(t+=l.w),a.top=n.h-Ln+5,a.left=On,a.width=this.w-On,s.width=this.w-(t+En+40),this.updateChildren(!0);var i=50,c={visible:!0,parent:{visible:!0}},p=[r,o,u,f,d,h,c,y,b,v].filter((function(e){return e&&e.visible&&e.parent.visible})),g=p.indexOf(c),m=p.length-1<5;m?p.splice.apply(p,[g,1].concat(Bu(new Array(5-(p.length-1))))):p.splice(g,1),this.applyOverflowVisibility(p,5,[h,u]);var w=i*p.length,S=(this.parent.h-w)/p.length||1,k=S/2,C=m?S:(this.parent.h-(w+i))/(p.length+1)||1,O=k,E=!1;this.styleConfigs=[],p.forEach((function(t,n){null!=t?(n>0&&e.styleConfigs.push({id:_.kebabCase(t.attrs.id),x1:t.x,y1:k+(i-t.h)/2,x2:t.x,y2:O+(i-t.h)/2}),t.update()):m&&!E&&(E=!0,O-=i+C-10),k+=i+S,O+=i+C})),this.addReplayStyles()},updatePhonePortraitLayout:function(){var e=this;this.revertOverflowVisibility(),a.top=0,a.update();var t,n=Q.model.rtl?function(e){return e.reverse()}:function(e){return e},i={visible:!0,parent:{visible:!0}},r=[l,c,u,d,f,h,i,y,b,v].filter((function(e){return e&&e.visible&&e.parent.visible})),o=r.indexOf(i);r.length-1<5?(t=r).splice.apply(t,[o,1].concat(Bu(new Array(5-(r.length-1))))):r.splice(o,1);this.applyOverflowVisibility(r,6,[c,h,u]);var s=50*(r=n(r)).length,p=(this.w-s)/r.length||1,g=p/2;r.forEach((function(t,n){null!=t&&(t.left=g+(50-t.w)/2,t.top=(e.h-t.h)/2,t.update()),g+=50+p}))},updateTabletLayout:function(){var e=Q.model.rtl;if(s.width=0,p.update(),a.visible&&s.visible){var t=this.calcChildrensWidth()+On,n=e?1:0,i=(this.children.filter((function(e){return e.w>0})).length+n)*On;s.width=this.w-t-i}this.flowChildren({alignChild:!0,bounds:{t:0,b:this.h,l:0,r:this.w},rtl:!1,pad:On,reverse:!0})},addReplayStyles:function(){var e=this;null==this.styleDiv&&(this.styleDiv=document.createElement("div"),this.el.appendChild(this.styleDiv));var t=this.styleConfigs.map((function(e){var t=e.id,n=e.x1,i=e.y1,r=e.x2,o=e.y2;return[".is-landscape #".concat(t,":not(.lightboxed) { left: ").concat(n,"px !important; top: ").concat(i,"px !important; transform: unset !important; }"),".is-landscape.has-reset.timeline-paused #".concat(t,":not(.lightboxed) { left: ").concat(r,"px !important; top: ").concat(o,"px !important; transform: unset !important; }")].join(" ")}));this.styleDiv.innerHTML="<style>".concat(t.join(" "),"</style>"),window.requestAnimationFrame((function(){var t=e.styleConfigs.map((function(e){var t=e.id;e.x1,e.y1,e.x2,e.y2;return[".is-landscape.has-reset #".concat(t," { transition: top ease-out 200ms }")].join(" ")}));e.styleDiv.innerHTML+="<style>".concat(t.join(" "),"</style>")}))},applyOverflowVisibility:function(e,t,n){for(this.ctrlOverflow=[];e.length>t&&n.length>0;){var i=n.pop(),r=e.indexOf(i);r>=0&&(e.splice(r,1),i.setVisibility(!1),this.ctrlOverflow.push(i),Wu.addClass(this.el,"".concat(i.nameKey,"-overflow")))}return e},revertOverflowVisibility:function(){var e=this;null!=this.ctrlOverflow&&this.ctrlOverflow.forEach((function(t){t.setVisibility(t.layoutDefaultVisible),Wu.removeClass(e.el,"".concat(t.nameKey,"-overflow"))}))}},hp:function(){return Ln},hl:function(){return Fu.isPhone?n.h:Ln}}}));var zu="navControls",Ku=DS.detection,Zu=Ku.deviceView;Ku.orientation;Q.def(zu,(function(e){var t=Q.model.rtl,n=Q.getNamespace(e).frame;return{attrs:{id:zu,role:"navigation","aria-label":"slide navigation"},overflow:"visible",parentAlign:function(){return Zu.isTablet?t?"l":"r":"rb"},wp:function(){return this.width||0},hp:Ln,wl:function(){return Zu.isPhone?En:this.wp},hl:function(){return Zu.isPhone?n.h:this.hp},yp:"vertical-center",yl:function(){return 0},xl:function(){return Zu.isPhone?this.parent.w-this.w:this.xp},xp:function(){return this.left||0},updateHook:function(){Zu.isTablet&&this.flowChildren({rtl:t,startPos:-10,pad:On,fullUpdate:!0,sizeToChildren:!0})}}}));var Qu="miscControls",Gu=DS.detection.deviceView;function qu(e){return qu="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},qu(e)}function Yu(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,(r=i.key,o=void 0,o=function(e,t){if("object"!==qu(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var i=n.call(e,t||"default");if("object"!==qu(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(r,"string"),"symbol"===qu(o)?o:String(o)),i)}var r,o}Q.def(Qu,(function(e){var t=Q.getNamespace(e).frame,n=Q.model.rtl;return{attrs:{id:Qu,"aria-label":"misc controls",role:"region"},wp:function(){return this.width||0},wl:function(){return Gu.isPhone?58:this.wp},xp:function(){return this.left||0},yl:function(){return 0},xl:function(){return Gu.isPhone?this.parent.w-this.w:this.xp},hp:function(){return 65},hl:function(){return Gu.isPhone?t.h:this.hp},overflow:"visible",parentAlign:function(){return n?"l":"r"},beforeUpdateHook:function(){Gu.isPhone||this.flowChildren({fullUpdate:!0,pad:bi,startPos:0,sizeToChildren:!0,rtl:n})}}}));var Xu=DS,$u=Xu._,Ju=Xu.pubSub,ef=Xu.events,tf=Xu.dom.toggleClasses,nf=(Xu.shortcutManager,function(){function e(t){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.view=t,$u.bindAll(this,"onSlideChanged","onTimelineChanged","onPlaybackChanged"),Ju.on(ef.slide.STARTED,this.onSlideChanged)}var t,n,i;return t=e,(n=[{key:"teardown",value:function(){Ju.off(ef.slide.STARTED,this.onSlideChanged)}},{key:"onTimelineChanged",value:function(e,t){e!==this.currTimeline&&(Ju.trigger(ef.playbackControls.TIMELINE_CHANGED,e,t),null!=this.currTimeline&&(this.currTimeline.off(ef.timeline.PLAYING,this.onPlaybackChanged),this.currTimeline.off(ef.timeline.PAUSED,this.onPlaybackChanged),this.currTimeline.off(ef.timeline.ENDED,this.onPlaybackChanged)),this.currTimeline=e,this.currTimeline.on(ef.timeline.PLAYING,this.onPlaybackChanged),this.currTimeline.on(ef.timeline.PAUSED,this.onPlaybackChanged),this.currTimeline.on(ef.timeline.ENDED,this.onPlaybackChanged),this.onPlaybackChanged())}},{key:"onPlaybackChanged",value:function(){var e=this.view.children,t=e.reset,n=e.playPause,i=null!=this.currTimeline&&"playing"===this.currTimeline.playbackState();tf(document.body,"timeline-playing","timeline-paused",i),tf(document.body,"has-reset","no-reset",this.view.visible&&null!=t&&t.visible),null!=n&&n.viewLogic.onPlaybackChanged()}},{key:"onSlideChanged",value:function(e,t,n){this.view.el.tabIndex=0,this.view.el.tabIndex=-1,this.view.nameSpace===n&&(null!=this.currSlide&&this.currSlide.off(ef.slide.CURRENT_TIMELINE,this.onTimelineChanged),this.currSlide=t,this.currSlide.on(ef.slide.CURRENT_TIMELINE,this.onTimelineChanged),this.onTimelineChanged(t.currentTimeline(),t))}}])&&Yu(t.prototype,n),i&&Yu(t,i),Object.defineProperty(t,"prototype",{writable:!1}),e}()),rf=DS.detection,of=rf.deviceView,af=rf.orientation,lf="playbackControls";function sf(e){return sf="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},sf(e)}function cf(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,yf(i.key),i)}}function uf(e,t){return uf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},uf(e,t)}function ff(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,i=hf(e);if(t){var r=hf(this).constructor;n=Reflect.construct(i,arguments,r)}else n=i.apply(this,arguments);return function(e,t){if(t&&("object"===sf(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return df(e)}(this,n)}}function df(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function hf(e){return hf=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},hf(e)}function pf(e,t,n){return(t=yf(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function yf(e){var t=function(e,t){if("object"!==sf(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var i=n.call(e,t||"default");if("object"!==sf(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===sf(t)?t:String(t)}Q.def(lf,nf,(function(e){var t=Q.getNamespace(e).seek,n=Q.model.rtl;return{attrs:{id:lf,"aria-label":"playback controls",role:"region",tabindex:-1},visibility:"no-reflow",x:function(){return this.left||0},w:function(){return this.width+(t.visible?0:t.w)||0},h:Ln,overflow:"visible",noUpdate:!0,beforeUpdateHook:function(){if(of.isTablet)this.flowChildren({rtl:n,fullUpdate:!0,sizeToChildren:!0,startPos:-10,pad:On});else if(af.isLandscape){this.flowChildren({fullUpdate:!0,startPos:-30,pad:30})}},parentAlign:function(){return n?"r":"l"},y:function(){return this.top||0}}}));var bf=DS,vf=bf._,gf=bf.detection,mf=bf.utils,wf=bf.keyManager,Sf=bf.dom,kf=bf.dom,Cf=kf.addClass,Of=kf.removeClass,Ef=bf.globalEventHelper.addWindowListener,Lf=bf.events,xf=bf.pubSub,Pf=(bf.stringTabler,function(e){for(;e.startsWith("0")&&!e.startsWith("0:");)e=e.substring(1);return e}),Tf=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&uf(e,t)}(o,e);var t,n,i,r=ff(o);function o(e){var t;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,o),pf(df(t=r.call(this,e)),"hasTooltip",!0),pf(df(t),"tooltipMoves",!0),vf.bindAll(df(t),"onTick","onSeek","onMouseDown","onMouseUp","onMouseMove","checkSeekable","onKeydown","onTimelineChanged","onChange"),xf.on(Lf.slide.STARTED,t.checkSeekable),xf.on(Lf.player.SEEK,t.onKeydown),xf.on(Lf.playbackControls.TIMELINE_CHANGED,t.onTimelineChanged),t.isUserControlled=!Q.model.frame.controlOptions.controls.readonly||Q.model.frame.controlOptions.controls.readonlyOnce,t.bindListeners(),t.isUp=!0,t}return t=o,(n=[{key:"bindListeners",value:function(){if(this.isUserControlled){var e=gf.deviceView.isClassicDesktop?this.seekEl:this.view.el;gf.device.isMobile?(e.addEventListener("touchstart",this.onMouseDown),this.seekEvent="touchmove",this.endEvent="touchend"):(e.addEventListener("mousedown",this.onMouseDown),e.addEventListener("keydown",this.onKeydown),this.seekEvent="mousemove",this.endEvent="mouseup"),this.progressBarEl.addEventListener("change",this.onChange)}}},{key:"isSeekable",value:function(){if(null==this.currSlide)return!1;var e=Q.model.frame.controlOptions.controls,t=e.readonly,n=e.readonlyOnce;return!(t&&!n||n&&!this.currSlide.currentTimelineCompletedOnce())}},{key:"getSeekValue",value:function(e){var t=this.view.getBox();return mf.clamp(0,1,(e-t.x)/t.w)}},{key:"getEventX",value:function(e){return gf.device.isMobile?e.touches[0]&&e.touches[0].pageX:e.pageX}},{key:"onTimelineChanged",value:function(e,t){null!=this.currTimeline&&(this.currTimeline.off(Lf.timeline.TICK,this.onTick),this.currTimeline.off(Lf.timeline.COMPLETE,this.checkSeekable)),this.currSlide=t,this.currTimeline=e,this.currTimeline.on(Lf.timeline.TICK,this.onTick),this.currTimeline.on(Lf.timeline.COMPLETE,this.checkSeekable);var n=this.currTimeline.progress();this.duration=mf.toSeconds(this.currTimeline.timelineDuration()),this.progressBarEl.max=Math.round(1e3*this.duration),this.progressBarEl.step=100,isNaN(n)&&(n=0),this.onTick(n),this.checkSeekable()}},{key:"checkSeekable",value:function(){this.isSeekable()?(Of(this.el,"read-only"),this.progressBarEl.disabled=!1):(Cf(this.el,"read-only"),this.progressBarEl.disabled=!0)}},{key:"onTick",value:function(e){var t=100*e;this.progressBarFillEl.style.width="".concat(t,"%"),this.progressBarEl.setAttribute("aria-valuetext","".concat(Math.round(t),"%")),this.progressBarEl.value=this.currTimeline.timeline?this.currTimeline.timeline.currentTime:0,xf.trigger(Lf.currTimeline.TICK,e,Math.floor(this.progressBarEl.value))}},{key:"onSeek",value:function(e){var t=this;e.preventDefault(),e.stopPropagation(),this.seeking=!0,!0!==this.isUp&&this.currTimeline.isPlaying()&&(this.currTimeline.pause(!0),this.currTimeline.on(Lf.timeline.AFTER_SEEK_UPDATE,(function e(){t.currTimeline.play(),t.currTimeline.off(Lf.timeline.AFTER_SEEK_UPDATE,e)})));var n=this.getEventX(e);null!=n&&this.currTimeline.progress(this.getSeekValue(n)),this.isUp=!1}},{key:"seekBy",value:function(e,t){var n=this;e.preventDefault();var i=(this.currTimeline.elapsedTime()+t)/this.currTimeline.duration();this.currTimeline.onSeekStart(),this.currTimeline.progress(i),this.currTimeline.onSeekEnd(),this.currTimeline.isPlaying()&&(this.currTimeline.pause(),setTimeout((function(){return n.currTimeline.play()}),125))}},{key:"isEnded",value:function(){return this.currTimeline.progress()>=1}},{key:"onChange",value:function(e){this.currTimeline.progress(this.progressBarEl.value/this.progressBarEl.max)}},{key:"onMouseDown",value:function(e){this.isUp=!1,this.removeEndListener=Ef(this.endEvent,this.onMouseUp),this.removeSeekListener=Ef(this.seekEvent,this.onSeek),this.currTimeline.onSeekStart(),this.onSeek(e)}},{key:"onMouseUp",value:function(e){this.onSeek(e),this.removeEndListener(),this.removeSeekListener(),this.isUp=!0,this.currTimeline.onSeekEnd()}},{key:"onKeydown",value:function(e){var t=e.which;wf.isActionKey(t)?xf.trigger(Lf.player.TOGGLE_PLAYBACK):this.isSeekable()&&(wf.isDownishKey(t)?this.seekBy(e,-100):wf.isPageDownKey(t)?this.seekBy(e,-1e3):wf.isUpishKey(t)?this.seekBy(e,100):wf.isPageUpKey(t)&&this.seekBy(e,1e3))}},{key:"getViewBox",value:function(e){var t=this.view.getBox();return null!=e&&(t.x="focusin"===e.type?t.x+t.w/2:e.pageX,t.w=2),t}},{key:"onMouseMove",value:function(e){bt.updateTooltip(e.pageX,this.getTooltipString())}},{key:"setForFollowMouse",value:function(){this.el.addEventListener("mousemove",this.onMouseMove)}},{key:"stopFollowMouse",value:function(){this.el.removeEventListener("mousemove",this.onMouseMove)}},{key:"getTooltipString",value:function(e){if(null!=this.duration){this.view.getBox().x;var t=this.getSeekValue(Sf.mouseX)*this.duration;null!=e&&"focusin"===e.type&&(t=Math.floor(mf.toSeconds(this.progressBarEl.value)));var n=mf.formatSecondsAsTime(t),i=mf.formatSecondsAsTime(this.duration);return"".concat(Pf(n)," / ").concat(Pf(i))}}}])&&cf(t.prototype,n),i&&cf(t,i),Object.defineProperty(t,"prototype",{writable:!1}),o}(_t);function jf(e){return jf="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},jf(e)}function _f(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,(r=i.key,o=void 0,o=function(e,t){if("object"!==jf(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var i=n.call(e,t||"default");if("object"!==jf(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(r,"string"),"symbol"===jf(o)?o:String(o)),i)}var r,o}function Df(){return Df="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(e,t,n){var i=function(e,t){for(;!Object.prototype.hasOwnProperty.call(e,t)&&null!==(e=Bf(e)););return e}(e,t);if(i){var r=Object.getOwnPropertyDescriptor(i,t);return r.get?r.get.call(arguments.length<3?e:n):r.value}},Df.apply(this,arguments)}function If(e,t){return If=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},If(e,t)}function Af(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,i=Bf(e);if(t){var r=Bf(this).constructor;n=Reflect.construct(i,arguments,r)}else n=i.apply(this,arguments);return function(e,t){if(t&&("object"===jf(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return Rf(e)}(this,n)}}function Rf(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Bf(e){return Bf=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},Bf(e)}var Mf=DS,Hf=Mf._,Nf=Mf.pubSub,Ff=Mf.events,Vf=Mf.dom,Wf=Mf.utils.pxify,Uf=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&If(e,t)}(o,e);var t,n,i,r=Af(o);function o(e){var t;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,o),t=r.call(this,e),Hf.bindAll(Rf(t),"onSlideStarted","onStartProgressDetails","onStopProgressDetails","animateProgressDetails"),Nf.on(Ff.slide.STARTED,t.onSlideStarted),t.resetPreviewWidths(),t.isUserControlled&&(t.view.el.addEventListener("mouseenter",t.onStartProgressDetails),t.view.el.addEventListener("mouseleave",t.onStopProgressDetails)),t}return t=o,n=[{key:"onTick",value:function(e){Df(Bf(o.prototype),"onTick",this).call(this,e),this.progress=e}},{key:"resetPreviewWidths",value:function(){this.previewWidth=0,this.prevProgWidth=0}},{key:"onSlideStarted",value:function(){this.resetPreviewWidths()}},{key:"onStartProgressDetails",value:function(){this.animateProgressDetails()}},{key:"onStopProgressDetails",value:function(){window.cancelAnimationFrame(this.detailsId)}},{key:"updatePreviewEls",value:function(){this.progressPreviewEl.style.width=Wf(this.previewWidth),this.prevProgressPreviewEl.style.width=Wf(this.prevProgWidth)}},{key:"animateProgressDetails",value:function(){var e=this.view.getBox(),t=Vf.mouseX-e.x,n=this.progress*e.w,i=n-e.w*(t/e.w);t>=n&&(i=0),this.previewWidth=Math.min(e.w,t),this.prevProgWidth=Math.min(n,i),this.updatePreviewEls(),this.detailsId=window.requestAnimationFrame(this.animateProgressDetails)}}],n&&_f(t.prototype,n),i&&_f(t,i),Object.defineProperty(t,"prototype",{writable:!1}),o}(Tf),zf=Uf,Kf=(DS.detection.device.isMobile,"seek");function Zf(e){return Zf="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Zf(e)}function Qf(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,(r=i.key,o=void 0,o=function(e,t){if("object"!==Zf(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var i=n.call(e,t||"default");if("object"!==Zf(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(r,"string"),"symbol"===Zf(o)?o:String(o)),i)}var r,o}function Gf(){return Gf="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(e,t,n){var i=function(e,t){for(;!Object.prototype.hasOwnProperty.call(e,t)&&null!==(e=$f(e)););return e}(e,t);if(i){var r=Object.getOwnPropertyDescriptor(i,t);return r.get?r.get.call(arguments.length<3?e:n):r.value}},Gf.apply(this,arguments)}function qf(e,t){return qf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},qf(e,t)}function Yf(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,i=$f(e);if(t){var r=$f(this).constructor;n=Reflect.construct(i,arguments,r)}else n=i.apply(this,arguments);return function(e,t){if(t&&("object"===Zf(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return Xf(e)}(this,n)}}function Xf(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function $f(e){return $f=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},$f(e)}Q.def(Kf,zf,(function(e){var t=Q.model,n=Q.model.frame.controlOptions.controls,i=n.readonly,r=de.getColor(e,".cs-seekcontrol","border-top-color"),o=de.getColor(e,".cs-seekcontrol","background-image").replace("(, ","(180deg,"),a=Q.getNamespace(e).slide;return{attrs:{id:Kf,tabindex:-1,class:"progress-bar cs-seekcontrol ".concat(i?"read-only":"")},y:"vertical-center",overflow:"visible",noUpdate:!0,w:function(){return this.width||0},h:30,updateHook:function(){var e=this,t=this.el.offsetParent;if(DS.detection.deviceView.isPhone&&DS.detection.orientation.isLandscape&&null!=t){var n=a.el.getBoundingClientRect(),i=n.height+n.top,r=t.getBoundingClientRect(),o=this.children.seek.el.getBoundingClientRect(),l=r.top+o.top-this.el.getBoundingClientRect().top+o.height;this.el.style.top="".concat(i-l,"px"),this.el.style.width="".concat(n.width,"px"),this.el.style.left="".concat(n.left-r.left,"px"),this.el.style.transform="none";var s=0;requestAnimationFrame((function t(){s++;var i=a.el.getBoundingClientRect();i.top!==n.top||i.left!==n.left||i.width!==n.width||i.height!==n.height?e.updateHook():s<10&&requestAnimationFrame(t)}))}},html:"\n      <style>\n        #seek:before {\n          border: 1px solid ".concat(r,";\n          background-image: ").concat(o,';\n          background-repeat: no-repeat !important;\n        }\n      </style>\n\n      <div class="cs-seekbar-inner progress-bar-inner slide-lockable">\n        <div data-ref="seek" class="cs-seek progress-bar-seek">\n          <div class="progress-bar-fill-preview cs-fill" data-ref="progressPreview"></div>\n          <input\n            tabIndex="0"\n            type="range"\n            aria-hidden="').concat(!n.seekbar,'"\n            aria-label="').concat(t.getString("acc_slide_progress"),'"\n            data-ref="progressBar">\n          <div class="cs-fill cs-brandhighlight-bg progress-bar-fill" style="width: 0px" data-ref="progressBarFill">\n            <div class="prev-progress-bar-fill-preview cs-fill" data-ref="prevProgressPreview"></div>\n          </div>\n        </div>\n      </div>\n    '),methods:{updateDomStrings:function(){this.viewLogic.progressBarEl.setAttribute("aria-label",t.getString("acc_slide_progress"))}}}}));var Jf=DS,ed=Jf._,td=Jf.pubSub,nd=Jf.events,id=Jf.dom,rd=Jf.utils.pxify,od=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&qf(e,t)}(o,e);var t,n,i,r=Yf(o);function o(e){var t;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,o),t=r.call(this,e),ed.bindAll(Xf(t),"onSlideStarted","onStartProgressDetails","onStopProgressDetails","animateProgressDetails"),td.on(nd.slide.STARTED,t.onSlideStarted),t.resetPreviewWidths(),t.isUserControlled&&(t.view.el.addEventListener("mouseenter",t.onStartProgressDetails),t.view.el.addEventListener("mouseleave",t.onStopProgressDetails)),t}return t=o,n=[{key:"onTick",value:function(e){Gf($f(o.prototype),"onTick",this).call(this,e),this.progress=e}},{key:"resetPreviewWidths",value:function(){this.previewWidth=0,this.prevProgWidth=0}},{key:"onSlideStarted",value:function(){this.resetPreviewWidths()}},{key:"onStartProgressDetails",value:function(){this.animateProgressDetails()}},{key:"onStopProgressDetails",value:function(){window.cancelAnimationFrame(this.detailsId)}},{key:"updatePreviewEls",value:function(){this.progressPreviewEl.style.width=rd(this.previewWidth),this.prevProgressPreviewEl.style.width=rd(this.prevProgWidth)}},{key:"animateProgressDetails",value:function(){var e=this.view.getBox(),t=id.mouseX-e.x,n=this.progress*e.w,i=n-e.w*(t/e.w);t>=n&&(i=0),this.previewWidth=Math.min(e.w,t),this.prevProgWidth=Math.min(n,i),this.updatePreviewEls(),this.detailsId=window.requestAnimationFrame(this.animateProgressDetails)}}],n&&Qf(t.prototype,n),i&&Qf(t,i),Object.defineProperty(t,"prototype",{writable:!1}),o}(Tf),ad=od,ld=(DS.detection.device.isMobile,"floatingSeek");Q.def(ld,ad,(function(e){var t=Q.model,n=Q.model.frame.controlOptions.controls,i=n.readonly,r=de.getColor(e,".cs-seekcontrol","border-top-color"),o=de.getColor(e,".cs-seekcontrol","background-image").replace("(, ","(180deg,"),a=Q.getNamespace(e).slide;return{attrs:{id:ld,tabindex:-1,class:"progress-bar cs-seekcontrol ".concat(i?"read-only":"")},x:On,y:function(){return this.parent.h-Ln-15},z:2,overflow:"visible",w:function(){return this.parent.w-20},h:30,updateHook:function(){var e=this.el.offsetParent;if(DS.detection.deviceView.isMobile&&DS.detection.orientation.isLandscape&&null!=e){var t=a.el.getBoundingClientRect(),n=t.height+t.top,i=e.getBoundingClientRect(),r=this.children.floatingSeek.el.getBoundingClientRect(),o=i.top+r.top-this.el.getBoundingClientRect().top+r.height;this.el.style.top="".concat(n-o,"px"),this.el.style.width="".concat(t.width,"px"),this.el.style.left="".concat(t.left-i.left,"px"),this.el.style.transform="none"}},html:"\n      <style>\n        #seek:before {\n          border: 1px solid ".concat(r,";\n          background-image: ").concat(o,';\n          background-repeat: no-repeat !important;\n        }\n      </style>\n\n      <div class="cs-seekbar-inner progress-bar-inner slide-lockable">\n        <div data-ref="floatingSeek" class="cs-seek progress-bar-seek">\n          <div class="progress-bar-fill-preview cs-fill" data-ref="progressPreview"></div>\n          <input\n            tabIndex="0"\n            type="range"\n            aria-hidden="').concat(!n.seekbar,'"\n            aria-label="').concat(t.getString("acc_slide_progress"),'"\n            data-ref="progressBar">\n          <div class="cs-fill cs-brandhighlight-bg progress-bar-fill" style="width: 0px" data-ref="progressBarFill">\n            <div class="prev-progress-bar-fill-preview cs-fill" data-ref="prevProgressPreview"></div>\n          </div>\n        </div>\n      </div>\n    '),methods:{updateDomStrings:function(){this.viewLogic.progressBarEl.setAttribute("aria-label",t.getString("acc_slide_progress"))}}}}));var sd=function(){return"no icon"},cd={next:function(){return'\n  <svg class="cs-icon next-icon" width="10px" height="18px" viewBox="0 -1 10 18" focusable="false">\n    <path transform="rotate(180, 5, 8)" d="M2.81685219,7.60265083 L9.00528946,1.41421356 L7.5910759,-1.27897692e-13 L1.55431223e-13,7.5910759 L0.0115749356,7.60265083 L1.55431223e-13,7.61422577 L7.5910759,15.2053017 L9.00528946,13.7910881 L2.81685219,7.60265083 Z" stroke="none" fillRule="evenodd"></path>\n  </svg>'},prev:function(){return'\n  <svg class="cs-icon prev-icon" width="10px" height="18px" viewBox="0 -1 10 18" focusable="false">\n    <path transform="translate(0, 1)" d="M2.81685219,7.60265083 L9.00528946,1.41421356 L7.5910759,-1.27897692e-13 L1.55431223e-13,7.5910759 L0.0115749356,7.60265083 L1.55431223e-13,7.61422577 L7.5910759,15.2053017 L9.00528946,13.7910881 L2.81685219,7.60265083 Z" stroke="none" fillRule="evenodd"></path>\n  </svg>\n'},submit:function(){return'\n   <svg class="cs-icon check-icon" width="17px" height="18px" viewBox="0 0 17 16" focusable="false">\n    <path stroke="none" transform="translate(0, 1)" d="M 17 1.4 L 15.6 0 5.7 9.9 1.4 5.65 0 7.05 5.65 12.75 5.7 12.75 17 1.4 Z"/>\n  </svg>'},replay:function(){return'<svg class="cs-icon" x="0px" y="0px" width="16px" height="16px" viewBox="0 0 16 16" focusable="false">\n    <path fill="#FFFFFF" stroke="none" d="\n      M 10.95 8.75\n      Q 11 9 11 9.25 10.95 11.15 9.7 12.4 8.4 13.7 6.5 13.75 4.6 13.7 3.3 12.4 2.05 11.15 2 9.25 2.05 7.3 3.3 6.05 4.398828125 4.998828125 6 4.75\n      L 6 6.9\n      Q 6.05 7.75 6.85 7.35\n      L 11.35 4.3\n      Q 11.7 4.05 11.7 3.75 11.7 3.45 11.35 3.2\n      L 6.85 0.15\n      Q 6.05 -0.3 6 0.6\n      L 6 2.75\n      Q 3.4517578125 3.001171875 1.8 4.75 0.05 6.6 0 9.25 0.05 12 1.9 13.85 3.75 15.65 6.5 15.75 9.25 15.65 11.1 13.85 12.95 12 13 9.25 13 9 13 8.75\n      L 10.95 8.75 Z"/>\n    </svg>'},play:function(){return'<svg id="icon-play" class="cs-icon play-icon" width="11" height="13" viewBox="0 0 11 13" focusable="false">\n    <path d="M 0.851 13.011 C 0.381 13.295 0 13.068 0 12.526 L 0 0.771 C 0 0.219 0.378 0 0.854 0.288 L 10.507 6.132 C 10.979 6.417 10.981 6.878 10.504 7.168 L 6.307 9.708 L 0.851 13.011 Z"></path>\n  </svg>'},pause:function(){return'<svg id="icon-pause" class="cs-icon pause-icon" width="9" height="14" viewBox="0 0 9 14" focusable="false">\n    <g>\n       <rect x="0" width="3" height="14"/>\n       <rect x="6" width="3" height="14"/>\n    </g>\n  </svg>'},volume:function(e,t){var n=Math.min(1,e/5),i=Math.min(1,Math.max(0,e/5-.5));return'<svg class="cs-icon volume-icon '.concat(t?"volume-icon-selected":"",'" width="16px" height="14px" viewBox="0 -3 16 14" focusable="false">\n    <g transform="translate(0, -3)">\n      <rect x="0" y="4" width="3" height="6"></rect>\n      <polygon points="4 4 9 0 9 14 4 10"></polygon>\n      <g transform="translate(10, 0)">\n        <mask id="vol-mask" fill="white">\n          <rect id="path-1" x="0" y="0" width="8" height="14"></rect>\n        </mask>\n        <circle strokeWidth="1.5" style="opacity: ').concat(i,';" mask="url(#vol-mask)" fill="none" cx="-1" cy="7" r="6.5"></circle>\n        <circle strokeWidth="1.5" style="opacity: ').concat(n,';" mask="url(#vol-mask)" fill="none" cx="-1" cy="7" r="3.5"></circle>\n      </g>\n    </g>\n  </svg>')},captionsOn:function(e){return'<svg class="cs-icon captions-icon" width="19px" height="16px" viewBox="0 0 19 16" focusable="false">\n            <path fill="#FFFFFF" stroke="none" d="M 19 2 Q 19 1.15 18.4 0.6 17.85 0 17 0 L 2 0 Q 1.15 0 0.6 0.6 0 1.15 0 2 L 0 12 Q 0 12.85 0.6 13.4 1.15 14 2 14 L 7.6 14 9.5 15.9 11.4 14 17 14 Q 17.85 14 18.4 13.4 19 12.85 19 12 L 19 2 M 15.7 4.2 L 15.25 4.85 Q 15.15 4.9 15.1 5 15 5.05 14.85 5.05 14.75 5.05 14.6 4.95 14.5 4.9 14.3 4.8 14.15 4.65 13.9 4.6 13.65 4.5 13.3 4.5 12.85 4.5 12.5 4.7 12.15 4.85 11.9 5.15 11.7 5.45 11.6 5.9 11.45 6.35 11.45 6.9 11.5 7.45 11.6 7.9 11.7 8.35 11.95 8.65 12.2 8.95 12.5 9.15 12.85 9.3 13.25 9.3 13.65 9.3 13.9 9.2 14.2 9.1 14.35 8.95 14.5 8.85 14.65 8.75 14.8 8.65 14.95 8.65 15.15 8.65 15.25 8.8 L 15.75 9.4 Q 15.45 9.75 15.15 10 14.8 10.2 14.45 10.35 14.05 10.5 13.7 10.55 13.3 10.6 12.95 10.6 12.25 10.6 11.65 10.35 11.1 10.1 10.65 9.65 10.2 9.15 9.95 8.45 9.7 7.75 9.7 6.9 9.7 6.1 9.95 5.4 10.15 4.75 10.6 4.25 11.05 3.75 11.7 3.5 12.35 3.2 13.2 3.2 14 3.2 14.6 3.45 15.2 3.7 15.7 4.2 M 5.85 4.7 Q 5.5 4.85 5.25 5.15 5.05 5.45 4.95 5.9 4.8 6.35 4.8 6.9 4.85 7.45 4.95 7.9 5.05 8.35 5.3 8.65 5.55 8.95 5.85 9.15 6.2 9.3 6.6 9.3 7 9.3 7.25 9.2 7.55 9.1 7.7 8.95 7.85 8.85 8 8.75 8.15 8.65 8.3 8.65 8.5 8.65 8.6 8.8 L 9.1 9.4 Q 8.8 9.75 8.5 10 8.15 10.2 7.8 10.35 7.4 10.5 7.05 10.55 6.65 10.6 6.3 10.6 5.6 10.6 5 10.35 4.45 10.1 4 9.65 3.55 9.15 3.3 8.45 3.05 7.75 3.05 6.9 3.05 6.1 3.3 5.4 3.5 4.75 3.95 4.25 4.4 3.75 5.05 3.5 5.7 3.2 6.55 3.2 7.35 3.2 7.95 3.45 8.55 3.7 9.05 4.2 L 8.6 4.85 Q 8.5 4.9 8.45 5 8.35 5.05 8.2 5.05 8.1 5.05 7.95 4.95 7.85 4.9 7.65 4.8 7.5 4.65 7.25 4.6 7 4.5 6.65 4.5 6.2 4.5 5.85 4.7 Z"/>\n          </svg>'},captionsOff:function(e){return'<svg class="cs-icon captions-icon" width="19px" height="16px" viewBox="0 0 19 16" focusable="false">\n            <g>\n              <path d="M 11.45 3.5 Q 10.8 3.75 10.35 4.25 9.9 4.75 9.7 5.4 9.45 6.1 9.45 6.9 9.45 7.75 9.7 8.45 9.95 9.15 10.4 9.65 10.85 10.1 11.4 10.35 12 10.6 12.7 10.6 13.05 10.6 13.45 10.55 13.8 10.5 14.2 10.35 14.55 10.2 14.9 10 15.2 9.75 15.5 9.4 L 15 8.8 Q 14.9 8.65 14.7 8.65 14.55 8.65 14.4 8.75 14.25 8.85 14.1 8.95 13.95 9.1 13.65 9.2 13.4 9.3 13 9.3 12.6 9.3 12.25 9.15 11.95 8.95 11.7 8.65 11.45 8.35 11.35 7.9 11.25 7.45 11.2 6.9 11.2 6.35 11.35 5.9 11.45 5.45 11.65 5.15 11.9 4.85 12.25 4.7 12.6 4.5 13.05 4.5 13.4 4.5 13.65 4.6 13.9 4.65 14.05 4.8 14.25 4.9 14.35 4.95 14.5 5.05 14.6 5.05 14.75 5.05 14.85 5 14.9 4.9 15 4.85 L 15.45 4.2 Q 14.95 3.7 14.35 3.45 13.75 3.2 12.95 3.2 12.1 3.2 11.45 3.5 M 5.6 4.7 Q 5.95 4.5 6.4 4.5 6.75 4.5 7 4.6 7.25 4.65 7.4 4.8 7.6 4.9 7.7 4.95 7.85 5.05 7.95 5.05 8.1 5.05 8.2 5 8.25 4.9 8.35 4.85 L 8.8 4.2 Q 8.3 3.7 7.7 3.45 7.1 3.2 6.3 3.2 5.45 3.2 4.8 3.5 4.15 3.75 3.7 4.25 3.25 4.75 3.05 5.4 2.8 6.1 2.8 6.9 2.8 7.75 3.05 8.45 3.3 9.15 3.75 9.65 4.2 10.1 4.75 10.35 5.35 10.6 6.05 10.6 6.4 10.6 6.8 10.55 7.15 10.5 7.55 10.35 7.9 10.2 8.25 10 8.55 9.75 8.85 9.4 L 8.35 8.8 Q 8.25 8.65 8.05 8.65 7.9 8.65 7.75 8.75 7.6 8.85 7.45 8.95 7.3 9.1 7 9.2 6.75 9.3 6.35 9.3 5.95 9.3 5.6 9.15 5.3 8.95 5.05 8.65 4.8 8.35 4.7 7.9 4.6 7.45 4.55 6.9 4.55 6.35 4.7 5.9 4.8 5.45 5 5.15 5.25 4.85 5.6 4.7 Z" />\n              <path class="icon-stroke-only" stroke-width="1.5" stroke-linejoin="round" stroke-linecap="round" fill="none" d="M 9.5 15.2 L 7.8 13.5 2 13.5 Q 1.35 13.5 0.95 13.05 0.5 12.65 0.5 12 L 0.5 2 Q 0.5 1.35 0.95 0.95 1.35 0.5 2 0.5 L 17 0.5 Q 17.65 0.5 18.05 0.95 18.5 1.35 18.5 2 L 18.5 12 Q 18.5 12.65 18.05 13.05 17.65 13.5 17 13.5 L 11.2 13.5 9.5 15.2 Z" />\n            </g>\n          </svg>'},captions:function(){return'\n    <svg class="cs-icon captions-icon" width="19px" height="16px" viewBox="0 0 19 16" focusable="false">\n      <g>\n        <path d="M 11.45 3.5 Q 10.8 3.75 10.35 4.25 9.9 4.75 9.7 5.4 9.45 6.1 9.45 6.9 9.45 7.75 9.7 8.45 9.95 9.15 10.4 9.65 10.85 10.1 11.4 10.35 12 10.6 12.7 10.6 13.05 10.6 13.45 10.55 13.8 10.5 14.2 10.35 14.55 10.2 14.9 10 15.2 9.75 15.5 9.4 L 15 8.8 Q 14.9 8.65 14.7 8.65 14.55 8.65 14.4 8.75 14.25 8.85 14.1 8.95 13.95 9.1 13.65 9.2 13.4 9.3 13 9.3 12.6 9.3 12.25 9.15 11.95 8.95 11.7 8.65 11.45 8.35 11.35 7.9 11.25 7.45 11.2 6.9 11.2 6.35 11.35 5.9 11.45 5.45 11.65 5.15 11.9 4.85 12.25 4.7 12.6 4.5 13.05 4.5 13.4 4.5 13.65 4.6 13.9 4.65 14.05 4.8 14.25 4.9 14.35 4.95 14.5 5.05 14.6 5.05 14.75 5.05 14.85 5 14.9 4.9 15 4.85 L 15.45 4.2 Q 14.95 3.7 14.35 3.45 13.75 3.2 12.95 3.2 12.1 3.2 11.45 3.5 M 5.6 4.7 Q 5.95 4.5 6.4 4.5 6.75 4.5 7 4.6 7.25 4.65 7.4 4.8 7.6 4.9 7.7 4.95 7.85 5.05 7.95 5.05 8.1 5.05 8.2 5 8.25 4.9 8.35 4.85 L 8.8 4.2 Q 8.3 3.7 7.7 3.45 7.1 3.2 6.3 3.2 5.45 3.2 4.8 3.5 4.15 3.75 3.7 4.25 3.25 4.75 3.05 5.4 2.8 6.1 2.8 6.9 2.8 7.75 3.05 8.45 3.3 9.15 3.75 9.65 4.2 10.1 4.75 10.35 5.35 10.6 6.05 10.6 6.4 10.6 6.8 10.55 7.15 10.5 7.55 10.35 7.9 10.2 8.25 10 8.55 9.75 8.85 9.4 L 8.35 8.8 Q 8.25 8.65 8.05 8.65 7.9 8.65 7.75 8.75 7.6 8.85 7.45 8.95 7.3 9.1 7 9.2 6.75 9.3 6.35 9.3 5.95 9.3 5.6 9.15 5.3 8.95 5.05 8.65 4.8 8.35 4.7 7.9 4.6 7.45 4.55 6.9 4.55 6.35 4.7 5.9 4.8 5.45 5 5.15 5.25 4.85 5.6 4.7 Z" />\n        <path class="icon-stroke-only" stroke-width="1.5" stroke-linejoin="round" stroke-linecap="round" fill="none" d="M 9.5 15.2 L 7.8 13.5 2 13.5 Q 1.35 13.5 0.95 13.05 0.5 12.65 0.5 12 L 0.5 2 Q 0.5 1.35 0.95 0.95 1.35 0.5 2 0.5 L 17 0.5 Q 17.65 0.5 18.05 0.95 18.5 1.35 18.5 2 L 18.5 12 Q 18.5 12.65 18.05 13.05 17.65 13.5 17 13.5 L 11.2 13.5 9.5 15.2 Z" />\n      </g>\n    </svg>'},carrot:function(e){return'\n    <svg style="left:calc('.concat(e,');" class="cs-icon cs-icon-carrot carrot"width="30" height="30" viewBox="0 0 30 30" focusable="false">\n      <g transform="translate(8, 8)">\n        <polygon style="fill:currentColor !important" points="1,1.5 5,5 1,8.5"/>\n      </g>\n  </svg>')},search:function(){return'\n    <svg class="search-icon" width="13px" height="15px" viewBox="0 0 13 15" focusable="false"\n      <g fill="none" fill-rule="evenodd">\n        <g stroke-width="2">\n          <circle cx="5.6" cy="5.6" r="4.6"/>\n          <path d="M8 9l4 5"/>\n        </g>\n      </g>\n    </svg>\n    '},searchClear:function(){return'\n    <svg class="cs-icon icon" width="11px" height="11px" viewBox="0 0 11 11">\n    <g id="Desktop-Color-Contrast" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">\n        <g id="Search" transform="translate(-290.000000, -69.000000)" fill="fill:currentColor !important" fill-rule="nonzero">\n            <g id="search" transform="translate(13.000000, 59.000000)">\n                <polygon id="ic_close" points="286.777666 10 282.500215 14.2779053 278.222334 10 277 11.2222382 281.277881 15.5002869 277 19.7779053 278.222334 21 282.500215 16.7222382 286.777666 21 288 19.7779053 283.722119 15.5002869 288 11.2222382"></polygon>\n            </g>\n        </g>\n    </g>\n    </svg>\n    '},filter:function(){return'<svg class="cs-icon icon-gear" width="14" height="14" viewBox="0 0 14 14" focusable="false">\n    <path id="icon-gear" transform="translate(0,3)" d="M11.1,9.8C11.1,9.8,11.1,9.8,11.1,9.8C11.1,9.8,11.1,9.7,11.1,9.8c0-0.1,0.1-0.1,0.1-0.1c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0-0.1c0,0,0,0,0-0.1c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0-0.1c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0-0.1,0.1-0.1c0,0,0,0,0,0c0-0.1,0.1-0.1,0.1-0.2c0,0,0,0,0,0c0-0.1,0-0.1,0.1-0.2c0,0,0,0,0,0c0.1-0.2,0.2-0.5,0.2-0.7l2-0.4V6.4l-2-0.4c0-0.3-0.1-0.5-0.2-0.7c0,0,0,0,0,0c0-0.1,0-0.1-0.1-0.2c0,0,0,0,0,0c0-0.1,0-0.1-0.1-0.2c0,0,0,0,0,0c0,0,0-0.1-0.1-0.1c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0-0.1c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0-0.1c0,0,0,0,0-0.1c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0-0.1c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0l1.2-1.7l-0.9-0.9L9.7,2.8c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0-0.1,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0-0.1,0c0,0,0,0-0.1,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0-0.1,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0-0.1,0-0.1-0.1c0,0,0,0,0,0c-0.1,0-0.1-0.1-0.2-0.1c0,0,0,0,0,0c-0.1,0-0.1,0-0.2-0.1c0,0,0,0,0,0C8.3,2.1,8.1,2.1,7.8,2L7.4,0H6.2L5.9,2c-0.3,0-0.5,0.1-0.7,0.2c0,0,0,0,0,0C5,2.3,5,2.3,4.9,2.3c0,0,0,0,0,0c-0.1,0-0.1,0-0.2,0.1c0,0,0,0,0,0c0,0-0.1,0-0.1,0.1c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0-0.1,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0-0.1,0c0,0,0,0-0.1,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0-0.1,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0L2.3,1.6L1.4,2.5l1.2,1.7c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0.1c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0.1c0,0,0,0,0,0.1c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0.1c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0.1-0.1,0.1c0,0,0,0,0,0C2.2,5,2.2,5,2.2,5.1c0,0,0,0,0,0c0,0.1,0,0.1-0.1,0.2c0,0,0,0,0,0C2,5.5,1.9,5.8,1.9,6l-2,0.4v1.2l2,0.4c0,0.3,0.1,0.5,0.2,0.7c0,0,0,0,0,0c0,0.1,0,0.1,0.1,0.2c0,0,0,0,0,0c0,0.1,0,0.1,0.1,0.2c0,0,0,0,0,0c0,0,0,0.1,0.1,0.1c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0.1c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0.1c0,0,0,0,0,0.1c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0.1c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0l-1.2,1.7l0.9,0.9L4,11.2c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0.1,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0.1,0c0,0,0,0,0.1,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0.1,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0.1,0,0.1,0.1c0,0,0,0,0,0c0.1,0,0.1,0.1,0.2,0.1c0,0,0,0,0,0c0.1,0,0.1,0,0.2,0.1c0,0,0,0,0,0c0.2,0.1,0.5,0.2,0.7,0.2l0.4,2h1.2l0.4-2c0.3,0,0.5-0.1,0.7-0.2c0,0,0,0,0,0c0.1,0,0.1,0,0.2-0.1c0,0,0,0,0,0c0.1,0,0.1,0,0.2-0.1c0,0,0,0,0,0c0,0,0.1,0,0.1-0.1c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0.1,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0.1,0c0,0,0,0,0.1,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0.1,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0l1.7,1.2l0.9-0.9L11.1,9.8C11,9.8,11,9.8,11.1,9.8C11,9.8,11.1,9.8,11.1,9.8z M6.8,9.2c-1.2,0-2.2-1-2.2-2.2c0-1.2,1-2.2,2.2-2.2C8,4.8,9,5.8,9,7C9,8.2,8,9.2,6.8,9.2z"/>\n  </svg>'},close:function(){return'<svg class="cs-icon icon" width="20" height="20" viewBox="0 0 36 36" focusable="false">\n    <use xlink:href="#close-icon" class="cs-icon-shadow" transform="translate(0, .5)" />\n    <polygon points="36,2.826 33.174,0 18,15.174 2.826,0 0,2.826 15.174,18 0,33.174 2.826,36 18,20.826 33.174,36 36,33.174 20.826,18" />\n  </svg>'},clear:function(){return'<svg class="cs-icon icon-clear" width="13" height="14" viewBox="0 0 13 14" focusable="false">\n    <use xlink:href="#icon-clear" fill="rgba(240, 240, 240, 1)" transform="translate(0, 1)" />\n    <path id="icon-clear" transform="translate(3,3)" d="M6.5,0C2.9,0,0,2.9,0,6.5C0,10.1,2.9,13,6.5,13c3.6,0,6.5-2.9,6.5-6.5C13,2.9,10.1,0,6.5,0z M1.5,6.5c0-2.8,2.2-5,5-5c1.2,0,2.4,0.5,3.2,1.2L2.2,9.1C1.8,8.3,1.5,7.5,1.5,6.5z M6.5,11.5c-1.2,0-2.3-0.5-3.2-1.2L10.8,4c0.4,0.7,0.7,1.6,0.7,2.5C11.5,9.3,9.3,11.5,6.5,11.5z"/>\n  </svg>'},hamburger:function(){return'\n    <svg class="cs-icon" width="30px" height="12px" viewBox="0 10 30 12" focusable="false">\n      <path d="M0,15 L17,15 L17,17 L0,17 L0,15 Z M0,11 L17,11 L17,13 L0,13 L0,11 Z M0,19 L17,19 L17,21 L0,21 L0,19 Z" ></path>\n    </svg>\n  '},file:function(){return'\n    <svg width="20px" height="27px" viewBox="0 0 40 50" focusable="false">\n      <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">\n        <g>\n          <path class="file-icon-bg" d="M2.00804893,0 C0.899034128,0 0,0.889064278 0,1.99091407 L0,48.0090859 C0,49.1086374 0.892756032,50 1.99862555,50 L37.2170607,50 C38.3208711,50 39.2156863,49.1011186 39.2156863,47.993136 L39.2156863,13.6363636 L26.1437908,0 L2.00804893,0 Z"></path>\n          <path class="file-icon-fold" d="M26.1437908,0 L26.1437908,11.7296861 C26.1437908,12.8319383 27.0422752,13.7254902 28.1433598,13.7254902 L39.2156863,13.7254902"></path>\n        </g>\n      </g>\n    </svg>'},link:function(e){return'\n  <svg class="link-icon" preserveAspectRatio="none" x="0px" y="0px" width="18px" height="18px" viewBox="0 0 18 18" focusable="false">\n      <path fill="'.concat(e,'" stroke="none" d="\n            M 1.45 8.5\n            Q 0.0453125 10.0015625 0 11.9\n            L 0 12.15\n            Q 0.0453125 14.0484375 1.45 15.55\n            L 1.5 15.6\n            Q 3.0015625 17.0046875 4.85 17.05\n            L 5.1 17.05\n            Q 7.0150390625 17.0046875 8.5 15.6\n            L 10.65 13.45\n            Q 10.95 13.15 10.95 12.75 10.95 12.35 10.65 12.05 10.3689453125 11.7689453125 10 11.75\n            L 9.95 11.75\n            Q 9.55 11.75 9.2 12.05\n            L 7.1 14.15\n            Q 6.2 15.05 5 15.05 3.8 15.05 2.9 14.15 2 13.25 2 12.05 2 10.8826171875 2.85 9.95\n            L 5 7.8\n            Q 5.3 7.5 5.3 7.1\n            L 5.3 7.05\n            Q 5.2810546875 6.6810546875 5 6.4 4.7 6.1 4.3 6.1 3.9 6.1 3.55 6.4\n            L 1.45 8.5\n            M 12.05 5\n            Q 11.75 4.7 11.35 4.7 10.95 4.7 10.65 5\n            L 5 10.65\n            Q 4.7 10.95 4.7 11.35 4.7 11.75 5 12.05 5.3 12.35 5.7 12.35 6.1 12.35 6.4 12.05\n            L 12.05 6.4\n            Q 12.35 6.1 12.35 5.7 12.35 5.3 12.05 5\n            M 15.6 1.5\n            L 15.55 1.45\n            Q 14 0 12.05 0\n            L 12 0\n            Q 10.05 0 8.5 1.45\n            L 6.4 3.55\n            Q 6.1 3.9 6.1 4.3 6.1 4.7 6.4 5 6.7 5.3 7.1 5.3 7.5 5.3 7.8 5\n            L 9.95 2.85\n            Q 10.8826171875 2 12.05 2 13.25 2 14.15 2.9 15.05 3.8 15.05 5 15.05 6.2 14.15 7.1\n            L 12.05 9.2\n            Q 11.75 9.55 11.75 9.95 11.75 10.35 12.05 10.65 12.35 10.95 12.75 10.95 13.15 10.95 13.45 10.65\n            L 15.6 8.5\n            Q 17.05 6.96875 17.05 5 17.05 3.05 15.6 1.5 Z"/>\n          </svg>')},disableOrientation:function(){return'<svg viewBox="0 0 161 135">\n    <g stroke="none" stroke-width="1" fill="#fff" fill-rule="evenodd">\n      <path d="M59,31.9948589 C59,30.340844 60.3408574,29 62.0069809,29 L99.9930191,29 C101.653729,29 103,30.3364792 103,31.9948589 L103,103.005141 C103,104.659156 101.659143,106 99.9930191,106 L62.0069809,106 C60.3462712,106 59,104.663521 59,103.005141 L59,31.9948589 Z M61,36 L101,36 L101,96 L61,96 L61,36 Z M81,104 C82.6568542,104 84,102.656854 84,101 C84,99.3431458 82.6568542,98 81,98 C79.3431458,98 78,99.3431458 78,101 C78,102.656854 79.3431458,104 81,104 Z M76,32.5 C76,32.2238576 76.2276528,32 76.5096495,32 L85.4903505,32 C85.7718221,32 86,32.2319336 86,32.5 C86,32.7761424 85.7723472,33 85.4903505,33 L76.5096495,33 C76.2281779,33 76,32.7680664 76,32.5 Z"></path>\n      <path d="M144.276039,68.4976037 C143.65768,83.6270348 137.530567,98.6224671 125.961909,110.191125 C101.576936,134.576098 62.1020027,134.704192 37.8006658,110.402855 L37.8275751,110.429765 L33.4090737,114.848266 L33.3821643,114.821357 C60.1400795,141.579272 103.595566,141.480117 130.445572,114.630111 C143.247134,101.828549 149.95913,85.2399018 150.581333,68.4976037 L161.373625,68.4976037 L147.23149,54.3554681 L133.089354,68.4976037 L144.276049,68.4976037 Z"></path>\n      <path d="M17.2900541,66.5559885 C17.8833587,51.3895735 24.012088,36.3498513 35.6085461,24.7533932 C59.9935191,0.36842015 99.4684528,0.240325436 123.76979,24.5416624 L123.74288,24.514753 L128.161382,20.0962516 L128.188291,20.1231609 C101.430376,-6.63475424 57.9748898,-6.5355989 31.1248839,20.314407 C18.2955218,33.1437691 11.582203,49.7766814 10.9851551,66.5559885 L0.259994507,66.5559885 L14.4021301,80.6981242 L28.5442658,66.5559885 L17.2900541,66.5559885 Z"></path>\n    </g>\n  </svg>'},enterFullScreen:function(){return'\n    <svg class="cs-icon enter-fullscreen-icon" width="14" height="14" viewBox="0 0 14 14" focusable="false">\n      <path d="M1.99055 2.3H5.1V0.4H0.1V5.2H1.9L1.9 2.3Z" stroke="none" />\n      <path d="M5.1 11.8H1.9V8.9H0.1V13.6H5.1L5.1 11.8Z" stroke="none" />\n      <path d="M11.5 11.8H8.4V13.6H13.3V8.9H11.5L11.5 11.8Z" stroke="none" />\n      <path d="M8.4 2.3H11.5V5.2H13.3V0.4H8.4L8.4 2.3Z" stroke="none" />\n    </svg>\n  '},exitFullScreen:function(){return'\n    <svg class="cs-icon exit-fullscreen-icon" width="15" height="14" viewBox="0 0 15 14" focusable="false">\n      <path d="M4.2 4.1H0.1V6.0H6.2V0H4.2V4.1Z" stroke="none" />\n      <path d="M0.1 9.8H4.2V13.9H6.2V7.9H0.1V9.8Z" stroke="none" />\n      <path d="M10.0 9.8H14.1V7.9H8.1V13.9H10.0V9.8Z" stroke="none" />\n      <path d="M8.1 0V6.0H14.1V4.1H10.0V0H8.1Z" stroke="none" />\n    </svg>\n  '},settings:function(){return'\n    <svg class="cs-icon" data-ref="settings" width="16px" height="16px" viewBox="0 0 16 16" focusable="false">\n      <path d="M8.94,0 C9.82,0 10.55,0.62 10.63,1.45 L10.73,2.36 C11.1,2.52 11.45,2.71 11.78,2.94 L12.66,2.56 C13.46,2.22 14.39,2.5 14.83,3.23 L15.77,4.77 C16.21,5.5 16,6.4 15.29,6.9 L14.51,7.42 C14.54,8.19 14.53,8.38 14.51,8.58 L15.29,9.11 C16,9.6 16.21,10.51 15.77,11.23 L14.83,12.77 C14.39,13.49 13.46,13.78 12.66,13.44 L11.78,13.06 C11.45,13.29 11.1,13.48 10.73,13.64 L10.63,14.55 C10.55,15.38 9.82,16 8.94,16 L7.06,16 C6.18,16 5.45,15.38 5.37,14.55 L5.27,13.64 C4.9,13.48 4.55,13.29 4.22,13.06 L3.34,13.44 C2.54,13.78 1.61,13.5 1.17,12.77 L0.23,11.23 C-0.21,10.51 0,9.6 0.71,9.11 L1.49,8.58 C1.46,7.81 1.47,7.62 1.49,7.42 L0.71,6.89 C0,6.40 -0.21,5.49 0.23,4.77 L1.17,3.23 C1.61,2.51 2.54,2.22 3.34,2.56 L4.22,2.94 C4.55,2.71 4.9,2.52 5.27,2.36 L5.37,1.45 C5.45,0.62 6.18,0 7.06,0 Z M7.96,4.53 C5.91,4.53 4.25,6.11 4.25,8.06 C4.25,10.01 5.91,11.59 7.96,11.59 C10.02,11.59 11.68,10.01 11.68,8.06 C11.68,6.11 10.02,4.53 7.96,4.53 Z"></path>\n    </svg>\n    '},playbackSpeed:function(){return'\n    <svg class="cs-icon" width="15" height="15" viewBox="0 0 15 15" focusable="false">\n      <path d="M5.9 4.0L10.4 7.4L5.9 10.8V4.0ZM1.5 8.2H0.0C0.1 9.6 0.6 10.9 1.5 12.0L2.6 11.0C2.0 10.1 1.6 9.2 1.5 8.2H1.5ZM15 7.4H14.9C14.9 5.6 14.3 3.8 13.0 2.4C11.8 1.0 10.0 0.1 8.2 0.0V1.5C10.1 1.7 11.8 2.9 12.8 4.7C13.7 6.4 13.7 8.5 12.8 10.2C11.8 12.0 10.1 13.1 8.2 13.4V14.9C10.0 14.7 11.8 13.8 13.0 12.5C14.3 11.1 14.9 9.3 14.9 7.4L15 7.4ZM3.6 12.1L2.5 13.1C3.7 14.1 5.1 14.8 6.7 14.9V13.4V13.4C5.5 13.3 4.5 12.8 3.6 12.1V12.1ZM2.6 3.9L1.5 2.8C0.6 3.9 0.1 5.3 0 6.7H1.5H1.5C1.6 5.7 2.0 4.7 2.6 3.9H2.6ZM6.7 1.5V0.0C5.1 0.1 3.7 0.7 2.5 1.7L3.6 2.8C4.5 2.1 5.5 1.6 6.7 1.5V1.5Z" stroke="none" />\n    </svg>\n  '},track:function(e,t){return'\n    <svg xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="16px" viewBox="0 0 24 16" focusable="false">\n      <defs>\n        <rect id="'.concat(t,'-track" x="2" y="3.5" width="20" height="9" rx="4.5"></rect>\n        <filter x="-12.5%" y="-27.8%" width="125.0%" height="155.6%" filterUnits="objectBoundingBox" id="').concat(t,'-trackFilter">\n          <feGaussianBlur stdDeviation="1.5" in="SourceAlpha" result="shadowBlurInner1"></feGaussianBlur>\n          <feOffset dx="0" dy="1" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>\n          <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>\n          <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.5 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>\n        </filter>\n      </defs>\n      <g class="thumb-off" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">\n        <g>\n          <use class="track" fill-rule="evenodd" xlink:href="#').concat(t,'-track"></use>\n          <use fill="black" fill-opacity="1" filter="url(#').concat(t,'-trackFilter)" xlink:href="#').concat(t,'-track"></use>\n          <use class="border" stroke-width="1" xlink:href="#').concat(t,'-track"></use>\n          <circle class="thumb" stroke-width="0" cx="8" cy="8" r="6"></circle>\n        </g>\n      </g>\n      <g class="thumb-on" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">\n        <g>\n          <use class="track" fill-rule="evenodd" xlink:href="#').concat(t,'-track"></use>\n          <use fill="black" fill-opacity="1" filter="url(#').concat(t,'-trackFilter)" xlink:href="#').concat(t,'-track"></use>\n          <use class="border" stroke-width="1" xlink:href="#').concat(t,'-track"></use>\n          <circle fill="').concat(e,'" stroke-width="0" cx="16" cy="8" r="6"></circle>\n        </g>\n      </g>\n    </svg>\n  ')},downArrow:function(e,t){return'\n    <div style="height: 100%; width: 100%; background-color: '.concat(e,"; border-right: 1px solid; border-bottom: 1px solid; border-color: ").concat(t,'; border-bottom-right-radius: 3px; transform: rotate(45deg);" />\n    ')}},ud={play:function(){return'<svg id="icon-play" class="cs-icon play-icon" width="14" height="16" viewBox="0 0 14 16" focusable="false">\n    <path d="M1.4 15.4C0.8 15.8 0 15.3 0 14.5L0 1.4C0 0.6 0.8 0.1 1.4 0.5L12.9 7.1C13.5 7.5 13.5 8.4 12.9 8.8L8.0 11.6L1.4 15.4Z" stroke="none" />\n  </svg>'}},fd=function(e){return DS.detection.env.isPerpetual?cd[e]||sd:ud[e]||cd[e]||sd};function dd(e){return dd="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},dd(e)}function hd(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,md(i.key),i)}}function pd(e,t){return pd=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},pd(e,t)}function yd(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,i=vd(e);if(t){var r=vd(this).constructor;n=Reflect.construct(i,arguments,r)}else n=i.apply(this,arguments);return function(e,t){if(t&&("object"===dd(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return bd(e)}(this,n)}}function bd(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function vd(e){return vd=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},vd(e)}function gd(e,t,n){return(t=md(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function md(e){var t=function(e,t){if("object"!==dd(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var i=n.call(e,t||"default");if("object"!==dd(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===dd(t)?t:String(t)}var wd=DS,Sd=wd._,kd=wd.events,Cd=wd.pubSub,Od=wd.detection,Ed=wd.dom.tappedClass,Ld=wd.keyManager,xd=wd.stringTabler,Pd=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&pd(e,t)}(o,e);var t,n,i,r=yd(o);function o(e){var t;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,o),gd(bd(t=r.call(this,e)),"hasTooltip",!0),gd(bd(t),"onTap",(function(e){Ed(t.el)})),gd(bd(t),"onClickBtn",(function(e){1!==t.currTimeline.progress()?t.currTimeline.togglePlayback():Od.theme.isUnified&&t.currTimeline.reset()})),gd(bd(t),"onKeydown",(function(e){var t=e.which;Ld.isSeekKey(t)&&Cd.trigger(kd.player.SEEK,e)})),gd(bd(t),"onPlaybackChanged",(function(){t.updateToggle(),t.updateView()})),Sd.bindAll(bd(t),"onTimelineChanged"),Cd.on(kd.player.TOGGLE_PLAYBACK,t.onClickBtn),Cd.on(kd.playbackControls.TIMELINE_CHANGED,t.onTimelineChanged),Od.deviceView.isMobile&&t.onClick(t.onTap),t.onClick(t.onClickBtn),t.el.addEventListener("keydown",t.onKeydown),t.view.toggle=!0,Od.deviceView.isMobile&&Cd.on(kd.currTimeline.TICK,t.onTick.bind(bd(t))),t}return t=o,(n=[{key:"tooltipKey",get:function(){return this.view.toggle?"acc_pause":"acc_play"}},{key:"teardown",value:function(){Cd.off(kd.currTimeline.TICK,this.onTick.bind(this))}},{key:"onTick",value:function(e){null!=this.circleProgress&&this.circleProgress.setAttribute("d",DS.svgUtils.wheelPath(17,17,17,0,360*e)),this.lastProgress=e}},{key:"updateView",value:function(){this.view.updateHtml(),this.circleProgress=this.el.querySelector(".circle-progress path"),this.onTick(this.lastProgress)}},{key:"updateToggle",value:function(){this.view.toggle="playing"===this.currTimeline.playbackState(),this.view.el.setAttribute("aria-pressed",!this.view.toggle),this.view.el.setAttribute("aria-label",xd.getString(this.view.toggle?"acc_pause":"acc_play"))}},{key:"onTimelineChanged",value:function(e){this.currTimeline=e,this.view.toggle=this.currTimeline.isPlaying(),this.updateView()}}])&&hd(t.prototype,n),i&&hd(t,i),Object.defineProperty(t,"prototype",{writable:!1}),o}(_t),Td="playPause";function jd(e){return jd="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},jd(e)}function _d(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,Md(i.key),i)}}function Dd(e,t){return Dd=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},Dd(e,t)}function Id(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,i=Rd(e);if(t){var r=Rd(this).constructor;n=Reflect.construct(i,arguments,r)}else n=i.apply(this,arguments);return function(e,t){if(t&&("object"===jd(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return Ad(e)}(this,n)}}function Ad(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Rd(e){return Rd=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},Rd(e)}function Bd(e,t,n){return(t=Md(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Md(e){var t=function(e,t){if("object"!==jd(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var i=n.call(e,t||"default");if("object"!==jd(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===jd(t)?t:String(t)}Q.def(Td,Pd,(function(e){var t=de.getColor(e,".cs-brandhighlight-bg","background-color",".cs-base.cs-custom-theme");return{tag:"button",attrs:{id:Td,class:"content-center cs-button",tabindex:-1,"aria-pressed":!1,"aria-label":"".concat(Q.model.getString("acc_play"),"/").concat(Q.model.getString("acc_pause"))},contentStyle:{width:"34px",height:"34px"},overflow:"visible",style:{background:"transparent",border:"none"},updateHook:function(){this.el.setAttribute("aria-pressed",!this.toggle)},html:function(){return"\n      ".concat(this.toggle?fd("pause")():fd("play")(),'\n\n       <svg class="circle-progress" width="34" height="34" viewBox="0 0 34 34">\n        <circle\n          class="circle-progress-well"\n          cx="17" cy="17" r="17"\n          fill="#464646"\n          stroke="none" />\n\n        <path\n          d="M 0 0"\n          transform="rotate(-90 17 17)"\n          fill="').concat(t,'"\n          stroke="none" />\n\n        <circle\n          class="circle-progress-bg"\n          cx="17" cy="17" r="14.5"\n          fill="#31373a"\n          stroke="none" />\n      </svg>\n\n      ')},methods:{updateDomStrings:function(){this.el.setAttribute("aria-label",this.toggle?Q.model.getString("acc_pause"):Q.model.getString("acc_play"))}},calcTextSize:!0,y:"vertical-center",x:function(){return this.left||0},minH:34,minW:34,z:1}}));var Hd=DS,Nd=Hd._,Fd=Hd.events,Vd=Hd.pubSub,Wd=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Dd(e,t)}(o,e);var t,n,i,r=Id(o);function o(e){var t;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,o),Bd(Ad(t=r.call(this,e)),"hasTooltip",!0),Bd(Ad(t),"tooltipKey","acc_replay"),Nd.bindAll(Ad(t),"onClickBtn","onTimelineChanged"),t.onClick(t.onClickBtn),Vd.on(Fd.playbackControls.TIMELINE_CHANGED,t.onTimelineChanged),t}return t=o,(n=[{key:"onClickBtn",value:function(){this.resetTimeline()}},{key:"resetTimeline",value:function(){this.currTimeline.reset()}},{key:"onTimelineChanged",value:function(e){this.currTimeline=e}}])&&_d(t.prototype,n),i&&_d(t,i),Object.defineProperty(t,"prototype",{writable:!1}),o}(_t),Ud=DS.detection.deviceView,zd="reset";function Kd(e){return Kd="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Kd(e)}function Zd(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,Xd(i.key),i)}}function Qd(e,t){return Qd=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},Qd(e,t)}function Gd(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,i=Yd(e);if(t){var r=Yd(this).constructor;n=Reflect.construct(i,arguments,r)}else n=i.apply(this,arguments);return function(e,t){if(t&&("object"===Kd(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return qd(e)}(this,n)}}function qd(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Yd(e){return Yd=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},Yd(e)}function Xd(e){var t=function(e,t){if("object"!==Kd(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var i=n.call(e,t||"default");if("object"!==Kd(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===Kd(t)?t:String(t)}Q.def(zd,Wd,(function(){var e=Q.model;return{tag:"button",ariaStringId:"acc_replay",attrs:{id:zd,class:"content-center cs-button","aria-label":e.getString("acc_replay"),tabindex:-1},style:{background:"transparent",border:"none"},html:fd("replay")(),z:1,xl:function(){return Ud.isPhone?this.parent.w-En+(En-this.w)/2:this.xp},xp:function(){return this.left||0},y:"vertical-center",padRight:10,minH:30,minW:30}}));var $d=DS,Jd=$d.detection,eh=$d.dom,th=$d.pubSub,nh=$d.events,ih={next:"acc_next_visual",prev:"acc_previous_visual",submit:"acc_submit"},rh=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Qd(e,t)}(o,e);var t,n,i,r=Gd(o);function o(e){var t,n,i,a;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,o),t=r.call(this,e),n=qd(t),a=!0,(i=Xd(i="hasTooltip"))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a,Jd.deviceView.isMobile&&t.onClick(t.onTap),t.onClick(t.onClickBtn),t}return t=o,(n=[{key:"tooltipKey",get:function(){return ih[this.view.nameKey]}},{key:"onTap",value:function(e){eh.tappedClass(this.el)}},{key:"onClickBtn",value:function(e){var t=this.view,n=t.enabled,i=t.nameKey;n&&th.trigger(nh.presentation.ON_OBJECT_EVENT,i+"_pressed")}},{key:"onLayoutChange",value:function(e){var t=this,n=this.view.nameKey;this.hasFocus&&(e[n]?window.requestAnimationFrame((function(){return t.onFocus()})):this.onBlur())}}])&&Zd(t.prototype,n),i&&Zd(t,i),Object.defineProperty(t,"prototype",{writable:!1}),o}(_t),oh=DS.detection.deviceView.isPhone,ah={next:"prev",prev:"next",submit:"submit"},lh={next:"acc_next",prev:"acc_previous",submit:"acc_submit"},sh={prev:"previous"},ch=function(e,t){Q.def(e,rh,(function(n){var i=Q.model,r=n!==DS.constants.refs.FRAME;return{tag:"button",ariaStringId:lh[e],attrs:{id:e,class:"cs-button btn ".concat(r?"lightboxed":""),"aria-label":i.getString(lh[e]),tabindex:0},parentAlign:function(){return r?"r":oh?"br":"r"},html:function(){var n=i.buttonOptions[sh[e]||e],r=n.includes("text")&&!oh?'<span data-ref="label" class="text '.concat(i.rtl?"rtl":"",'">').concat(i.getString(e),"</span>"):"",o=n.includes("icon")||oh?'<span class="btn-icon">'.concat(fd(i.rtl?ah[e]:e)(),"</span>"):"";return i.dir(t?[o,r]:[r,o]).join("\n")},calcTextSize:!0,wp:"fit-to-text-w",wl:function(){return oh?this.width||0:this.wp},yp:function(){return(this.parent.h-this.h)/2},yl:function(){return!oh||r?this.yp:this.top||0},xp:function(){return this.left||0},xl:function(){return r||!oh?this.xp:(this.parent.w-this.w)/2},methods:{shortcutActivated:function(){this.visible&&this.viewLogic.onClickBtn()},updateDomStrings:function(){null!=this.viewLogic.labelEl&&(this.viewLogic.labelEl.textContent=i.getString(e))}},overflow:"visible",padLeft:20,padRight:3.5,minW:30,minH:30}}))};function uh(e){return uh="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},uh(e)}function fh(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,bh(i.key),i)}}function dh(e,t){return dh=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},dh(e,t)}function hh(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,i=yh(e);if(t){var r=yh(this).constructor;n=Reflect.construct(i,arguments,r)}else n=i.apply(this,arguments);return function(e,t){if(t&&("object"===uh(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return ph(e)}(this,n)}}function ph(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function yh(e){return yh=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},yh(e)}function bh(e){var t=function(e,t){if("object"!==uh(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var i=n.call(e,t||"default");if("object"!==uh(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===uh(t)?t:String(t)}ch("prev",!0),ch("next",!1),ch("submit",!1);var vh=DS,gh=vh.pubSub,mh=vh.captionsManager,wh=vh.stringTabler,Sh=vh.events.captions,kh=Sh.SHOW_BUTTON,Ch=Sh.HIDE_BUTTON,Oh=Sh.ENABLED,Eh=Sh.ENABLE,Lh=vh.detection.theme.isUnified,xh=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&dh(e,t)}(o,e);var t,n,i,r=hh(o);function o(e){var t,n,i,a;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,o),t=r.call(this,e),n=ph(t),a=!0,(i=bh(i="hasTooltip"))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a,t.onClick(t.onClickCaptions),t.toggle=!1,gh.on(kh,(function(e){return t.onVisibilityChanged(!0)})),gh.on(Ch,(function(e){return t.onVisibilityChanged(!1)})),gh.on(Oh,t.onCaptionsEnabled.bind(ph(t))),t}return t=o,(n=[{key:"tooltipKey",get:function(){return this.toggle?"acc_cc_hide":"acc_cc_show"}},{key:"onVisibilityChanged",value:function(e){this.view.setVisibility(e,!0),this.view.childVisibilityChanged(),this.toggle=mh.isCaptionEnabled(),this.updateBtn()}},{key:"onCaptionsEnabled",value:function(e){this.toggle=e,this.updateBtn()}},{key:"updateBtn",value:function(){var e=this.toggle?"add":"remove";this.view.el.classList[e]("cs-tabs","cs-selected"),this.view.el.setAttribute("aria-pressed",this.toggle),this.view.el.setAttribute("aria-label",wh.getString(this.toggle?"acc_cc_hide":"acc_cc_show")),Lh&&this.view.updateHtml()}},{key:"onClickCaptions",value:function(e){this.toggleCaptions()}},{key:"toggleCaptions",value:function(){this.toggle=!this.toggle,gh.trigger(Eh,this.toggle),this.updateBtn()}},{key:"getViewBox",value:function(){var e=this.view.getBox(),t=(e.h-30)/2;return e.h=30,e.y=e.y+t,e}},{key:"teardown",value:function(){gh.off(kh),gh.off(Ch),gh.off(Oh)}}])&&fh(t.prototype,n),i&&fh(t,i),Object.defineProperty(t,"prototype",{writable:!1}),o}(_t),Ph=DS,Th=Ph.detection.deviceView,jh=(Ph.flagManager,"captions");Q.def(jh,xh,(function(e){var t=Q.model,n=e!==DS.constants.refs.FRAME;return{tag:"button",ariaStringId:"acc_closed_captions",attrs:{id:jh,class:"cs-button btn content-center","aria-label":t.getString("acc_closed_captions"),"aria-pressed":!1,tabindex:0},minW:34,minH:34,y:function(){return n||!Th.isPhone?(this.parent.h-this.h)/2:this.top||0},noUpdate:!0,parentAlign:function(){return n?"l":"lt"},visible:!1,html:function(){var e=null!=this.viewLogic&&this.viewLogic.toggle,t=de.getColor(DS.constants.refs.FRAME,".cs-brandhighlight-bg","background-color",".cs-base.cs-custom-theme");return"\n        <style>\n          #captions.cs-button:after {\n            background: ".concat(e?t:"transparent",";\n          }\n        </style>\n        ").concat(fd("captions")(),"\n        ")},xp:function(){return this.left||0},xl:function(){return Th.isPhone&&!n?(this.parent.w-this.w)/2:this.xp}}}));var _h="title",Dh=DS.detection;Q.def(_h,_t,(function(e){var t,n=Q.model,i=Q.model.frame,r=Q.getNamespace(e),o=r.hamburger,a=r.sidebar,l="right"===a.pos&&!n.rtl,s=i.fontscale/100;t=1.23077*s*13+bi;var c=!Dh.deviceView.isPhone&&n.sidebarOpts.titleEnabled;return{simpleView:!0,id:_h,tag:"h1",attrs:{class:"presentation-title cs-title"},overflow:"visible",x:function(){var e=a.visible?o.w:11,t=n.rtl?this.parent.w-this.w-11-11:11+e;return"right"===a.pos&&o.visible&&(t-=e),t},y:"vertical-center",calcTextSize:!0,w:"fit-to-text-w",h:22,html:function(){return"<div data-ref='label' class=\"presentation-title-text\">".concat(c?n.title.text:"","</div> ").concat(l?'<div class="top-tabs-line" style="height:'.concat(t,'px;"></div>'):"")},methods:{updateDomStrings:function(){c&&(this.viewLogic.labelEl.textContent=n.title.text)}},visible:c}}));function Ih(e){return Ih="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Ih(e)}function Ah(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,(r=i.key,o=void 0,o=function(e,t){if("object"!==Ih(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var i=n.call(e,t||"default");if("object"!==Ih(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(r,"string"),"symbol"===Ih(o)?o:String(o)),i)}var r,o}function Rh(){return Rh="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(e,t,n){var i=function(e,t){for(;!Object.prototype.hasOwnProperty.call(e,t)&&null!==(e=Nh(e)););return e}(e,t);if(i){var r=Object.getOwnPropertyDescriptor(i,t);return r.get?r.get.call(arguments.length<3?e:n):r.value}},Rh.apply(this,arguments)}function Bh(e,t){return Bh=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},Bh(e,t)}function Mh(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,i=Nh(e);if(t){var r=Nh(this).constructor;n=Reflect.construct(i,arguments,r)}else n=i.apply(this,arguments);return function(e,t){if(t&&("object"===Ih(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return Hh(e)}(this,n)}}function Hh(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Nh(e){return Nh=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},Nh(e)}Q.def("sidebarOverlay",_t,(function(){return{simpleView:!0,tag:"button",ariaStringId:"close",attrs:{class:"sidebar-overlay btn","aria-label":Q.model.getString("close"),tabIndex:-1},x:0,y:0,w:function(){return window.innerWidth},h:function(){return window.innerHeight},position:function(){},add:!0}}));var Fh=DS,Vh=Fh._,Wh=Fh.pubSub,Uh=Fh.events,zh=Fh.detection,Kh=Fh.globalEventHelper,Zh=Kh.addWindowListener,Qh=Kh.addDocumentListener,Gh=Fh.dom,qh=Gh.isWithin,Yh=Gh.isInput,Xh=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Bh(e,t)}(o,e);var t,n,i,r=Mh(o);function o(e){var t;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,o),t=r.call(this,e),Vh.bindAll(Hh(t),"onResize","hidePanel","onCheckShouldHide","onLayoutReady"),t.onClickEl(t.iconEl,t.onOpenList);var n=Q.getNamespace(t.view.nameSpace);return t.topEllipsisPanel=n.topEllipsisPanel,Zh("resize",t.onResize),Wh.on(Uh.tabLink.SHOW_PANEL,(function(e){t.currentPanelLink=e,t.onCheckShouldHide({target:e.view.el})})),Wh.on(Uh.topEllipsesPanel.HIDE,t.hidePanel),Wh.on(Uh.frame.LAYOUT_READY,t.onLayoutReady),t.hideEvent=DS.detection.device.isMobile?"touchstart":"mousedown",Qh(t.hideEvent,t.onCheckShouldHide,!0),t.arrowEl.style.display="none",t}return t=o,(n=[{key:"onFocus",value:function(e){var t=DS.dom.getParentWithClass(e.target,"panel-link")||DS.dom.getParentWithClass(e.target,"custom-link");null==t?null==DS.dom.getParentWithClass(e.target,"panel")&&Rh(Nh(o.prototype),"onFocus",this).call(this,e):DS.focusManager.setFocusRectOn(t)}},{key:"onLayoutReady",value:function(e){if(!e.hasCustomLinks){var t=!1;Object.keys(e).forEach((function(n){if(-1!=n.indexOf("Panel")){var i=n.replace("Panel","Link");!e[n]&&e[i]&&(t=!0)}})),this.view.setVisibility(t)}}},{key:"onCheckShouldHide",value:function(e){this.hideOnNextTouch&&(qh(e.target,"top-ellipsis-panel")?zh.deviceView.isPhone&&document.body.classList.contains("nested-panel-shown")&&!qh(e.target,"panel")&&this.hidePanel():this.hidePanel())}},{key:"updateAnimation",value:function(){var e=this;window.requestAnimationFrame((function(){e.topEllipsisPanel.updateTrans()}))}},{key:"showPanel",value:function(){this.topEllipsisPanel.setVisibility(!0),this.hideOnNextTouch=!0,this.topEllipsisPanel.yOff=0,this.updateAnimation(),zh.deviceView.isDesktop&&(DS.flagManager.multiLangSupport||DS.flagManager.aiCourseTranslation?this.arrowEl.style.visibility="visible":this.arrowEl.style.display="block"),Wh.trigger(Uh.topEllipsesPanel.PANEL_SHOWN,this)}},{key:"hidePanel",value:function(){Wh.trigger(Uh.topEllipsesPanel.HIDE_PANEL,this),DS.flagManager.multiLangSupport?this.arrowEl.style.visibility="hidden":this.arrowEl.style.display="none",this.topEllipsisPanel.setVisibility(!1),this.hideOnNextTouch=!1,this.topEllipsisPanel.yOff=-20,this.updateAnimation(),null!=this.currentPanelLink&&(this.currentPanelLink.hidePanel(!0),this.currentPanelLink=null)}},{key:"onResize",value:function(e){this.topEllipsisPanel.visible&&!Yh(document.activeElement)&&this.hidePanel()}},{key:"onOpenList",value:function(e){this.topEllipsisPanel.visible?this.hidePanel():this.showPanel()}}])&&Ah(t.prototype,n),i&&Ah(t,i),Object.defineProperty(t,"prototype",{writable:!1}),o}(_t),$h=Xh,Jh="topEllipsis",ep=DS.detection,tp=ep.deviceView.isPhone,np=(ep.orientation,ep.env);function ip(e){return ip="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},ip(e)}function rp(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,i)}return n}function op(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?rp(Object(n),!0).forEach((function(t){ap(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):rp(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function ap(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==ip(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var i=n.call(e,t||"default");if("object"!==ip(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===ip(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}Q.def(Jh,$h,(function(e){var t=Q.getNamespace(e),n=t.linksRight,i=t.sidebar,r=(Q.model.rtl,de.getColor(e,".cs-topmenu-item.active .cs-panel","border-top-color",".cs-base")),o=np.isFileProtocol?"background-color":"background",a=de.getColor(e,".cs-topmenu-item.active .cs-panel",o);return{tag:"div",attrs:{id:Jh},x:function(){var e=Q.model.rtl,t=0;return tp?"right"===i.pos?0:window.innerWidth-58:(t=e?n.x-58+bi:"right"===i.pos?n.x+n.tabWidths():n.right(),t)},w:58,visible:!1,overflow:"visible",html:'\n      <button\n        class="top-tabs-drop-icon cs-button btn"\n        data-ref="icon"\n        aria-expanded="false"\n        aria-controls="top-ellipsis-panel"\n        aria-label="top_links"\n      >\n        <div></div>\n        <div></div>\n        <div></div>\n      </button>\n\n      '.concat(Vi(a,r),"\n    "),yl:function(){return tp&&null!=this.top?this.top:(this.parent.h-this.h)/2},yp:function(){return(this.parent.h-this.h)/2},h:function(){return this.parent.h/(tp?2:1)}}}));var lp=DS.detection,sp="topEllipsisPanel";function cp(e){return cp="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},cp(e)}function up(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,vp(i.key),i)}}function fp(){return fp="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(e,t,n){var i=function(e,t){for(;!Object.prototype.hasOwnProperty.call(e,t)&&null!==(e=yp(e)););return e}(e,t);if(i){var r=Object.getOwnPropertyDescriptor(i,t);return r.get?r.get.call(arguments.length<3?e:n):r.value}},fp.apply(this,arguments)}function dp(e,t){return dp=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},dp(e,t)}function hp(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,i=yp(e);if(t){var r=yp(this).constructor;n=Reflect.construct(i,arguments,r)}else n=i.apply(this,arguments);return function(e,t){if(t&&("object"===cp(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return pp(e)}(this,n)}}function pp(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function yp(e){return yp=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},yp(e)}function bp(e,t,n){return(t=vp(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function vp(e){var t=function(e,t){if("object"!==cp(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var i=n.call(e,t||"default");if("object"!==cp(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===cp(t)?t:String(t)}Q.def(sp,(function(e){var t=Q.getNamespace(e),n=t.sidebar,i=t.topEllipsis,r=t.topBar,o=lp.env.isFileProtocol?"background-color":"background",a=de.getColor(e,".cs-topmenu-item.active .cs-panel",o),l=document.createElement("style"),s=Q.model.frame;return l.innerHTML="\n    .nested-panel-shown .cs-".concat(s.default_layout," .top-tabs-drop:not(.panel) {\n      box-shadow: none !important;\n      background: transparent;\n      border: transparent !important;\n    }\n    .nested-panel-shown .cs-").concat(s.default_layout," .top-tabs-drop .top-tab {\n      visibility: hidden;\n    }\n  "),document.body.appendChild(l),op(op({attrs:{id:sp,class:"top-ellipsis-panel top-tabs-drop","aria-expanded":!1,"aria-controls":"temp-container",style:{background:a}}},DS.flagManager.multiLangSupport||DS.flagManager.aiCourseTranslation?{visibility:"no-reflow"}:{}),{},{overflow:"visible",visible:!1,yOff:-20,y:function(){return lp.deviceView.isPhone?i.y-vi:r.h-bi},x:function(){for(var e=this.el.querySelectorAll(".panel-links .top-tab"),t=0,i=0;i<e.length;i++){var r=parseFloat(e[i].style.width);r>t&&(t=r)}if(this.maxWidth=t,lp.deviceView.isPhone){var o=lp.env.is360&&lp.orientation.isPortrait?40:0;return"left"===n.pos?15+o:t-20-o}var a=20-t/2,l=this.parent.x+this.parent.parent.x+a,s=l+t,c=window.innerWidth-20;return s>c?a-=s-c:l<10&&(a=10),a},w:null,h:null,html:'\n      <div class="panel-links" data-ref="links">\n      </div>\n    '})}));var gp=DS,mp=gp.dom,wp=mp.addClass,Sp=mp.removeClass,kp=gp.appState,Cp=gp.scaler,Op=gp.shortcutManager,Ep=gp.events,Lp=gp.focusManager,xp=gp.pubSub,Pp=gp.keyManager,Tp=gp.utils.fullScreen,jp=gp.globalEventHelper.addDocumentListener,_p=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&dp(e,t)}(o,e);var t,n,i,r=hp(o);function o(e){var t;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,o),bp(pp(t=r.call(this,e)),"hasTooltip",!0),bp(pp(t),"tooltipKey","acc_settings"),bp(pp(t),"hideTooltipWhenOpen",!0),bp(pp(t),"onToggleBackgroundAudio",(function(){xp.trigger(Ep.backgroundAudio.TOGGLE)})),bp(pp(t),"toggleBackgroundAudio",(function(){t.updateToggle(t.backgroundAudioSwitchEl,DS.courseAudio.enabled())})),bp(pp(t),"onCaptionsEnabled",(function(){t.view.updateHook()})),DS._.bindAll(pp(t),"onAccessibleTextChanged","onZoomModeChanged","onKeyboardShortcutsChanged"),t.onClickEl(t.settingsBtnEl,t.togglePanel),null!=t.shortcutsSwitchEl&&(t.onClickEl(t.shortcutsSwitchEl,t.toggleShortcuts),DS.pubSub.on(Ep.player.ENABLE_KEYBOARD_SHORTCUTS,t.onKeyboardShortcutsChanged)),null!=t.captionsButtonEl&&(t.onClickEl(t.captionsButtonEl,t.toggleCaptions),xp.on(Ep.captions.ENABLED,t.onCaptionsEnabled)),null!=t.resetButtonEl&&t.onClickEl(t.resetButtonEl,t.resetTimeline),null!=t.acctextSwitchEl&&(t.onClickEl(t.acctextSwitchEl,t.toggleAccessibleText),kp.on(Ep.player.ACCESSIBLE_TEXT_CHANGED,t.onAccessibleTextChanged)),null!=t.zoomSwitchEl&&(t.onClickEl(t.zoomSwitchEl,t.toggleZoomMode),xp.on(Ep.window.ZOOM_MODE_CHANGED,t.onZoomModeChanged)),t.handleFullScreenChange=t.handleFullScreenChange.bind(pp(t)),null!=t.fullScreenToggleButtonEl&&(t.onClickEl(t.fullScreenToggleButtonEl,t.toggleFullScreen),t.removeFullScreenListener=Tp.addChangeListener(t.handleFullScreenChange)),null!=t.backgroundAudioSwitchEl&&(t.onClickEl(t.backgroundAudioSwitchEl,t.onToggleBackgroundAudio),xp.on(Ep.backgroundAudio.HAS_TOGGLED,t.toggleBackgroundAudio)),t}return t=o,(n=[{key:"toggleFullScreen",value:function(){kp.toggleFullScreen()}},{key:"handleFullScreenChange",value:function(){Tp.getEl()===kp.getPresoEl()?wp(this.el,"full-screen"):Sp(this.el,"full-screen")}},{key:"togglePanel",value:function(){this.isOpen?this.hidePanel():(this.showPanel(),this.dismissTooltip())}},{key:"showPanel",value:function(){var e=this;this.isOpen=!0,wp(this.el,"open"),this.view.updatePanelPosition(),this.settingsBtnEl.setAttribute("aria-expanded",!0),null!=this.view.updatePanelDepth&&this.view.updatePanelDepth(!0),this.removeShowPanelListener=jp("keydown",(function(t){return e.handleKeyDown(t)}))}},{key:"hidePanel",value:function(){this.isOpen=!1,Sp(this.el,"open"),this.settingsBtnEl.setAttribute("aria-expanded",!1),null!=this.view.updatePanelDepth&&this.view.updatePanelDepth(!1),null!=this.removeShowPanelListener&&this.removeShowPanelListener()}},{key:"handleKeyDown",value:function(e){Pp.isEscapeKey(e.which)&&(this.hidePanel(),this.settingsBtnEl.focus(),Lp.setFocusRectOn(this.settingsBtnEl))}},{key:"onBlur",value:function(e){null!=e&&this.isOpen&&!this.el.contains(e.relatedTarget)&&(this.hidePanel(),fp(yp(o.prototype),"onBlur",this).call(this))}},{key:"updateToggle",value:function(e,t){t?(wp(e,"toggle-on"),Sp(e,"toggle-off")):(wp(e,"toggle-off"),Sp(e,"toggle-on")),e.querySelector("button").setAttribute("aria-checked",t)}},{key:"onAccessibleTextChanged",value:function(e){this.accTextOn=e,this.updateToggle(this.acctextSwitchEl,this.accTextOn)}},{key:"onKeyboardShortcutsChanged",value:function(e){this.updateToggle(this.shortcutsSwitchEl,e)}},{key:"toggleAccessibleText",value:function(){this.accTextOn=!this.accTextOn,this.updateToggle(this.acctextSwitchEl,this.accTextOn),kp.onToggleAccessibleText(this.accTextOn)}},{key:"onZoomModeChanged",value:function(){this.updateToggle(this.zoomSwitchEl,Cp.zoomMode)}},{key:"toggleZoomMode",value:function(){Cp.enableZoomMode(!Cp.zoomMode),this.updateToggle(this.zoomSwitchEl,Cp.zoomMode),Lp.setFocusRectOn(this.zoomSwitchEl.querySelector("button"))}},{key:"toggleShortcuts",value:function(){Op.enableShortcuts(!Op.enabled),this.updateToggle(this.shortcutsSwitchEl,Op.enabled)}},{key:"toggleCaptions",value:function(){Q.getNamespace("_frame").captions.viewLogic.toggleCaptions(),this.view.updateHook()}},{key:"resetTimeline",value:function(){Q.getNamespace("_frame").reset.viewLogic.resetTimeline()}},{key:"teardown",value:function(){kp.off(Ep.player.ACCESSIBLE_TEXT_CHANGED,this.onAccessibleTextChanged),xp.off(Ep.window.ZOOM_MODE_CHANGED,this.onZoomModeChanged),xp.off(Ep.captions.ENABLED,this.onCaptionsEnabled),xp.off(Ep.backgroundAudio.HAS_TOGGLED,this.toggleBackgroundAudio),null!=this.removeFullScreenListener&&this.removeFullScreenListener()}},{key:"onFocus",value:function(e){var t=e.target;if(this.el.contains(t))return Lp.setFocusRectOn(t),this.onHoverIn(e),this.isFocused=!1,!1}}])&&up(t.prototype,n),i&&up(t,i),Object.defineProperty(t,"prototype",{writable:!1}),o}(_t);function Dp(e){return function(e){if(Array.isArray(e))return Ip(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"==typeof e)return Ip(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Ip(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Ip(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,i=new Array(t);n<t;n++)i[n]=e[n];return i}var Ap=DS,Rp=Ap.dom,Bp=Rp.addClass,Mp=Rp.removeClass,Hp=Ap.appState,Np=Ap.utils,Fp=Np.pxify,Vp=Np.getPath,Wp=Ap.detection.deviceView,Up=Wp.isPhone,zp=Wp.isTablet,Kp="settings";function Zp(e){return Zp="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Zp(e)}function Qp(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,(r=i.key,o=void 0,o=function(e,t){if("object"!==Zp(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var i=n.call(e,t||"default");if("object"!==Zp(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(r,"string"),"symbol"===Zp(o)?o:String(o)),i)}var r,o}function Gp(e,t){return Gp=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},Gp(e,t)}function qp(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,i=Yp(e);if(t){var r=Yp(this).constructor;n=Reflect.construct(i,arguments,r)}else n=i.apply(this,arguments);return function(e,t){if(t&&("object"===Zp(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,n)}}function Yp(e){return Yp=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},Yp(e)}Q.def(Kp,_p,(function(e){var t=Q.model,n=(Q.model.rtl,de.getColor(e,".cs-topmenu-item.active .cs-panel","background-color")),i=de.getColor(e,".cs-topmenu-item.active .cs-panel","border-top-color",".cs-base"),r=de.getColor(e,".cs-brandhighlight-bg","background-color",".cs-base.cs-custom-theme"),o=de.getColor(e,".cs-brandhighlight-secondary-bg","background-color",".cs-base.cs-custom-theme"),a=Q.getNamespace(e),l=a.bottomBar,s=a.captions;return{noUpdate:!0,attrs:{id:Kp,class:"cs-settings"},overflow:"visible",w:30,h:function(){return 30},xp:function(){return this.left||0},xl:function(){return Up?(this.parent.w-this.w)/2:this.xp},y:function(){return zp?(this.parent.h-this.h)/2:this.top||0},methods:{getControlConfigs:function(){return[].concat(Dp(DS.frameModel.hasModernText?[{name:"acctext",labelId:"accessible_text",isOn:function(){return Hp.accessibleTextOn()}}]:[]),Dp(null!=DS.courseAudio&&DS.courseAudio.hasAudio()?[{name:"backgroundAudio",labelId:"background_audio",isOn:function(){return DS.courseAudio.enabled}}]:[]))},getToggleControls:function(){return this.getControlConfigs().map((function(e){var n=e.name,i=e.labelId,r=(0,e.isOn)();return'\n            <div class="switch '.concat(r?"toggle-on":"toggle-off",'" data-ref="').concat(n,'Switch">\n              <label class="switch-label" id="').concat(n,'-label" data-label-id="').concat(i,'">\n                ').concat(t.getString(i),'\n              </label>\n              <button class="switch-toggle" id="').concat(n,'-switch" tabindex="0" role="switch" aria-checked="').concat(r,'" aria-labelledby="').concat(n,'-label">\n                ').concat(fd("track")(o,n),"\n              </button>\n            </div>\n          ")})).join("")},getIconConfigs:function(){return[{name:"captions",labelId:"acc_closed_captions",getIcon:function(){return"\n                <style>\n                  .captions-off #settings-captions:after {\n                    background: transparent;\n                  }\n                  .captions-on #settings-captions:after {\n                    background: ".concat(r,';\n                  }\n                </style>\n                <div id="settings-captions">').concat(fd("captions")(),"</div>\n                ")}},{name:"fullScreenToggle",labelId:"acc_enter_fullscreen",getIcon:function(){return'\n                <div id="settings-fullscreen">'.concat(fd("enterFullScreen")()).concat(fd("exitFullScreen")(),"</div>\n                ")}},{name:"reset",labelId:"acc_replay",getIcon:fd("replay")}]},getIconControls:function(){return this.getIconConfigs().map((function(e){var n=e.name,i=e.labelId,r=e.getIcon;return'\n            <button id="'.concat(n,'-overflow" class="cs-button overflow-button" tabindex="0" aria-label="').concat(t.getString(i),'" data-ref="').concat(n,'Button">\n              ').concat(r(),"\n            </button>\n          ")})).join("")},updatePanelPosition:function(){if(null!=l){var e=this.children.settingsPanel.el;e.style.left=0;var t=e.getBoundingClientRect(),n=l.el.getBoundingClientRect(),i=0;t.right>n.right&&(i=n.right-(t.right+On)),t.left<n.left&&(i=n.left-t.left+On),e.style.left=Fp(i),this.children.settingsPanelArrow.el.style.transform="translateX(".concat(Fp(0-i),")")}},updateDomStrings:function(){this.el.querySelectorAll("*[data-label-id]").forEach((function(e){e.textContent=t.getString(e.dataset.labelId)})),this.el.setAttribute("aria-label",t.getString("acc_settings"))}},updateHook:function(){var e=this;window.requestAnimationFrame((function(){return e.updatePanelPosition()})),Vp(s,"viewLogic.toggle")?(Bp(this.el,"captions-on"),Mp(this.el,"captions-off")):(Bp(this.el,"captions-off"),Mp(this.el,"captions-on")),this.setVisibility(0!==this.getControlConfigs().length)},html:function(){var e='\n        <button data-ref="settingsBtn" aria-expanded="false" class="cs-button" aria-label="'.concat(t.getString("acc_settings"),'" tabIndex="0">\n          ').concat(fd("settings")(),'\n        </button>\n        <div data-ref="settingsPanel" class="settings-panel" tabindex="-1">\n          <div class="icon-buttons">\n            ').concat(this.getIconControls(),'\n          </div>\n          <div class="toggle-buttons">\n            ').concat(this.getToggleControls(),'\n          </div>\n          <div class="panel-down-arrow" style="height: 18px; width: 18px; position: absolute;">\n            <div data-ref="settingsPanelArrow" style="height: 100%; width: 100%;">\n              ').concat(fd("downArrow")(n,i),"\n            </div>\n          </div>\n        </div>\n        ");return e}}}));var Xp=DS.appState,$p=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Gp(e,t)}(o,e);var t,n,i,r=qp(o);function o(e){var t;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,o),(t=r.call(this,e)).selectBtnEl.addEventListener("change",(function(e){t.handleSpeedChanged(e)})),t}return t=o,(n=[{key:"handleSpeedChanged",value:function(e){var t=Number(e.target.value);Xp.setPlaybackSpeed(t),this.view.updateSelected(t)}}])&&Qp(t.prototype,n),i&&Qp(t,i),Object.defineProperty(t,"prototype",{writable:!1}),o}(_t),Jp=DS.detection,ey=Jp.deviceView,ty=Jp.device,ny=Jp.os,iy="playbackSpeed",ry=String.fromCharCode(10003),oy=String.fromCharCode(160);Q.def(iy,$p,(function(e){var t=Q.model,n=de.getColor(e,".cs-button .cs-icon","fill");return{noUpdate:!0,attrs:{id:iy,class:"btn cs-button cs-settings"},overflow:"visible",w:30,parentAlign:"l",h:function(){return 30},xl:function(){return ey.isPhone?this.parent.w-En+(En-this.w)/2:this.xp},xp:function(){return this.left||0},y:"vertical-center",methods:{updateSelected:function(e){ty.isTablet&&ny.isAndroid&&this.el.querySelectorAll("option").forEach((function(t){var n=Number(t.value)===e?ry:oy;t.label="".concat(n," ").concat(t.label.substr(1))}))},getSpeedOptions:function(){var e=[{label:"2",value:2},{label:"1.75",value:1.75},{label:"1.5",value:1.5},{label:"1.25",value:1.25},{label:Q.model.getString("playback_speed_normal"),value:1,ref:"Normal"},{label:"0.75",value:.75},{label:"0.5",value:.5},{label:"0.25",value:.25}],t=DS.appState.getPlaybackSpeed();return e.map((function(e,n){var i=e.label,r=e.value,o=e.ref,a="";null!=o&&(a='data-ref="label'.concat(o,'"'));var l=r===t,s="";return DS.presentation.isPreview()&&(i="".concat(oy).concat(i).concat(oy),s='style="font-size: initial; color: black;"'),ty.isTablet&&ny.isAndroid&&(i=l?"".concat(ry," ").concat(i):"".concat(oy," ").concat(i)),"\n            <option ".concat(s,'\n              value="').concat(r,'" \n              data-index="').concat(n,'"\n              ').concat(l?" selected":"","\n              ").concat(a,"\n            >").concat(i,"</option>\n          ")})).join("")},updateDomStrings:function(){this.viewLogic.labelNormalEl.textContent=t.getString("playback_speed_normal")}},html:function(){var e='\n        <select \n          class="cs-button mobile-button-select" \n          style="background-image: '.concat(function(e,t){var n=fd(e)(),i=document.createElement("div");i.innerHTML=n;var r=i.querySelector("svg");r.hasAttribute("xmlns")||r.setAttribute("xmlns","http://www.w3.org/2000/svg"),r.style.fill=t,r.style.stroke=t;var o=encodeURIComponent(r.outerHTML.replace("\n"," "));return"url( 'data:image/svg+xml,".concat(o,"' )")}("playbackSpeed",n),'" \n          tabindex="0"\n          aria-label="').concat(t.getString("acc_playback_speed"),'"\n          data-ref="selectBtn"\n        >\n          ').concat(this.getSpeedOptions(),"\n        </select>\n      ");return e}}}));var ay=function(){return"no icon"},ly={hamburger:function(){return'<svg class="cs-icon hamburger-icon" width="40" height="36" viewBox="0 0 40 36" focusable="false">\n    <g id="icon-menu-mobile">\n    <rect width="40" height="4"></rect>\n    <rect y="16" width="40" height="4"></rect>\n    <rect y="32" width="40" height="4"></rect>\n    </g>\n  </svg>'},play:function(){return'<svg class="cs-icon play-icon" width="13" height="15" viewBox="0 0 13 15" focusable="false">\n    <path id="icon-play" d="M 13 7.15 L 2 0 2 14.3 13 7.15 Z"/>\n  </svg>'},next:function(){return'<svg className="cs-icon pause-icon" width="40" height="36" viewBox="0 0 40 36" focusable="false">\n    <path transform="translate(0, -5)" d="M39.414,16.587L22.826,0L20,2.826L33.175,16H0v4h33.175L20,33.174L22.826,36l16.588-16.587C40.195,18.633,40.195,17.368,39.414,16.587z"/>\n  </svg>'},prev:function(){return'<svg class="cs-icon prev-mobile-icon" width="40" height="36" viewBox="0 0 40 36" focusable="false">\n    <path transform="translate(0, -5)" d="M0.586,19.413L17.174,36L20,33.174L6.825,20H40v-4H6.825L20,2.826L17.174,0L0.586,16.587C-0.195,17.367-0.195,18.632,0.586,19.413z"/>\n  </svg>'},pause:function(){return'<svg class="cs-icon pause-icon" width="12" height="14" viewBox="0 0 12 14" focusable="false">\n    <g id="icon-pause">\n      <rect x="0" width="4" height="14"/>\n      <rect x="8" width="4" height="14"/>\n    </g>\n  </svg>'},submit:function(){return'<svg class="cs-icon submit-icon" width="40" height="30" viewBox="0 0 40 30" focusable="false">\n    <path transform="translate(0, -5)" d="M12.685,30c-0.538,0-1.053-0.218-1.429-0.604L0,17.832l2.857-2.8l9.827,10.097L37.143,0L40,2.8L14.114,29.396C13.738,29.782,13.222,30,12.685,30z"/>\n  </svg>'},close:function(){return'\n    <svg class="cs-icon icon-close" width="36" height="36" viewBox="0 0 36 36" focusable="false">\n      <polygon points="36,2.826 33.174,0 18,15.174 2.826,0 0,2.826 15.174,18 0,33.174 2.826,36 18,20.826 33.174,36 36,33.174 20.826,18" />\n    </svg>'},captions:function(e){return'<svg class="cs-icon caption-icon" width="22px" height="20px" viewBox="0 0 22 20" focusable="false">\n    <g stroke="none" stroke-width="1" fill-rule="evenodd" focusable="false">\n      <g>\n        <path d="M14.8517422,14 L20.008845,14 C21.1103261,14 22,13.1019465 22,11.9941413 L22,2.00585866 C22,0.897060126 21.1085295,0 20.008845,0 L1.991155,0 C0.889673948,0 0,0.898053512 0,2.00585866 L0,11.9941413 C0,13.1029399 0.891470458,14 1.991155,14 L8.09084766,14 L11.4712949,17.3804472 L14.8517422,14 Z M3,4 L13,4 L13,6 L3,6 L3,4 Z M14,4 L19,4 L19,6 L14,6 L14,4 Z M19,8 L8,8 L8,10 L19,10 L19,8 Z M7,8 L3,8 L3,10 L7,10 L7,8 Z"></path>\n      </g>\n    </g>\n  </svg>'},disableOrientation:function(){return'<svg viewBox="0 0 161 135">\n    <g stroke="none" stroke-width="1" fill="#fff" fill-rule="evenodd">\n      <path d="M59,31.9948589 C59,30.340844 60.3408574,29 62.0069809,29 L99.9930191,29 C101.653729,29 103,30.3364792 103,31.9948589 L103,103.005141 C103,104.659156 101.659143,106 99.9930191,106 L62.0069809,106 C60.3462712,106 59,104.663521 59,103.005141 L59,31.9948589 Z M61,36 L101,36 L101,96 L61,96 L61,36 Z M81,104 C82.6568542,104 84,102.656854 84,101 C84,99.3431458 82.6568542,98 81,98 C79.3431458,98 78,99.3431458 78,101 C78,102.656854 79.3431458,104 81,104 Z M76,32.5 C76,32.2238576 76.2276528,32 76.5096495,32 L85.4903505,32 C85.7718221,32 86,32.2319336 86,32.5 C86,32.7761424 85.7723472,33 85.4903505,33 L76.5096495,33 C76.2281779,33 76,32.7680664 76,32.5 Z"></path>\n      <path d="M144.276039,68.4976037 C143.65768,83.6270348 137.530567,98.6224671 125.961909,110.191125 C101.576936,134.576098 62.1020027,134.704192 37.8006658,110.402855 L37.8275751,110.429765 L33.4090737,114.848266 L33.3821643,114.821357 C60.1400795,141.579272 103.595566,141.480117 130.445572,114.630111 C143.247134,101.828549 149.95913,85.2399018 150.581333,68.4976037 L161.373625,68.4976037 L147.23149,54.3554681 L133.089354,68.4976037 L144.276049,68.4976037 Z"></path>\n      <path d="M17.2900541,66.5559885 C17.8833587,51.3895735 24.012088,36.3498513 35.6085461,24.7533932 C59.9935191,0.36842015 99.4684528,0.240325436 123.76979,24.5416624 L123.74288,24.514753 L128.161382,20.0962516 L128.188291,20.1231609 C101.430376,-6.63475424 57.9748898,-6.5355989 31.1248839,20.314407 C18.2955218,33.1437691 11.582203,49.7766814 10.9851551,66.5559885 L0.259994507,66.5559885 L14.4021301,80.6981242 L28.5442658,66.5559885 L17.2900541,66.5559885 Z"></path>\n    </g>\n  </svg>'},search:function(){return'<svg class="cs-icon search-icon" width="22" height="22" viewBox="0 0 40 40" focusable="false">\n    <g id="icon-search" transform="translate(4, 0)">\n      <path d="M14.1378906,27.4473684 C21.6653507,27.4473684 27.7757813,21.4196023 27.7757813,13.9736842 C27.7757813,6.52776609 21.6653507,0.5 14.1378906,0.5 C6.61043053,0.5 0.5,6.52776609 0.5,13.9736842 C0.5,21.4196023 6.61043053,27.4473684 14.1378906,27.4473684 L14.1378906,27.4473684 Z M14.1378906,24.4473684 C8.25820695,24.4473684 3.5,19.7535325 3.5,13.9736842 C3.5,8.19383595 8.25820695,3.5 14.1378906,3.5 C20.0175743,3.5 24.7757813,8.19383595 24.7757813,13.9736842 C24.7757813,19.7535325 20.0175743,24.4473684 14.1378906,24.4473684 L14.1378906,24.4473684 Z" ></path>\n      <path d="M20.887408,24.4494377 L31.4348235,34.8541634 L32.5026823,35.9075758 L34.609507,33.7718582 L33.5416482,32.7184459 L22.9942327,22.3137202 L21.9263739,21.2603078 L19.8195492,23.3960254 L20.887408,24.4494377 L20.887408,24.4494377 Z" ></path>\n    </g>\n  </svg>'},enterFullScreen:function(){return'<svg class="cs-icon enter-fullscreen-icon" width="25" height="25" viewBox="0 0 32 32" focusable="false">\n    <g>\n      <path d="M0,0 L12,0 L12,3 L3,3 L3,12 L0,12 L0,0 Z"/>  \n      <path d="M20,0 L32,0 L32,12, L29,12, L29,3, L20,3, L20,0 Z"/>\n      <path d="M0,20 L3,20 L3,29, L12,29, L12,32, L0,32, L0,20 Z"/>\n      <path d="M29,20 L32,20 L32,32, L20,32, L20,29, L29,29, L29,20 Z"/>\n    </g>\n  </svg>'},settings:function(){return'\n    <svg class="cs-icon" data-ref="settings" width="16px" height="16px" viewBox="0 0 16 16" focusable="false">\n      <path d="M8.94,0 C9.82,0 10.55,0.62 10.63,1.45 L10.73,2.36 C11.1,2.52 11.45,2.71 11.78,2.94 L12.66,2.56 C13.46,2.22 14.39,2.5 14.83,3.23 L15.77,4.77 C16.21,5.5 16,6.4 15.29,6.9 L14.51,7.42 C14.54,8.19 14.53,8.38 14.51,8.58 L15.29,9.11 C16,9.6 16.21,10.51 15.77,11.23 L14.83,12.77 C14.39,13.49 13.46,13.78 12.66,13.44 L11.78,13.06 C11.45,13.29 11.1,13.48 10.73,13.64 L10.63,14.55 C10.55,15.38 9.82,16 8.94,16 L7.06,16 C6.18,16 5.45,15.38 5.37,14.55 L5.27,13.64 C4.9,13.48 4.55,13.29 4.22,13.06 L3.34,13.44 C2.54,13.78 1.61,13.5 1.17,12.77 L0.23,11.23 C-0.21,10.51 0,9.6 0.71,9.11 L1.49,8.58 C1.46,7.81 1.47,7.62 1.49,7.42 L0.71,6.89 C0,6.40 -0.21,5.49 0.23,4.77 L1.17,3.23 C1.61,2.51 2.54,2.22 3.34,2.56 L4.22,2.94 C4.55,2.71 4.9,2.52 5.27,2.36 L5.37,1.45 C5.45,0.62 6.18,0 7.06,0 Z M7.96,4.53 C5.91,4.53 4.25,6.11 4.25,8.06 C4.25,10.01 5.91,11.59 7.96,11.59 C10.02,11.59 11.68,10.01 11.68,8.06 C11.68,6.11 10.02,4.53 7.96,4.53 Z"></path>\n    </svg>\n    '},track:function(e){return'\n      <svg xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="16px" viewBox="0 0 24 16" focusable="false">\n        <defs>\n            <rect id="'.concat(e,'-track" x="2" y="3.5" width="20" height="9" rx="4.5"></rect>\n            <filter x="-12.5%" y="-27.8%" width="125.0%" height="155.6%" filterUnits="objectBoundingBox" id="').concat(e,'-trackFilter">\n                <feGaussianBlur stdDeviation="1.5" in="SourceAlpha" result="shadowBlurInner1"></feGaussianBlur>\n                <feOffset dx="0" dy="1" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>\n                <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>\n                <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.5 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>\n            </filter>\n        </defs>\n        <g class="thumb-off" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">\n            <g>\n                <use fill="#303030" fill-rule="evenodd" xlink:href="#').concat(e,'-track"></use>\n                <use fill="black" fill-opacity="1" filter="url(#').concat(e,'-trackFilter)" xlink:href="#').concat(e,'-track"></use>\n                <use stroke="#595959" stroke-width="1" xlink:href="#').concat(e,'-track"></use>\n                <circle fill="#B4B4B4" stroke-width="0" cx="8" cy="8" r="5"></circle>\n            </g>\n        </g>\n        <g class="thumb-on" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">\n            <g>\n                <use fill="#505050" fill-rule="evenodd" xlink:href="#').concat(e,'-track"></use>\n                <use fill="black" fill-opacity="1" filter="url(#').concat(e,'-trackFilter)" xlink:href="#').concat(e,'-track"></use>\n                <use stroke="#595959" stroke-width="1" xlink:href="#').concat(e,'-track"></use>\n                <circle fill="#6EBBEF" stroke-width="0" cx="16" cy="8" r="6"></circle>\n            </g>\n        </g>        \n    </svg>\n    ')},downArrow:function(){return'\n      <svg width="22px" height="11px" viewBox="0 0 22 11" focusable="false">\n        <path d="M 0 0 L 11 11 22 0" fill="#191c1d"/>\n      </svg>\n    '}},sy=DS,cy=(sy.detection,sy.detection),uy=cy.orientation,fy=cy.deviceView,dy="disableOverlay";function hy(e){return hy="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},hy(e)}function py(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,(r=i.key,o=void 0,o=function(e,t){if("object"!==hy(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var i=n.call(e,t||"default");if("object"!==hy(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(r,"string"),"symbol"===hy(o)?o:String(o)),i)}var r,o}function yy(e,t){return yy=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},yy(e,t)}function by(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,i=gy(e);if(t){var r=gy(this).constructor;n=Reflect.construct(i,arguments,r)}else n=i.apply(this,arguments);return function(e,t){if(t&&("object"===hy(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return vy(e)}(this,n)}}function vy(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function gy(e){return gy=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},gy(e)}Q.def(dy,(function(){var e=Q.model,t=e.frame,n=window.globals.parseParams().orientations||null,i=JSON.parse(decodeURIComponent(n))||t.orientations,r=i.phone,o=i.tablet;return{attrs:{id:dy,role:"dialog","aria-modal":!0,"aria-labelledby":"mobile-disabled-orientation-text"},style:{fontSize:"".concat(t.fontscale,"%"),background:"black",color:"white"},x:0,y:0,w:function(){},h:function(){},updateHook:function(){var e=!0;uy.isLandscape?(fy.isPhone&&r.includes("landscape")||fy.isTablet&&o.includes("landscape"))&&(e=!1):uy.isPortrait&&(fy.isPhone&&r.includes("portrait")||fy.isTablet&&o.includes("portrait"))&&(e=!1),e&&!uy.forceHideWarning?document.body.classList.add("show-disabled-overlay"):document.body.classList.remove("show-disabled-overlay"),this.el.setAttribute("aria-hidden",!(e&&!uy.forceHideWarning))},html:function(){return'<div class="mobile-disabled-orientation-overlay-inner">\n        <div class="mobile-disabled-orientation-overlay-icon">\n          '.concat((t="disableOrientation",ly[t]||ay)(),'\n        </div>\n        <div id="mobile-disabled-orientation-text">').concat(e.getString("disabled_orientation"),"</div>\n      </div>");var t},add:!0}}));var my=DS,wy=my.events,Sy=my.pubSub,ky=my._,Cy=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&yy(e,t)}(o,e);var t,n,i,r=by(o);function o(e){var t;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,o),t=r.call(this,e),ky.bindAll(vy(t),"onEnterFullscreen"),t.onClick(t.onClickClose),t.view.setVisibility(!1),Sy.on(wy.presentation.ENTER_FULLSCREEN,t.onEnterFullscreen),t}return t=o,(n=[{key:"onEnterFullscreen",value:function(){this.view.setVisibility(!0)}},{key:"onClickClose",value:function(e){var t=this;DS.utils.fullScreen.exit(),setTimeout((function(){Sy.trigger(wy.presentation.EXIT_FULLSCREEN),t.view.setVisibility(!1)}),300)}}])&&py(t.prototype,n),i&&py(t,i),Object.defineProperty(t,"prototype",{writable:!1}),o}(_t),Oy=DS.constants.MOBILE_UI_SIZE,Ey="fullScreenClose";function Ly(e){return Ly="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Ly(e)}function xy(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,Dy(i.key),i)}}function Py(e,t){return Py=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},Py(e,t)}function Ty(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,i=_y(e);if(t){var r=_y(this).constructor;n=Reflect.construct(i,arguments,r)}else n=i.apply(this,arguments);return function(e,t){if(t&&("object"===Ly(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return jy(e)}(this,n)}}function jy(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function _y(e){return _y=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},_y(e)}function Dy(e){var t=function(e,t){if("object"!==Ly(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var i=n.call(e,t||"default");if("object"!==Ly(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===Ly(t)?t:String(t)}Q.def(Ey,Cy,(function(e){var t=Q.getNamespace(e).sidebar;return{tag:"button",attrs:{id:Ey,class:"cs-button btn fullscreen-close-btn",tabindex:0},overflow:"visible",html:"\n      ".concat(fd("close")(),"\n    "),x:function(){return null==t||"left"===t.pos?window.innerWidth-Oy:3},y:5,w:50,h:Oy,scale:.9,noContent:!0}}));var Iy=DS,Ay=(Iy.events,Iy.pubSub,Iy._,Iy.utils.fullScreen),Ry=Iy.detection,By=(Ry.env.is360,Ry.deviceView.isTablet,function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Py(e,t)}(o,e);var t,n,i,r=Ty(o);function o(e){var t,n,i,a;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,o),t=r.call(this,e),n=jy(t),a=!0,(i=Dy(i="hasTooltip"))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a,t.onClick(t.onClickToggle),t.view.toggle=!1,Ay.addChangeListener((function(){return t.handleFullScreenChange()})),t}return t=o,(n=[{key:"tooltipKey",get:function(){return this.view.toggle?"acc_exit_fullscreen":"acc_enter_fullscreen"}},{key:"getFullScreenEl",value:function(){return document.getElementById(DS.constants.els.PRESO)}},{key:"isFullScreen",value:function(){return Ay.getEl()===this.getFullScreenEl()}},{key:"handleFullScreenChange",value:function(){this.view.toggle=this.isFullScreen(),this.view.updateHtml()}},{key:"onClickToggle",value:function(e){DS.appState.toggleFullScreen()}},{key:"getViewBox",value:function(){var e=this.view.getBox(),t=(e.h-30)/2;return e.h=30,e.y=e.y+t,e}}])&&xy(t.prototype,n),i&&xy(t,i),Object.defineProperty(t,"prototype",{writable:!1}),o}(_t)),My="fullScreenToggle",Hy=DS,Ny=Hy.constants.MOBILE_UI_SIZE,Fy=Hy.detection.deviceView.isPhone;function Vy(e){return Vy="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Vy(e)}function Wy(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,Qy(i.key),i)}}function Uy(e,t){return Uy=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},Uy(e,t)}function zy(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,i=Zy(e);if(t){var r=Zy(this).constructor;n=Reflect.construct(i,arguments,r)}else n=i.apply(this,arguments);return function(e,t){if(t&&("object"===Vy(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return Ky(e)}(this,n)}}function Ky(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Zy(e){return Zy=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},Zy(e)}function Qy(e){var t=function(e,t){if("object"!==Vy(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var i=n.call(e,t||"default");if("object"!==Vy(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===Vy(t)?t:String(t)}Q.def(My,By,(function(e){var t=Q.model;return{tag:"button",attrs:{id:My,class:"cs-button btn fullscreen-open-btn",tabindex:0},wp:"fit-to-text-w",wl:function(){return Fy?this.width||0:this.wp},yp:function(){return(this.parent.h-this.h)/2},yl:function(){return Fy?this.top||0:this.yp},xp:function(){return this.left||0},xl:function(){return Fy?(this.parent.w-this.w)/2:this.xp},html:function(){var e=this.toggle?"acc_exit_fullscreen":"acc_enter_fullscreen";return this.el.setAttribute("aria-label",t.getString(e)),this.toggle?fd("exitFullScreen")():fd("enterFullScreen")()},methods:{updateDomStrings:function(){var e=this.toggle?"acc_exit_fullscreen":"acc_enter_fullscreen";this.el.setAttribute("aria-label",t.getString(e))}},z:1,padLeft:6,padRight:6,minW:30,minH:Ny,noUpdate:!t.frame.chromeless,parentAlign:"br",noContent:!1,x:function(){return this.left||0}}}));var Gy=DS.utils,qy=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Uy(e,t)}(o,e);var t,n,i,r=zy(o);function o(e){var t,n,i,a;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,o),t=r.call(this,e),n=Ky(t),a=function(){var e=Gy.fullScreen.getEl();if(null!=e){if(Q.getNamespace(t.view.nameSpace).slide.el.contains(e)){var n=e.querySelector(".caption-container-host");null!=n?(null==t.origParent&&(t.origParent=t.el.parentElement),n.appendChild(t.el)):DS.flagManager.dropInVideo&&(clearTimeout(t.findHost),t.findHost=window.setTimeout(t.reparent,30))}}else null!=t.origParent&&(t.origParent.appendChild(t.el),t.origParent=null)},(i=Qy(i="reparent"))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a,Gy.fullScreen.addChangeListener((function(){t.handleFullScreenChange()})),t}return t=o,(n=[{key:"handleFullScreenChange",value:function(){DS.flagManager.dropInVideo?(clearTimeout(this.reparentTimeout),this.reparentTimeout=window.setTimeout(this.reparent,200)):window.requestAnimationFrame(this.reparent)}}])&&Wy(t.prototype,n),i&&Wy(t,i),Object.defineProperty(t,"prototype",{writable:!1}),o}(_t),Yy=DS,Xy=Yy.detection,$y=Xy.deviceView.isPhone,Jy=Xy.orientation;Yy.flagManager;Q.def("captionContainer",qy,(function(e){var t=Q.getNamespace(e),n=t.bottomBar,i=t.topBar,r=t.slide,o=Q.model.frame,a=o.fontscale,l=o.captionFontScale,s=o.controlOptions.ccOptions,c=e!==DS.constants.refs.FRAME;return{attrs:{class:"caption-container"},style:{fontSize:"".concat(null!=s?s.size:l||a,"%")},z:2,methods:{beforeUpdateHook:function(){var e=null!=i,t=null!=n,o=$y&&Jy.isLandscape?window.innerHeight-65+5:window.innerHeight-(e?i.h+n.h:0)+($y?10:0);e||t||(o=window.innerHeight),this.dims=c?{x:0,y:0,w:this.parent.w,h:this.parent.h}:{x:r.sidebarXOffset,y:$y&&Jy.isLandscape?0:e?i.h:0,w:window.innerWidth-Q.model.getDockedWidth()-r.sidebarWidthOffset,h:o}}},x:function(){return this.dims.x},y:function(){return this.dims.y},w:function(){return this.dims.w},h:function(){return this.dims.h},add:!0}}));var eb="frameBlocker";function tb(e){return function(e){if(Array.isArray(e))return nb(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"==typeof e)return nb(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return nb(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function nb(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,i=new Array(t);n<t;n++)i[n]=e[n];return i}Q.def(eb,(function(e){var t=Q.getNamespace(e).slide,n=function(){return"".concat(t.x,"px ").concat(t.w,"px ").concat(window.innerWidth-t.x-t.w,"px")},i=function(){return"".concat(t.y,"px ").concat(t.h,"px ").concat(window.innerHeight-t.y-t.h,"px")};return{attrs:{id:eb,class:"frame-blocker-container"},x:0,y:0,z:gi,w:function(){return window.innerWidth},h:function(){return window.innerHeight},style:{"pointer-events":"none","grid-template-columns":n(),"grid-template-rows":i()},visible:!1,visibility:"no-reflow",html:'<div class="frame-blocker-slide"></div><div class="frame-blocker-top"></div><div class="frame-blocker-bottom"></div><div class="frame-blocker-right"></div><div class="frame-blocker-left"></div>',updateHook:function(){var e=this;window.requestAnimationFrame((function(){e.el.style["grid-template-columns"]=n(),e.el.style["grid-template-rows"]=i()}))},methods:{setBackgroundColor:function(e){this.el.childNodes.forEach((function(t){t.style.background=e}))}}}}));var ib=DS.constants.refs.FRAME,rb=DS.detection,ob=rb.deviceView,ab=ob.isPhone,lb=ob.isTablet,sb=rb.env.is360;function cb(e){return cb="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},cb(e)}function ub(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,(r=i.key,o=void 0,o=function(e,t){if("object"!==cb(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var i=n.call(e,t||"default");if("object"!==cb(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(r,"string"),"symbol"===cb(o)?o:String(o)),i)}var r,o}var fb=DS,db=fb._,hb=fb.pubSub,pb=fb.events,yb=function(){function e(t){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.el=t.el,this.nameSpace=t.nameSpace,this.el.classList.add(this.nameSpace),db.bindAll(this,"onSlideTransitionIn","onWindowClosing"),hb.on(pb.slide.ON_TRANSITION_IN,this.onSlideTransitionIn),hb.on(pb.window.CLOSING,this.onWindowClosing)}var t,n,i;return t=e,(n=[{key:"onSlideTransitionIn",value:function(e){e.windowId===this.nameSpace&&document.body.classList.add("showing-".concat(this.nameSpace.toLowerCase()))}},{key:"onWindowClosing",value:function(e){e===this.nameSpace&&document.body.classList.remove("showing-".concat(this.nameSpace.toLowerCase()))}}])&&ub(t.prototype,n),i&&ub(t,i),Object.defineProperty(t,"prototype",{writable:!1}),e}(),bb="lightBoxWrapper";Q.def(bb,yb,(function(e){var t=Q.model,n=t.slideWidth,i=t.slideHeight,r=Q.getNamespace(e).lightBoxBottom;return{attrs:{id:bb,class:"cs-".concat(t.frame.default_layout," fn-").concat(t.frame.default_layout)},style:{transformOrigin:"0px 0px"},overflow:"visible",dimScale:function(){var e=.8*(ut.width-t.getDockedWidth()),r=.8*ut.height;return Math.min(e/n,r/i)},w:function(){return n*this.dimScale},h:function(){return i*this.dimScale+(null!=r?50:0)},x:function(){return(ut.width-t.getDockedWidth()-this.w)/2},y:function(){return(ut.height-this.h)/2},add:!0,html:'<div data-ref="bottomBg" class="bottom-bar-bg cs-base cs-'.concat(t.frame.default_layout,'"></div>')}}));var vb="lightBoxSlide";Q.def(vb,Cn,(function(e){var t=Q.getNamespace(e),n=t.lightBoxBottom,i=t.lightBoxWrapper;return{attrs:{class:"".concat(vb," window-slide ").concat(e,"-slide"),role:"dialog","aria-modal":!0},overflow:"visible",winScale:function(){return i.dimScale},pinchZoomBounds:function(){return{x:0,y:0,width:window.innerWidth,height:window.innerHeight-(null!=n?50:0)}},w:"100%",h:function(){return this.parent.h-(null!=n?50:0)},z:1,add:!0,html:'<div id="slide-label-lightbox" data-ref="label" aria-live="polite"></div><main data-ref="container" class="slide-container" tabindex="-1"></main>',childViews:["captionContainer"],updateHook:function(){this.children.captionContainer.update()}}}));function gb(e){return gb="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},gb(e)}function mb(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,(r=i.key,o=void 0,o=function(e,t){if("object"!==gb(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var i=n.call(e,t||"default");if("object"!==gb(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(r,"string"),"symbol"===gb(o)?o:String(o)),i)}var r,o}function wb(e,t){return wb=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},wb(e,t)}function Sb(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,i=kb(e);if(t){var r=kb(this).constructor;n=Reflect.construct(i,arguments,r)}else n=i.apply(this,arguments);return function(e,t){if(t&&("object"===gb(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,n)}}function kb(e){return kb=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},kb(e)}Q.def("lightBox",(function(e){return{attrs:{class:"lightbox"},x:0,y:0,z:1,w:"100%",h:"100%",overflow:"visible"}}));var Cb=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&wb(e,t)}(o,e);var t,n,i,r=Sb(o);function o(e){var t;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,o),(t=r.call(this,e)).onClick(t.onClickClose),t}return t=o,(n=[{key:"onClickClose",value:function(e){var t=this.model.windowId;DS.pubSub.trigger(DS.events.window.CLOSING,t)}}])&&mb(t.prototype,n),i&&mb(t,i),Object.defineProperty(t,"prototype",{writable:!1}),o}(_t),Ob="lightBoxClose";Q.def(Ob,Cb,(function(e){var t={windowId:e};return{tag:"button",ariaStringId:"close",attrs:{id:Ob,class:"lightbox-close-btn-floating",tabindex:0,"z-index":999,"aria-label":DS.stringTabler.getString("close")},style:{overflow:"visible"},html:fd("close"),y:35,x:function(){return window.innerWidth-Q.model.getDockedWidth()-(this.w+35)},model:t,w:20,h:20}}));Q.def("visibleOverlay",(function(){return{attrs:{class:"visible-overlay"},x:0,y:0,w:function(){return window.innerWidth},h:function(){return window.innerHeight},position:"fixed",bgColor:"rgba(0, 0, 0, 0.4)",add:!0}}));var Eb="lightBoxBlocker";Q.def(Eb,(function(e){var t=Q.getNamespace(e).lightBoxWrapper,n=function(){return"".concat(t.x,"px ").concat(t.w,"px ").concat(window.innerWidth-t.x-t.w,"px")},i=function(){return"".concat(t.y,"px ").concat(t.h,"px ").concat(window.innerHeight-t.y-t.h,"px")};return{attrs:{id:Eb,class:"frame-blocker-container"},x:0,y:0,z:gi,w:function(){return window.innerWidth},h:function(){return window.innerHeight},style:{"pointer-events":"none","grid-template-columns":n(),"grid-template-rows":i()},visible:!1,visibility:"no-reflow",html:'<div class="frame-blocker-slide"></div><div class="frame-blocker-top"></div><div class="frame-blocker-bottom"></div><div class="frame-blocker-right"></div><div class="frame-blocker-left"></div>',updateHook:function(){var e=this;window.requestAnimationFrame((function(){e.el.style["grid-template-columns"]=n(),e.el.style["grid-template-rows"]=i()}))},methods:{setBackgroundColor:function(e){this.el.childNodes.forEach((function(t){t.style.background=e}))}}}}));var Lb="LightboxWnd",xb=DS.utils.pxify,Pb="lightBoxBottom";Q.def(Pb,(function(e){return{tag:"nav",attrs:{id:Pb,class:"option-pane"},methods:{getZoomBounds:function(){return Q.getNamespace(e).zoomBounds},getParentBB:function(){return null==this.cachedParentBB&&(this.cachedParentBB=this.parent.getBox(),null!=this.cachedParentBB&&(this.cachedParentBB.left=this.cachedParentBB.x,this.cachedParentBB.right=this.cachedParentBB.x+this.cachedParentBB.w,this.cachedParentBB.top=this.cachedParentBB.y,this.cachedParentBB.bottom=this.cachedParentBB.y+this.cachedParentBB.h)),this.cachedParentBB},shouldZoom:function(){return null!=this.getParentBB()&&null!=this.getZoomBounds()}},x:function(){if(this.shouldZoom()){var e=this.getZoomBounds(),t=this.getParentBB();return Math.floor(Math.max(e.left,0)-t.left)}return 0},y:function(){if(this.shouldZoom()){var e=this.getZoomBounds(),t=this.getParentBB();return t.h+(Math.min(e.bottom,window.innerHeight-50)-t.bottom)}return this.parent.y+this.parent.h-this.h},w:function(){if(this.shouldZoom()){var e=this.getZoomBounds();return Math.ceil(Math.min(e.right,window.innerWidth)-Math.max(e.left,0))}return this.parent.w},h:50,updateHook:function(){if(this.hasAllChildren()){this.flowChildren({alignChild:!0,bounds:{t:0,b:this.h,l:0,r:this.w},pad:4});var t=Q.getNamespace(e).wrapper.children.bottomBg.el;Object.assign(t.style,{left:xb(this.x),top:xb(this.y),width:xb(this.w),height:xb(this.h)}),this.cachedParentBB=null}},childVisibilityChangedHook:function(){this.update()}}}));var Tb="lightBoxControlsBlocker";Q.def(Tb,(function(e){var t=Q.getNamespace(e),n=t.lightBoxWrapper,i=t.lightBoxBottom,r=function(){return"".concat(n.x,"px ").concat(n.w,"px ").concat(window.innerWidth-n.x-n.w,"px")},o=function(){return"".concat(n.y,"px ").concat(n.h-i.h,"px ").concat(window.innerHeight-n.y-n.h+i.h,"px")};return{attrs:{id:Tb,class:"frame-blocker-container"},x:0,y:0,z:gi,w:function(){return window.innerWidth},h:function(){return window.innerHeight},style:{"pointer-events":"none","grid-template-columns":r(),"grid-template-rows":o()},visible:!1,visibility:"no-reflow",html:'<div class="frame-blocker-slide"></div><div class="frame-blocker-top"></div><div class="frame-blocker-bottom"></div><div class="frame-blocker-right"></div><div class="frame-blocker-left"></div>',updateHook:function(){var e=this;window.requestAnimationFrame((function(){e.el.style["grid-template-columns"]=r(),e.el.style["grid-template-rows"]=o()}))},methods:{setBackgroundColor:function(e){this.el.childNodes.forEach((function(t){t.style.background=e}))}}}}));var jb="LightboxControlsWnd",_b="printWrapper";Q.def(_b,(function(e){return{attrs:{id:_b,class:"print-window"},w:0,h:0,style:{overflow:"visible",transformOrigin:"0 0",background:"transparent"},scale:1,x:0,y:0,add:!0}}));var Db=DS.constants.printSettings,Ib="printSlide";Q.def(Ib,Cn,(function(e){var t=Q.model.slideWidth;return{attrs:{id:Ib,class:"cs-window window-slide ".concat(e,"-slide"),tabindex:-1},style:{overflow:"visible",transformOrigin:"0 0",background:"transparent",display:"none"},origin:"0 0",winScale:function(){return this.w/t},w:function(){var e=DS.detection.os,t=e.isAndroid,n=e.isIOS;return t?Db.android.pageW:n?Db.ios.pageW:Db.desktop.pageW},h:0,x:0,y:0,z:1,html:'<div data-ref="container" class="slide-container"></div>'}}));var Ab="PrintWindow",Rb="messageWindowWrapper";Q.def(Rb,(function(e){var t=Q.model,n=t.preso.display().windows().find({id:e}),i=Q.getNamespace(DS.constants.refs.FRAME),r=i.slide,o=i.wrapper;return{attrs:{id:Rb,class:"cs-base cs-".concat(t.frame.default_layout," fn-").concat(t.frame.default_layout)},w:.8*n.get("width"),h:n.get("height"),style:{overflow:"visible",transformOrigin:"0 0",background:"transparent"},scale:function(){return o.scale},x:function(){var e=o.scale;return o.x+(r.x+(r.w-this.w)/2)*e},y:function(){var e=o.scale;return o.y+(r.y+(r.h-this.h)/2)*e},add:!0}}));var Bb="messageWindowSlide";Q.def(Bb,Cn,(function(){return{attrs:{id:Bb,class:"cs-window","aria-labelledby":"slide-label-message",role:"alertdialog","aria-modal":!0,tabindex:-1},origin:"0 0",w:"100%",h:"100%",x:0,y:0,z:1,html:'<div id="slide-label-message" data-ref="label"></div><div data-ref="container" class="slide-container"></div>'}}));Q.def("overlay",(function(){return{attrs:{class:"overlay overlay-message"},x:0,y:0,w:function(){return window.innerWidth},h:function(){return window.innerHeight},position:"fixed",add:!0}}));var Mb,Hb="MessageWnd";function Nb(e){return Nb="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Nb(e)}function Fb(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==Nb(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var i=n.call(e,t||"default");if("object"!==Nb(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===Nb(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}!function(e,t){Je(e),function(e){var t=window.globalProvideData;if(window.globalProvideData=function(e,n){"frame"===e&&(null!=(ve=JSON.parse(n)).localization&&DS.flagManager.multiLangSupport?(DS.localizationManager.registerFrameLanguageData(ve.localization),DS.localizationManager.loadFrameData().then((function(e){ge=e,et()}))):et(),window.globalProvideData=t)},ze.on(_e,(function(e){new Ee,be=new f(ve,ge,e),Q.setModel(be),Qe.initialize(be),Ge.initialize((function(e){return be.getString(e)})),DS.frameModel=be,DS.views=Q,(DS.frameModel.frame.chromeless||DS.detection.env.hideFrame)&&qe.addClass(document.body,"chromeless"),ze.trigger(Ae)})),window.globals.useJson){var n=e.replace(".js",".json"),i=new XMLHttpRequest;return i.overrideMimeType("application/json"),i.onreadystatechange=function(){4===i.readyState&&200===i.status&&window.globalProvideData("frame",i.responseText.replace(/\\'/g,"'").replace(/\\"/g,'"'))},i.open("GET",n,!0),void i.send(null)}DS.loadScript(e)}(t)}((Fb(Mb={},ib,(function(e){var t,n,i=e.topTabsLeft,r=e.topTabsRight,o=e.sidebarOpts,a=o.timeEnabled,l=o.logoEnabled,s=o.html5_logo_url,c=e.frame.controlOptions.controls,u=c.closed_captions,f=c.settings,d=function(){return ab&&sb},h=Q.tree(ib,[{wrapper:["frameBlocker",e.frame.chromeless?"slide":{frame:[e.frame.chromeless?null:"sidebarOverlay","slide"].concat(tb(ab?["floatingSeek"]:[]),[{bottomBar:[{playbackControls:["playPause","seek","reset"]},{miscControls:[].concat(tb(u?["captions"]:[]),["playbackSpeed"],tb(f?["settings"]:[]))},{navControls:[].concat(tb(DS.utils.fullScreen.isSupported()?["fullScreenToggle"]:[]),["prev","next","submit"])}]},{sidebar:[].concat(tb(lb&&l&&s?["logo"]:[]),["hamburger","tabs","sidebarPanels"])},{topBar:["title",{topTabs:["arrowShadow","linksRight"]}].concat(tb(d()?["fullScreenClose"]:[]),[{topEllipsis:["topEllipsisPanel"]}],tb(a?["timer"]:[]))}])},e.frame.chromeless&&lb&&sb?"fullScreenToggle":null,e.frame.chromeless&&d()?"fullScreenClose":null]},"captionContainer"].concat(tb(sb?[]:["disableOverlay"])));t=document.querySelectorAll(".panel > div[id*=content]"),n=it.theme.isUnified?12:0,Array.from(t).forEach((function(e){var t=e.parentNode.id;if("outline-panel"!=t){var i=t.replace("-panel","");new nt(e,i,!1,n)}else{var r=e.querySelector("#outline-content"),o=e.querySelector("#search-results-content");null!=r&&new nt(r,"outline",!1,n),null!=o&&new nt(o,"search",!1,n)}}));var p=function(e){return function(t){return t.name===e}},y=function(e){return!i.some(p(e))&&!r.some(p(e))},b=function(e){return"customlink"===e.name},v=i.some(b)||r.some(b);DS.pubSub.on(DS.events.frameModel.LAYOUT_CHANGED,(function(t,n){if(n===ib){var i=function(e,t){var n=e.sidebarOpts.sidebarEnabled;return n&&(n=ot(e.sidebarTabs,t)),n}(e,t),r=ot([].concat(tb(e.topTabsRight),tb(e.topTabsLeft)),t),o={playPause:t.pauseplay,reset:t.seekbar,seek:t.seekbar,playbackControls:t.pauseplay||t.reset||t.seek,fullScreenToggle:t.fullScreenToggle||lb&&sb,playbackSpeed:t.playbackSpeedControl,next:t.next,prev:t.previous,submit:t.submit,skipnav:i||r,floatingSeek:t.seekbar&&t.pauseplay,volume:t.volume,glossaryTab:t.glossary,glossaryPanel:t.glossary&&y("glossary"),glossaryLink:t.glossary,resourcesTab:t.resources,resourcesPanel:t.resources&&y("resources"),resourcesLink:t.resources,outlineTab:t.outline.enabled,outlinePanel:t.outline.enabled&&y("outline"),outlineLink:t.outline.enabled,transcriptTab:t.transcript,transcriptPanel:t.transcript&&y("transcript"),transcriptLink:t.transcript,sidebar:i,bottomBar:!lb||e.bottomBarOpts.bottomBarEnabled,hasCustomLinks:v};DS.pubSub.trigger(DS.events.frame.LAYOUT_READY,o),Q.resetStates(ib),Q.updateVisibility(o,ib),Q.update(h)}})),DS.pubSub.on(DS.events.navcontrols.CHANGED,(function(e){var t=e.kind,n=e.name,i=e.visible,r=e.enable,o="toggle_window_control_visible"===t||"toggle_window_control"===t;"previous"===n&&(n="prev");var a=Q["enable_window_control"===t||"toggle_window_control"===t?"getTopNameSpace":"getFrameNameSpace"]()[n];null!=a&&(o?"toggle_window_control_visible"===t?(a.setVisibility(!a.visible),a.childVisibilityChanged()):a.setEnabled(!a.enabled):"set_window_control_visible"===t?(a.setVisibility(i),a.childVisibilityChanged()):a.setEnabled(r))}));var g=function(){Q.update(h);var e=Q.getNamespace(ib).sidebarPanels;e&&null!=e.children&&e.updateChildren(!0)};return lb&&DS.pubSub.on(DS.events.utilityWindow.DOCKED,(function(t,n){var i=n.dockedState,r=n.dockedWidth;e.setDocked(t,i,r),g(),DS.pubSub.trigger(DS.events.frame.SCALE);var o=DS.utils.getPath(Q.getNamespace(ib),"hamburger.viewLogic");null!=o&&o.onResize()})),DS.pubSub.on(DS.events.frame.REFLOW,g),{all:g,resize:g}})),Fb(Mb,Lb,(function(e){var t=Q.tree(Lb,["lightBoxBlocker","visibleOverlay",{lightBoxWrapper:["lightBoxSlide","lightBox"]},"lightBoxClose"]),n=Q.getNamespace(Lb);return n.slide=n.lightBoxSlide,n.wrapper=n.lightBoxWrapper,{all:function(){return Q.update(t)},resize:function(){n.isAttached&&Q.update(t)}}})),Fb(Mb,jb,(function(e){var t=Q.tree(jb,["lightBoxControlsBlocker","visibleOverlay",{lightBoxWrapper:["lightBoxSlide",{lightBox:[{lightBoxBottom:["submit","next","prev","captions"]}]}]},"lightBoxClose"]),n=Q.getNamespace(jb);n.slide=n.lightBoxSlide,n.wrapper=n.lightBoxWrapper;var i=function(e,n){if(n===jb){var i={next:e.next,prev:e.previous,submit:e.submit};Q.resetStates(jb),Q.updateVisibility(i,jb),Q.update(t)}};return DS.pubSub.on(DS.events.frameModel.LAYOUT_CHANGED,i),i(e.currControlLayout),{all:function(){return Q.update(t)},resize:function(){n.isAttached&&Q.update(t)},pinchZoom:function(){return n.lightBoxBottom.update()}}})),Fb(Mb,Ab,(function(e){Q.tree(Ab,[{printWrapper:["printSlide"]}]);var t=Q.getNamespace(Ab);return t.slide=t.printSlide,t.wrapper=t.printWrapper,{all:function(){},resize:function(){},pinchZoom:function(){}}})),Fb(Mb,Hb,(function(e){var t=Q.tree(Hb,["overlay",{messageWindowWrapper:["messageWindowSlide"]}]),n=Q.getNamespace(Hb);return n.slide=n.messageWindowSlide,n.wrapper=n.messageWindowWrapper,{all:function(){return Q.update(t)},resize:function(){n.isAttached&&(n.overlay.update(),n.messageWindowWrapper.update())}}})),Mb),"html5/data/js/frame.js")}();
